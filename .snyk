exclude:
  global:
    - .mvn/**

ignore:
  SNYK-JAVA-ORGAPACHEKAFKA-10336719:
    - reason: "Temporarily accepted risk; upgrade planned to 3.9.1"
      expires: "2025-08-31"
      severity: high
      locations:
        - pom.xml > org.apache.kafka:kafka-clients@3.6.1
  SNYK-JAVA-ORGAPACHEKAFKA-10350513:
    - reason: "Temporarily accepted risk; upgrade planned to 3.9.1"
      expires: "2025-08-31"
      severity: high
      locations:
        - pom.xml > org.apache.kafka:kafka-clients@3.6.1
  SNYK-JAVA-ORGAPACHEKAFKA-10350567:
    - reason: "Temporarily accepted risk; upgrade planned to 3.9.1"
      expires: "2025-08-31"
      severity: high
      locations:
        - pom.xml > org.apache.kafka:kafka-clients@3.6.1
