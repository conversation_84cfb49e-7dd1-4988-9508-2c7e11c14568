/*
 * Copyright (c) Jaguar Land Rover Ltd 2023. All rights reserved
 */
package com.jaguarlandrover.vcdp.Utils;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.json.JsonMapper;
import net.mguenther.kafka.junit.*;

import java.time.Duration;
import java.util.List;
import java.util.stream.Stream;

import static org.testcontainers.shaded.org.awaitility.Awaitility.await;

/** A slim wrapper around <a href="https://mguenther.github.io/kafka-junit/">kafka-junit</a>. */
public class CookbookKafkaCluster extends EmbeddedKafkaCluster {

    private static final ObjectMapper OBJECT_MAPPER =
            JsonMapper.builder().build();

    public CookbookKafkaCluster() {
        super(EmbeddedKafkaClusterConfig.defaultClusterConfig());

        this.start();
    }

    /**
     * Creates a topic with the given name and synchronously writes all data from the given stream
     * to that topic.
     *
     * @param args topic to create
     */
    public void createTopic(String args[] ) {
        for(String topic: args){
            createTopic(TopicConfig.withName(topic));
        }
    }

    /**
     * Write the provided data to the topic provided
     *
     * @param topic topic where the data to be written
     * @param topicData data to be sent to the topic
     * @param <EVENT> event type
     */
    public <EVENT>  void produceRecord(String topic, Stream<EVENT> topicData){
        topicData.forEach(record -> sendEventAsJSON(topic, record));
    }

    public List<String> getTopicRecords(String topic, int numRecords) {
        return getTopicRecords(topic, 0, numRecords);
    }

    public List<String> getTopicRecords(String topic, int offset, int numRecords) {
        final ReadKeyValues.ReadKeyValuesBuilder<String, String> from =
                ReadKeyValues.from(topic).seekTo(0, offset).withLimit(numRecords);

        try {
            return readValues(from);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            throw new RuntimeException(e);
        }
    }

    /**
     * Sends one JSON-encoded event to the topic and sleeps for 100ms.
     *
     * @param event An event to send to the topic.
     */
    private <EVENT> void sendEventAsJSON(String topic, EVENT event) {
        try {
            final SendValues<String> sendRequest =
                    SendValues.to(topic, OBJECT_MAPPER.writeValueAsString(event)).build();
            this.send(sendRequest);
            await().atMost(Duration.ofMillis(100));
        } catch (InterruptedException | JsonProcessingException e) {
            throw new RuntimeException(e);
        }
    }

}