/*
 * Copyright (c) Jaguar Land Rover Ltd 2023. All rights reserved
 */
package com.jaguarlandrover.vcdp;


import com.alibaba.fastjson2.JSONObject;
import com.amazonaws.services.kinesisanalytics.runtime.KinesisAnalyticsRuntime;
import com.jaguarlandrover.vcdp.config.ConfigLoader;
import com.jaguarlandrover.vcdp.serde.MsgGatewayProtobufDeserializer;
import org.apache.flink.connector.kafka.source.enumerator.initializer.OffsetsInitializer;
import org.apache.flink.streaming.api.environment.StreamExecutionEnvironment;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.junit.Test;

import java.io.IOException;
import java.util.Map;
import java.util.Properties;

import static com.jaguarlandrover.vcdp.DataEngineITTest.PROTO_DESC_DIRECTORY;
import static com.jaguarlandrover.vcdp.config.ConfigLoader.getJsonObjectFromResourceFile;
import static com.jaguarlandrover.vcdp.config.Utils.*;
import static org.junit.Assert.*;
import com.jaguarlandrover.vcdp.functions.SourceVaFormatMappingWithKeyFunction;

public class DataEngineUnitTest {
  @Test
  public void testGetKafkaSaslPropertiesLocal() {
    // Test the case of LocalStreamEnvironment
    StreamExecutionEnvironment sEnv = StreamExecutionEnvironment.getExecutionEnvironment();
    Properties localProperties = getKafkaSaslProperties(sEnv);
    assertNotNull(localProperties);
    assertEquals(0, localProperties.size());
  }

  @Test
  public void testGetJsonObjectFromResourceFile() {
    // Test the case of LocalStreamEnvironment
    String jsonFileName = "transformationMapping.json";
    JSONObject jsonObject = getJsonObjectFromResourceFile(jsonFileName);
    assertNotNull(jsonObject);
  }

  @Test
  public void testGetJsonObjectFromResourceFileDoNotExistsError() {
    // Test the case of LocalStreamEnvironment
    String jsonFileName = "transformationMapping_DoNotExists.json";
    JSONObject jsonObject = getJsonObjectFromResourceFile(jsonFileName);
    assertEquals(new JSONObject(), jsonObject);
  }

  @Test
  public void testGetJsonObjectFromResourceFileParsingError() {
    // Test the case of LocalStreamEnvironment
    String jsonFileName = "transformationMapping_error.json";
    JSONObject jsonObject = getJsonObjectFromResourceFile(jsonFileName);
    assertEquals(new JSONObject(), jsonObject);
  }

  @Test
  public void testGetKafkaSaslPropertiesRemote() {
    // Test the case of RemoteStreamEnvironment.
    StreamExecutionEnvironment remoteEnv = StreamExecutionEnvironment.createRemoteEnvironment("mockhost", 111);
    Properties properties = getKafkaSaslProperties(remoteEnv);
    assertNotNull(properties);
    assertEquals("SASL_SSL", properties.getProperty("security.protocol"));
    assertEquals("SASL_SSL", properties.getProperty("security.protocol"));
    assertEquals("AWS_MSK_IAM", properties.getProperty("sasl.mechanism"));
    assertEquals("software.amazon.msk.auth.iam.IAMLoginModule required;", properties.getProperty("sasl.jaas.config"));
    assertEquals("software.amazon.msk.auth.iam.IAMClientCallbackHandler", properties.getProperty("sasl.client.callback.handler.class"));
  }
  @Test
  public void TestStartoffStrategy(){
    assertEquals(OffsetsInitializer.latest().getClass(),getOffsetStrategy("LATEST_OFFSET").getClass());
    assertEquals(OffsetsInitializer.latest().getClass(),getOffsetStrategy("NOTVALID").getClass());
  }

  @Test
  public void TestLocalSenv() throws NullPointerException, IOException {

    Map<String, Properties> applicationPropertiesMapExp = null;

    applicationPropertiesMapExp = KinesisAnalyticsRuntime
            .getApplicationProperties(Thread.currentThread().getContextClassLoader()
                    .getResource("application-properties-blank.json")
                    .getPath());

    assertEquals(applicationPropertiesMapExp ,initPropertiesMap(null, "application-properties-test.json"));

    final StreamExecutionEnvironment sEnv = StreamExecutionEnvironment.getExecutionEnvironment();
    initPropertiesMap(sEnv, "application-properties-tests.json");
    initPropertiesMap(sEnv, "application-properties-ioexcep.json");
  }

  @Test
  public void testIdToClassMapGenerator() {
    try {
      final StreamExecutionEnvironment sEnv = StreamExecutionEnvironment.getExecutionEnvironment();
      final String PROTO_DESC_DIRECTORY = "/protoDescriptors";

      Map<String, String> idToClassMap = ConfigLoader.generateMessageTypeHashMapping(sEnv, PROTO_DESC_DIRECTORY);
      assertNotNull(idToClassMap);
      assertEquals(62, idToClassMap.size());
    } catch (Exception e) {
      throw new RuntimeException(e);
    }
  }
  @Test(expected = Test.None.class )
  public void testMsgGatewayProtobufDeserializer()  {
    final StreamExecutionEnvironment sEnv = StreamExecutionEnvironment.getExecutionEnvironment();
    Map<String, String> idToClassMap = ConfigLoader.generateMessageTypeHashMapping(sEnv, PROTO_DESC_DIRECTORY);
    MsgGatewayProtobufDeserializer chk = new MsgGatewayProtobufDeserializer(idToClassMap);
    byte[] blank = new byte[0];
    ConsumerRecord<byte[], byte[]> kafkaRecordNullKey = new ConsumerRecord<>("anyStringKey", 0, 0, null, blank);
    ConsumerRecord<byte[], byte[]> kafkaRecordNullValue = new ConsumerRecord<>("anyStringKey", 0, 0, blank, null);
    ConsumerRecord<byte[], byte[]> kafkaRecord = new ConsumerRecord<>("anyStringKey", 0, 0, blank, blank);
    chk.deserialize(kafkaRecordNullKey, null);
    chk.deserialize(kafkaRecordNullValue, null);
    chk.deserialize(kafkaRecord, null);
  }

  @Test
  public void testAnonymizeStringNull() {
    assertNull(anonymizeString(null,8));
  }

  @Test
  public void testAnonymizeStringValid() {
    String inputString    = "bsdfsdfsdfsdfsdfsdfsdf";
    String expectedString = "bsdfsdfs**************";
    assertEquals(expectedString,anonymizeString(inputString,8));
  }

  @Test
  public void testSourceVaFormatMappingWithKeyFunctionMapNullTransMapping() {
    JSONObject transformationMappingJson = new JSONObject();
    SourceVaFormatMappingWithKeyFunction function = new SourceVaFormatMappingWithKeyFunction(transformationMappingJson);
    JSONObject inputJson = new JSONObject();
    JSONObject result = function.map(inputJson);
    assertEquals(new JSONObject() ,result);
  }

  @Test
  public void testSourceVaFormatMappingWithKeyFunctionMapStatusNotOK() {
    JSONObject transformationMappingJson = new JSONObject().fluentPut("xyz",  new JSONObject());
    JSONObject inputJson = new JSONObject().fluentPut("hash", "xyz").fluentPut("payload", new JSONObject().fluentPut("status", "ENUM_STATUS_NOT_OK"));
    SourceVaFormatMappingWithKeyFunction function = new SourceVaFormatMappingWithKeyFunction(transformationMappingJson);
    JSONObject result = function.map(inputJson);
    assertEquals(new JSONObject() ,result);
  }

}


