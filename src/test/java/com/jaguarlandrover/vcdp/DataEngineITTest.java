/*
 * Copyright (c) Jaguar Land Rover Ltd 2023. All rights reserved
 */

package com.jaguarlandrover.vcdp;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.amazonaws.services.kinesisanalytics.flink.connectors.serialization.JsonSerializationSchema;
import com.jaguarlandrover.vcdp.Data.Payload;
import com.jaguarlandrover.vcdp.Utils.CookbookKafkaCluster;
import com.jaguarlandrover.vcdp.config.ConfigLoader;
import com.jaguarlandrover.vcdp.functions.SourceVaFormatMappingWithKeyFunction;
import com.jaguarlandrover.vcdp.serde.MsgGatewayProtobufDeserializer;
import lombok.extern.slf4j.Slf4j;
import net.mguenther.kafka.junit.SendKeyValues;
import org.apache.flink.api.common.eventtime.WatermarkStrategy;
import org.apache.flink.connector.kafka.sink.KafkaRecordSerializationSchema;
import org.apache.flink.connector.kafka.sink.KafkaSink;
import org.apache.flink.connector.kafka.source.KafkaSource;
import org.apache.flink.connector.kafka.source.enumerator.initializer.OffsetsInitializer;
import org.apache.flink.runtime.testutils.MiniClusterResourceConfiguration;
import org.apache.flink.streaming.api.datastream.SingleOutputStreamOperator;
import org.apache.flink.streaming.api.environment.StreamExecutionEnvironment;
import org.apache.flink.streaming.api.functions.sink.SinkFunction;
import org.apache.flink.test.junit5.MiniClusterExtension;
import org.apache.flink.test.util.MiniClusterWithClientResource;
import org.apache.kafka.clients.producer.ProducerConfig;
import org.junit.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.api.extension.RegisterExtension;
import org.testcontainers.junit.jupiter.Testcontainers;

import java.util.*;

import static com.jaguarlandrover.vcdp.DataEnginePreprocessor.defineWorkFlow;
import static com.jaguarlandrover.vcdp.config.ConfigLoader.getJsonObjectFromResourceFile;
import static com.jaguarlandrover.vcdp.config.StreamConfig.*;
import static com.jaguarlandrover.vcdp.config.Utils.configureEnvironment;
import static com.jaguarlandrover.vcdp.config.Utils.initPropertiesMap;
import static com.jaguarlandrover.vcdp.sinks.KafkaStreamingSink.getKafkaSink;
import static com.jaguarlandrover.vcdp.sources.KafkaStreamingSource.getKafkaProtobufToJsonObjectSource;
import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.Assert.assertNotNull;


@Slf4j
@Testcontainers
@ExtendWith(MiniClusterExtension.class)
public class DataEngineITTest {

  @RegisterExtension
  public static MiniClusterWithClientResource flinkCluster =
          new MiniClusterWithClientResource(
                  new MiniClusterResourceConfiguration.Builder()
                          .setNumberSlotsPerTaskManager(2)
                          .setNumberTaskManagers(1)
                          .build());

  public static final String PROPERTIES_FILE = "application-properties-test.json";

  private static final String INTOPIC = "TEST.protobuf-input";

  private static final String OUTTOPIC = "TEST.data-product-factory-input";

  public static final String PROTO_DESC_DIRECTORY = "/protoDescriptors";

  JSONObject mappingJson = getJsonObjectFromResourceFile(MAPPING_JSON_TRANSFORMATION);

  private static class CollectSink implements SinkFunction<JSONObject> {
    public static final List<JSONObject> values = Collections.synchronizedList(new ArrayList<>());

    @Override
    public void invoke(JSONObject value, SinkFunction.Context context) {
      values.add(value);
    }
  }


  @Test
  public void testProtobufDeserializerIsCorrect() {
    try (final CookbookKafkaCluster kafka = new CookbookKafkaCluster()) {
      kafka.createTopic(new String[]{INTOPIC});
      StreamExecutionEnvironment sEnv = configureEnvironment();
      sEnv.setBufferTimeout(BUFFER_OUT_TIME);
      Map<String, Properties> propertiesMap = initPropertiesMap(sEnv, PROPERTIES_FILE);
      Properties kafkaProperties = propertiesMap.get(KAFKA_SOURCE_GROUP_ID_KEY);
      Properties producerProps = new Properties();
      producerProps.put(ProducerConfig.BOOTSTRAP_SERVERS_CONFIG, kafka.getBrokerList());
      producerProps.put(ProducerConfig.KEY_SERIALIZER_CLASS_CONFIG,
              "org.apache.kafka.common.serialization.StringSerializer");
      producerProps.put(ProducerConfig.VALUE_SERIALIZER_CLASS_CONFIG,
              "org.apache.kafka.common.serialization.ByteArraySerializer");

//            String base64Data = Payload.base64DataMsgString_V10_5_0; // "YiIiGGnOHtvWvPedm39NNevHe2utud+duOfH9ioGCAIQAhgBcODjkMjNMQ==";
//            String stringDataKey =  Payload.stringDataMsgKey_V10_5_0 ; // "vehicle/uuid-x/notify/data/signal_name";
//            String inputJsonString =Payload.inputJsonMsgString;
      //            "{" +
      //                "\"relay_message\":{" +
      //                    "\"id\":\"ac4e29a8952bf00168d7a625352458f2\"," +
      //                    "\"message_payload\":[8,2,16,2,24,1]}," +
      //                "\"timestamp_ms\":1704447980000" +
      //            "}"
      Map<String,String> hashMapping =  ConfigLoader.generateMessageTypeHashMapping(sEnv, PROTO_DESC_DIRECTORY);

      String base64Data = Payload.base64DataMsgString_V10_5_0; // "YiIiGGnOHtvWvPedm39NNevHe2utud+duOfH9ioGCAIQAhgBcODjkMjNMQ==";
      String stringDataKey =  Payload.stringDataMsgKey_V10_5_0; // "vehicle/uuid-x/notify/data/signal_name";
      String inputJsonString =Payload.inputJsonMsgString_V10_5_0;
////                                    "{\"type\":\"ENUM_MESSAGE_TYPE_EVENT\"," +
////                                    "\"hash\":\"ac4e29a8952bf00168d7a625352458f2\"," +
////                                    "\"timestamp\":\"1704447980000\"," +
////                                    "\"payload\":{\"" +
////                                            "aperture_status\":\"ENUM_APERTURE_STATUS_ALL_APERTURES_EXTERIOR_LOCKED\"," +
////                                            "\"individual_aperture_status\":2," +
////                                            "\"status\":\"ENUM_STATUS_OK\"}," +
////                                    "\"unique_id\":\"ac4e29a8952bf00168d7a625352458f2\"}";
      byte[] binaryData = java.util.Base64.getDecoder().decode(base64Data);

      List<net.mguenther.kafka.junit.KeyValue<String, byte[] >> records = new ArrayList<>();
      records.add(new net.mguenther.kafka.junit.KeyValue<>(stringDataKey, binaryData ));

      kafka.send(SendKeyValues.to(INTOPIC, records).withAll(producerProps));
//            Thread.sleep(2000);

      KafkaSource<JSONObject> kafkaProtobufSource = KafkaSource.<JSONObject>builder()
              .setBootstrapServers("localhost:9092")
              .setTopics(INTOPIC)
              .setStartingOffsets(OffsetsInitializer.earliest())
              .setBounded(OffsetsInitializer.latest())
              .setDeserializer(new MsgGatewayProtobufDeserializer(hashMapping))
              .build();


      SingleOutputStreamOperator<JSONObject> protobufSourceStream = sEnv
              .fromSource(kafkaProtobufSource, WatermarkStrategy.noWatermarks(), "kafka-protobuf-source")
              .name("kafkaIngestSourceStream")
              .uid("kafkaIngestSourceStream");

      CollectSink.values.clear();
      protobufSourceStream.addSink(new CollectSink());
      sEnv.execute();

      Boolean asserter = false;
      int numberOfRecords = CollectSink.values.size();
      if (numberOfRecords>0){
        JSONObject outputJson = CollectSink.values.get(0);;
        JSONObject inputJson = JSON.parseObject(inputJsonString);

        outputJson.remove("start_deserialisation_timestamp");
        outputJson.remove("end_deserialisation_timestamp");
        asserter = inputJson.equals(outputJson);
      }
      assertThat(asserter).isTrue();
      kafka.stop();
    } catch (Exception e) {
      throw new RuntimeException(e);
    }
  }
  @Test
  public void testMappingToVAFormatIsCorrect() {
    try (final CookbookKafkaCluster kafka = new CookbookKafkaCluster()) {
      kafka.createTopic(new String[]{INTOPIC});
      StreamExecutionEnvironment sEnv = configureEnvironment();
      sEnv.setBufferTimeout(BUFFER_OUT_TIME);
      Map<String, Properties> propertiesMap = initPropertiesMap(sEnv, PROPERTIES_FILE);
      Properties kafkaProperties = propertiesMap.get(KAFKA_SOURCE_GROUP_ID_KEY);
      Properties producerProps = new Properties();
      producerProps.put(ProducerConfig.BOOTSTRAP_SERVERS_CONFIG, kafka.getBrokerList());
      producerProps.put(ProducerConfig.KEY_SERIALIZER_CLASS_CONFIG,
              "org.apache.kafka.common.serialization.StringSerializer");
      producerProps.put(ProducerConfig.VALUE_SERIALIZER_CLASS_CONFIG,
              "org.apache.kafka.common.serialization.ByteArraySerializer");

      Map<String,String> hashMapping =  ConfigLoader.generateMessageTypeHashMapping(sEnv, PROTO_DESC_DIRECTORY);

      String base64Data = Payload.base64DataMsgString_V10_5_0; // "YiIiGGnOHtvWvPedm39NNevHe2utud+duOfH9ioGCAIQAhgBcODjkMjNMQ==";
      String stringDataKey =  Payload.stringDataMsgKey_V10_5_0 ; // "vehicle/uuid-x/notify/data/signal_name";
      String inputJsonString =Payload.inputJsonMsgString_V10_5_0;
      byte[] binaryData = java.util.Base64.getDecoder().decode(base64Data);

      List<net.mguenther.kafka.junit.KeyValue<String, byte[] >> records = new ArrayList<>();
      records.add(new net.mguenther.kafka.junit.KeyValue<>(stringDataKey, binaryData ));

      kafka.send(SendKeyValues.to(INTOPIC, records).withAll(producerProps));
//            Thread.sleep(2000);

      KafkaSource<JSONObject> kafkaProtobufSource = KafkaSource.<JSONObject>builder()
              .setBootstrapServers("localhost:9092")
              .setTopics(INTOPIC)
              .setStartingOffsets(OffsetsInitializer.earliest())
              .setBounded(OffsetsInitializer.latest())
              .setDeserializer(new MsgGatewayProtobufDeserializer(hashMapping))
              .build();

      SingleOutputStreamOperator<JSONObject> protobufSourceStream = sEnv
              .fromSource(kafkaProtobufSource, WatermarkStrategy.noWatermarks(), "kafka-protobuf-source")
              .name("kafkaIngestSourceStream")
              .uid("kafkaIngestSourceStream");

      SingleOutputStreamOperator<JSONObject> kafkaMappingSourceStream =  protobufSourceStream
              .map(new SourceVaFormatMappingWithKeyFunction(mappingJson))
              .filter(jsonObj -> !jsonObj.isEmpty())
              .name("kafkaMappingSourceStream")
              .uid("kafkaMappingSourceStream");

      CollectSink.values.clear();
      kafkaMappingSourceStream.addSink(new CollectSink());
      sEnv.execute();

      Boolean asserter = false;
      int numberOfRecords = CollectSink.values.size();
      if (numberOfRecords>0){
        JSONObject outputJson = CollectSink.values.get(0);
        JSONObject expectedOoutputFAFormatJson = JSON.parseObject(Payload.getOutputFAFormatJsonMsgString_V10_5_0);

        outputJson.remove("start_deserialisation_timestamp");
        outputJson.remove("end_deserialisation_timestamp");
        asserter = expectedOoutputFAFormatJson.equals(outputJson);
      }
      assertThat(asserter).isTrue();
      kafka.stop();
    } catch (Exception e) {
      throw new RuntimeException(e);
    }
  }
  @Test
  public void testMappingToVAFormatIsCorrectGetResponse() {
    try (final CookbookKafkaCluster kafka = new CookbookKafkaCluster()) {
      kafka.createTopic(new String[]{INTOPIC});
      StreamExecutionEnvironment sEnv = configureEnvironment();
      sEnv.setBufferTimeout(BUFFER_OUT_TIME);
      Map<String, Properties> propertiesMap = initPropertiesMap(sEnv, PROPERTIES_FILE);
      Properties kafkaProperties = propertiesMap.get(KAFKA_SOURCE_GROUP_ID_KEY);
      Properties producerProps = new Properties();
      producerProps.put(ProducerConfig.BOOTSTRAP_SERVERS_CONFIG, kafka.getBrokerList());
      producerProps.put(ProducerConfig.KEY_SERIALIZER_CLASS_CONFIG,
              "org.apache.kafka.common.serialization.StringSerializer");
      producerProps.put(ProducerConfig.VALUE_SERIALIZER_CLASS_CONFIG,
              "org.apache.kafka.common.serialization.ByteArraySerializer");

      Map<String,String> hashMapping =  ConfigLoader.generateMessageTypeHashMapping(sEnv, PROTO_DESC_DIRECTORY);

      String base64Data = Payload.base64DataMsgString_GetResponse_V10_5_0;
      String stringDataKey =  Payload.stringDataMsgKey_GetResponse_V10_5_0;
      String inputJsonString =Payload.inputJsonMsgString_V10_5_0;
      byte[] binaryData = java.util.Base64.getDecoder().decode(base64Data);

      List<net.mguenther.kafka.junit.KeyValue<String, byte[] >> records = new ArrayList<>();
      records.add(new net.mguenther.kafka.junit.KeyValue<>(stringDataKey, binaryData ));

      kafka.send(SendKeyValues.to(INTOPIC, records).withAll(producerProps));
//            Thread.sleep(2000);

      KafkaSource<JSONObject> kafkaProtobufSource = KafkaSource.<JSONObject>builder()
              .setBootstrapServers("localhost:9092")
              .setTopics(INTOPIC)
              .setStartingOffsets(OffsetsInitializer.earliest())
              .setBounded(OffsetsInitializer.latest())
              .setDeserializer(new MsgGatewayProtobufDeserializer(hashMapping))
              .build();


      SingleOutputStreamOperator<JSONObject> protobufSourceStream = sEnv
              .fromSource(kafkaProtobufSource, WatermarkStrategy.noWatermarks(), "kafka-protobuf-source")
              .name("kafkaIngestSourceStream")
              .uid("kafkaIngestSourceStream");

      SingleOutputStreamOperator<JSONObject> kafkaMappingSourceStream =  protobufSourceStream
              .map(new SourceVaFormatMappingWithKeyFunction(mappingJson))
              .filter(jsonObj -> !jsonObj.isEmpty())
              .name("kafkaMappingSourceStream")
              .uid("kafkaMappingSourceStream");

      CollectSink.values.clear();
      kafkaMappingSourceStream.addSink(new CollectSink());
      sEnv.execute();

      Boolean asserter = false;
      int numberOfRecords = CollectSink.values.size();
      if (numberOfRecords>0){
        JSONObject outputJson = CollectSink.values.get(0);
        JSONObject expectedOutputFAFormatJson = JSON.parseObject(Payload.getOutputFAFormatJsonMsgString_GetResponse_V10_5_0);
        outputJson.remove("start_deserialisation_timestamp");
        outputJson.remove("end_deserialisation_timestamp");
        asserter = expectedOutputFAFormatJson.equals(outputJson);
      }
      assertThat(asserter).isTrue();
      kafka.stop();
    } catch (Exception e) {
      throw new RuntimeException(e);
    }
  }
  @Test
  public void testWorkflow() {
    try (final CookbookKafkaCluster kafka = new CookbookKafkaCluster()) {
      kafka.createTopic(new String[]{INTOPIC});
      kafka.createTopic(new String[]{OUTTOPIC});

      StreamExecutionEnvironment sEnv = configureEnvironment();
      sEnv.setBufferTimeout(BUFFER_OUT_TIME);
      Map<String, Properties> propertiesMap = initPropertiesMap(sEnv, PROPERTIES_FILE);

      Properties producerProps = new Properties();
      producerProps.put(ProducerConfig.BOOTSTRAP_SERVERS_CONFIG, kafka.getBrokerList());
      producerProps.put(ProducerConfig.KEY_SERIALIZER_CLASS_CONFIG,
              "org.apache.kafka.common.serialization.StringSerializer");
      producerProps.put(ProducerConfig.VALUE_SERIALIZER_CLASS_CONFIG,
              "org.apache.kafka.common.serialization.ByteArraySerializer");

      Map<String,String> hashMapping =  ConfigLoader.generateMessageTypeHashMapping(sEnv, PROTO_DESC_DIRECTORY);

      String base64Data = Payload.base64DataMsgString_V10_5_0; // "YiIiGGnOHtvWvPedm39NNevHe2utud+duOfH9ioGCAIQAhgBcODjkMjNMQ==";
      String stringDataKey =  Payload.stringDataMsgKey_V10_5_0; // "vehicle/uuid-x/notify/data/signal_name";

      byte[] binaryData = java.util.Base64.getDecoder().decode(base64Data);

      List<net.mguenther.kafka.junit.KeyValue<String, byte[] >> records = new ArrayList<>();
      records.add(new net.mguenther.kafka.junit.KeyValue<>(stringDataKey, binaryData ));

      kafka.send(SendKeyValues.to(INTOPIC, records).withAll(producerProps));

      KafkaSource<JSONObject> kafkaProtobufSource = KafkaSource.<JSONObject>builder()
              .setBootstrapServers(kafka.getBrokerList())
              .setTopics(INTOPIC)
              .setStartingOffsets(OffsetsInitializer.earliest())
              .setBounded(OffsetsInitializer.latest())
              .setDeserializer(new MsgGatewayProtobufDeserializer(hashMapping))
              .build();
      KafkaSink<JSONObject> kafkaSink = KafkaSink.<JSONObject>builder()
              .setBootstrapServers(kafka.getBrokerList())
              .setRecordSerializer( KafkaRecordSerializationSchema.builder()
                      .setValueSerializationSchema(new JsonSerializationSchema<JSONObject>())
                      .setTopic(OUTTOPIC)
                      .build())
              .build();

      defineWorkFlow( sEnv, kafkaProtobufSource,kafkaSink);

      kafka.send(SendKeyValues.to(INTOPIC, records).withAll(producerProps));

      sEnv.execute("Data Engine Preprocessor Test");

      final List<String> topicRecords = kafka.getTopicRecords(OUTTOPIC, 1);

      Boolean asserter = false;

      if (topicRecords.size() >= 1 ){
        asserter = true;
      }

      assertThat(asserter).isTrue();
      kafka.stop();
    } catch (Exception e) {
      throw new RuntimeException(e);
    }
  }
  @Test
  public void testKafkaSourceAndSink() {
    StreamExecutionEnvironment sEnv = configureEnvironment();
    Map<String, Properties> propertiesMap = initPropertiesMap(sEnv, PROPERTIES_FILE);
    Properties kafkaProperties = propertiesMap.get(KAFKA_SOURCE_GROUP_ID_KEY);
    String TopicNameOut = kafkaProperties.getProperty(KAFKA_TOPIC_JSON_OUT_NAME);
    String BootstrapServers = kafkaProperties.getProperty(KAFKA_INPUT_BOOTSTRAP_SERVERS);
    String TopicNameIn = kafkaProperties.getProperty(KAFKA_TOPIC_PROTOBUF_IN_NAME);
    Map<String,String> hashMapping =  ConfigLoader.generateMessageTypeHashMapping(sEnv, PROTO_DESC_DIRECTORY);
    KafkaSource<JSONObject> source = getKafkaProtobufToJsonObjectSource(sEnv, kafkaProperties,hashMapping);
    KafkaSink<JSONObject> kafkaSink = getKafkaSink(sEnv, kafkaProperties);
    assertNotNull(source);
    assertNotNull(kafkaSink);
  }

  @Test
  public void testMappingToVAFormatNoValidValueInMappingFile() {
    try (final CookbookKafkaCluster kafka = new CookbookKafkaCluster()) {
      kafka.createTopic(new String[]{INTOPIC});
      StreamExecutionEnvironment sEnv = configureEnvironment();
      sEnv.setBufferTimeout(BUFFER_OUT_TIME);
      Properties producerProps = new Properties();
      producerProps.put(ProducerConfig.BOOTSTRAP_SERVERS_CONFIG, kafka.getBrokerList());
      producerProps.put(ProducerConfig.KEY_SERIALIZER_CLASS_CONFIG,
              "org.apache.kafka.common.serialization.StringSerializer");
      producerProps.put(ProducerConfig.VALUE_SERIALIZER_CLASS_CONFIG,
              "org.apache.kafka.common.serialization.ByteArraySerializer");

      Map<String,String> hashMapping =  ConfigLoader.generateMessageTypeHashMapping(sEnv, PROTO_DESC_DIRECTORY);

      String base64Data = Payload.base64DataMsgString_V10_5_0;
      String stringDataKey =  Payload.stringDataMsgKey_V10_5_0 ;
      byte[] binaryData = java.util.Base64.getDecoder().decode(base64Data);

      List<net.mguenther.kafka.junit.KeyValue<String, byte[] >> records = new ArrayList<>();
      records.add(new net.mguenther.kafka.junit.KeyValue<>(stringDataKey, binaryData ));

      kafka.send(SendKeyValues.to(INTOPIC, records).withAll(producerProps));

      KafkaSource<JSONObject> kafkaProtobufSource = KafkaSource.<JSONObject>builder()
              .setBootstrapServers("localhost:9092")
              .setTopics(INTOPIC)
              .setStartingOffsets(OffsetsInitializer.earliest())
              .setBounded(OffsetsInitializer.latest())
              .setDeserializer(new MsgGatewayProtobufDeserializer(hashMapping))
              .build();


      SingleOutputStreamOperator<JSONObject> protobufSourceStream = sEnv
              .fromSource(kafkaProtobufSource, WatermarkStrategy.noWatermarks(), "kafka-protobuf-source")
              .name("kafkaIngestSourceStream")
              .uid("kafkaIngestSourceStream");

      mappingJson.remove("e416518c20e657325bf83089af9f7873");
      SingleOutputStreamOperator<JSONObject> kafkaMappingSourceStream =  protobufSourceStream
              .map(new SourceVaFormatMappingWithKeyFunction(mappingJson))
              .filter(jsonObj -> !jsonObj.isEmpty())
              .name("kafkaMappingSourceStream")
              .uid("kafkaMappingSourceStream");

      CollectSink.values.clear();
      kafkaMappingSourceStream.addSink(new CollectSink());
      sEnv.execute();

      Boolean asserter = false;
      int numberOfRecords = CollectSink.values.size();
      asserter = (numberOfRecords == 0);

      assertThat(asserter).isTrue();
      kafka.stop();
    } catch (Exception e) {
      throw new RuntimeException(e);
    }
  }
}