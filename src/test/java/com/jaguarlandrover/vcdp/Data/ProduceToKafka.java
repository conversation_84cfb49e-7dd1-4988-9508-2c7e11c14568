package com.jaguarlandrover.vcdp.Data;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;

import java.util.function.Supplier;

/** A supplier that produces Strings. */
public class ProduceToKafka implements Supplier<JSONObject> {
    private String payload;

    public ProduceToKafka(String payload) {
        this.payload = payload;
    }
    @Override
    public JSONObject get() {
        return JSON.parseObject(payload);
    }

}
