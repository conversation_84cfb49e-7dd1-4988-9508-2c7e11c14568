package com.jaguarlandrover.vcdp.Data;

public interface Payload {

    String base64DataMsgString_V10_5_0 = "IjQKIGU0MTY1MThjMjBlNjU3MzI1YmY4MzA4OWFmOWY3ODczEhAIARADGAMgAygDMAM4A0ABMMShuZKcMg==";
    String stringDataMsgKey_V10_5_0 = "vehicle/uuid-10-5-0-x/notify/data/signal_name ";
    String inputJsonMsgString_V10_5_0 ="{\"hash\":\"e416518c20e657325bf83089af9f7873\"," +
            "\"unique_id\":\"uuid-10-5-0-x\"," +
            "\"payload\":{" +
                "\"status\":\"ENUM_STATUS_OK\"," +
                "\"driver_door_status\":\"ENUM_APERTURE_LOCK_STATUS_DOUBLE_LOCKED\"," +
                "\"passenger_door_status\":\"ENUM_APERTURE_LOCK_STATUS_DOUBLE_LOCKED\"," +
                "\"reardriver_door_status\":\"ENUM_APERTURE_LOCK_STATUS_DOUBLE_LOCKED\"," +
                "\"rearpassenger_door_status\":\"ENUM_APERTURE_LOCK_STATUS_DOUBLE_LOCKED\"," +
                "\"tailgate_status\":\"ENUM_APERTURE_LOCK_STATUS_DOUBLE_LOCKED\"," +
                "\"bonnet_status\":\"ENUM_APERTURE_LOCK_STATUS_DOUBLE_LOCKED\"," +
                "\"central_lock_status\":\"ENUM_CENTRAL_LOCK_STATUS_UNLOCKED\"}," +
            "\"timestamp_ms\":1725541798084}";
    String getOutputFAFormatJsonMsgString_V10_5_0 = "{\"query_id\":\"Q730203001\"," +
            "\"unique_id\":\"uuid-10-5-0-x\"," +
            "\"data\":[" +
                "{\"data_id\":\"SRV-ApertureLockState-driver_door_status\"," +
                    "\"samples\":[" +
                        "{\"timestamp_ms\":1725541798084," +
                        "\"value\":\"ENUM_APERTURE_LOCK_STATUS_DOUBLE_LOCKED\"," +
                        "\"type\":\"DATA_STRING\"}]}," +
                "{\"data_id\":\"SRV-ApertureLockState-passenger_door_status\"," +
                    "\"samples\":[" +
                        "{\"timestamp_ms\":1725541798084," +
                            "\"value\":\"ENUM_APERTURE_LOCK_STATUS_DOUBLE_LOCKED\"," +
                            "\"type\":\"DATA_STRING\"}]}," +
                            "{\"data_id\":\"SRV-ApertureLockState-reardriver_door_status\"," +
                    "\"samples\":[" +
                        "{\"timestamp_ms\":1725541798084," +
                        "\"value\":\"ENUM_APERTURE_LOCK_STATUS_DOUBLE_LOCKED\"," +
                        "\"type\":\"DATA_STRING\"}]}," +
                "{\"data_id\":\"SRV-ApertureLockState-rearpassenger_door_status\"," +
                    "\"samples\":[" +
                        "{\"timestamp_ms\":1725541798084," +
                        "\"value\":\"ENUM_APERTURE_LOCK_STATUS_DOUBLE_LOCKED\"," +
                        "\"type\":\"DATA_STRING\"}]}," +
                "{\"data_id\":\"SRV-ApertureLockState-tailgate_status\"," +
                    "\"samples\":[" +
                        "{\"timestamp_ms\":1725541798084," +
                        "\"value\":\"ENUM_APERTURE_LOCK_STATUS_DOUBLE_LOCKED\"," +
                        "\"type\":\"DATA_STRING\"}]}," +
                "{\"data_id\":\"SRV-ApertureLockState-bonnet_status\"," +
                    "\"samples\":[" +
                        "{\"timestamp_ms\":1725541798084," +
                        "\"value\":\"ENUM_APERTURE_LOCK_STATUS_DOUBLE_LOCKED\"," +
                        "\"type\":\"DATA_STRING\"}]}," +
                "{\"data_id\":\"SRV-ApertureLockState-central_lock_status\"," +
                    "\"samples\":[" +
                        "{\"timestamp_ms\":1725541798084," +
                        "\"value\":\"ENUM_CENTRAL_LOCK_STATUS_UNLOCKED\"," +
                        "\"type\":\"DATA_STRING\"}]}]," +
            "\"event_timestamp_ms\":1725541798084," +
            "\"fleet_id\":\"\"}";

  String base64DataMsgString_GetResponse_V10_5_0 = "IjUKIDgwNDc3ODI0NjU0NmYwZmZlYmJlODM2Zjg1NWEyZDc1EhEIARADGAEgAoABAogBAZABATDx7Y2xpjI=";
  String stringDataMsgKey_GetResponse_V10_5_0 = "vehicle/uuid-10-5-0-x/GetChargeStateResponse ";
  String getOutputFAFormatJsonMsgString_GetResponse_V10_5_0 = "{\"unique_id\":\"uuid-10-5-0-x\"," +
            "\"query_id\":\"Q730502010\"," +
            "\"data\":[" +
                "{\"data_id\":\"SRV-ChargeState-charging_status\"," +
                    "\"samples\":[" +
                        "{\"timestamp_ms\":1728290453233,\"value\":\"ENUM_CHARGE_STATE_WAITING_TO_CHARGE\",\"type\":\"DATA_STRING\"}]}," +
                "{\"data_id\":\"SRV-ChargeState-charge_error_state\"," +
                    "\"samples\":[" +
                        "{\"timestamp_ms\":1728290453233,\"value\":\"ENUM_CHARGE_ERROR_MODE_NO_ERROR\",\"type\":\"DATA_STRING\"}]}," +
                "{\"data_id\":\"SRV-ChargeState-charging_inlet_state\"," +
                    "\"samples\":[" +
                        "{\"timestamp_ms\":1728290453233,\"value\":\"ENUM_CHARGING_INLET_STATE_PLUGGED\",\"type\":\"DATA_STRING\"}]}," +
                "{\"data_id\":\"SRV-ChargeState-charge_cable_lock_status\"," +
                    "\"samples\":[" +
                        "{\"timestamp_ms\":1728290453233,\"value\":\"ENUM_CHARGE_CABLE_OPERATION_LOCK\",\"type\":\"DATA_STRING\"}]}," +
                "{\"data_id\":\"SRV-ChargeState-charge_troubleshooter\"," +
                    "\"samples\":[" +
                        "{\"timestamp_ms\":1728290453233,\"value\":\"ENUM_CHRG_TROUBLESHT_CLEAR_MESSAGE\",\"type\":\"DATA_STRING\"}]}," +
                "{\"data_id\":\"SRV-ChargeState-tgt_soc_reachable_by_departure\"," +
                    "\"samples\":[" +
                        "{\"timestamp_ms\":1728290453233,\"value\":true,\"type\":\"DATA_BOOL\"}]}]," +
            "\"event_timestamp_ms\":1728290453233," +
            "\"fleet_id\":\"\"}";

}
