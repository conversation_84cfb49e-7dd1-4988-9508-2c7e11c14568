{"e416518c20e657325bf83089af9f7873": {"query_id": "Q730203001", "data_id_mapping": {"driver_door_status": {"id": "SRV-ApertureLockState-driver_door_status", "type": "DATA_STRING"}, "passenger_door_status": {"id": "SRV-ApertureLockState-passenger_door_status", "type": "DATA_STRING"}, "reardriver_door_status": {"id": "SRV-ApertureLockState-reardriver_door_status", "type": "DATA_STRING"}, "rearpassenger_door_status": {"id": "SRV-ApertureLockState-rearpassenger_door_status", "type": "DATA_STRING"}, "tailgate_status": {"id": "SRV-ApertureLockState-tailgate_status", "type": "DATA_STRING"}, "bonnet_status": {"id": "SRV-ApertureLockState-bonnet_status", "type": "DATA_STRING"}, "central_lock_status": {"id": "SRV-ApertureLockState-central_lock_status", "type": "DATA_STRING"}}}, "c64fe5047d3cb5b9e9e40705041c4107": {"query_id": "Q730203002", "data_id_mapping": {"alarm_status": {"id": "SRV-VehicleAlarmState-alarm_status", "type": "DATA_STRING"}}}, "13d65765b3eb51dead52c637ae68f56f": {"query_id": "Q730303001", "data_id_mapping": {"cabin_air_cleaning_status": {"id": "SRV-VehicleCabinAirCleanState-cabin_air_cleaning_status", "type": "DATA_STRING"}, "cabin_pm2_5_level": {"id": "SRV-VehicleCabinAirCleanState-cabin_pm2_5_level", "type": "DATA_FLOAT"}, "cabin_pm2_5_band": {"id": "SRV-VehicleCabinAirCleanState-cabin_pm2_5_band", "type": "DATA_FLOAT"}, "external_pm2_5_level": {"id": "SRV-VehicleCabinAirCleanState-external_pm2_5_level", "type": "DATA_FLOAT"}, "external_pm2_5_band": {"id": "SRV-VehicleCabinAirCleanState-external_pm2_5_band", "type": "DATA_FLOAT"}, "cabin_air_clean_cycles_remaining": {"id": "SRV-VehicleCabinAirCleanState-cabin_air_clean_cycles_remaining", "type": "DATA_FLOAT"}}}, "a0f898abc8c56b4d357ad109bb843a5c": {"query_id": "Q730303002", "data_id_mapping": {"precondition_mode": {"id": "SRV-VehiclePreconditionState-precondition_mode", "type": "DATA_STRING"}, "precondition_status": {"id": "SRV-VehiclePreconditionState-precondition_status", "type": "DATA_STRING"}, "time_remaining": {"id": "SRV-VehiclePreconditionState-time_remaining", "type": "DATA_FLOAT"}}}, "18fb7383bc4e2d89e6bda25274e906b1": {"query_id": "Q730501001", "data_id_mapping": {"charging_status": {"id": "SRV-ChargeState-charging_status", "type": "DATA_STRING"}, "charge_error_state": {"id": "SRV-ChargeState-charge_error_state", "type": "DATA_STRING"}, "charging_inlet_state": {"id": "SRV-ChargeState-charging_inlet_state", "type": "DATA_STRING"}, "charge_cable_lock_status": {"id": "SRV-ChargeState-charge_cable_lock_status", "type": "DATA_STRING"}, "charge_troubleshooter": {"id": "SRV-ChargeState-charge_troubleshooter", "type": "DATA_STRING"}, "tgt_soc_reachable_by_departure": {"id": "SRV-ChargeState-tgt_soc_reachable_by_departure", "type": "DATA_BOOL"}}}, "b8637abb07f2e69229d81c35b5b23b64": {"query_id": "Q730501002", "data_id_mapping": {"charge_type": {"id": "SRV-ChargeSettings-charge_type", "type": "DATA_STRING"}, "off_peak_start_time_hours": {"id": "SRV-ChargeSettings-off_peak_start_time_hours", "type": "DATA_FLOAT"}, "off_peak_start_time_minutes": {"id": "SRV-ChargeSettings-off_peak_start_time_minutes", "type": "DATA_FLOAT"}, "off_peak_stop_time_hours": {"id": "SRV-ChargeSettings-off_peak_stop_time_hours", "type": "DATA_FLOAT"}, "off_peak_stop_time_minutes": {"id": "SRV-ChargeSettings-off_peak_stop_time_minutes", "type": "DATA_FLOAT"}, "max_battery_soc": {"id": "SRV-ChargeSettings-max_battery_soc", "type": "DATA_FLOAT"}, "actual_ac_charge_rate_limit": {"id": "SRV-ChargeSettings-actual_ac_charge_rate_limit", "type": "DATA_FLOAT"}}}, "3ff084974f6ccc85f37757777dba4be9": {"query_id": "Q730501003", "data_id_mapping": {"current_battery_soc": {"id": "SRV-BatteryCurrentStateOfCharge-current_battery_soc", "type": "DATA_FLOAT"}}}, "2660c9fa694be9d1ec4efb52abe6dbc4": {"query_id": "Q730501004", "data_id_mapping": {"charging_method": {"id": "SRV-ChargeInProgressData-charging_method", "type": "DATA_STRING"}, "max_current_limit": {"id": "SRV-ChargeInProgressData-max_current_limit", "type": "DATA_FLOAT"}, "inst_current": {"id": "SRV-ChargeInProgressData-inst_current", "type": "DATA_FLOAT"}, "inst_charge_power": {"id": "SRV-ChargeInProgressData-inst_charge_power", "type": "DATA_FLOAT"}, "charge_rate_mts_ph": {"id": "SRV-ChargeInProgressData-charge_rate_mts_ph", "type": "DATA_FLOAT"}, "charge_rate_percentage_ph": {"id": "SRV-ChargeInProgressData-charge_rate_percentage_ph", "type": "DATA_FLOAT"}}}, "7650032748da867015fa78e7af982a97": {"query_id": "Q730501005", "data_id_mapping": {"next_charge_start_time_seconds": {"id": "SRV-PredictedCharge-next_charge_start_time_seconds", "type": "DATA_FLOAT"}, "predicted_charge_data": {"id": "SRV-PredictedCharge-predicted_charge_data", "type": "DATA_JSON_ARRAY"}, "time_to_tgt_soc": {"id": "SRV-PredictedCharge-time_to_tgt_soc", "type": "DATA_FLOAT"}, "tgt_soc_ev_range_km": {"id": "SRV-PredictedCharge-tgt_soc_ev_range_km", "type": "DATA_FLOAT"}}}, "1d3e02f811caa6daf1b645e6d5af9060": {"query_id": "Q730501006", "data_id_mapping": {"left_charge_door_status": {"id": "SRV-ChargeDoorStatus-left_charge_door_status", "type": "DATA_STRING"}, "right_charge_door_status": {"id": "SRV-ChargeDoorStatus-right_charge_door_status", "type": "DATA_STRING"}}}, "287ebee0782dbd5f86331ddc8c9bc3a1": {"query_id": "Q730501007", "data_id_mapping": {"range_added": {"id": "SRV-ChargeSessionAttributes-range_added", "type": "DATA_FLOAT"}, "energy_added": {"id": "SRV-ChargeSessionAttributes-energy_added", "type": "DATA_FLOAT"}, "charging_duration": {"id": "SRV-ChargeSessionAttributes-charging_duration", "type": "DATA_FLOAT"}}}, "357483b544cdddbb89b9a835741c7ce9": {"query_id": "Q730501008", "data_id_mapping": {"hv_batt_energy_max_usable": {"id": "SRV-BatteryEnergyData-hv_batt_energy_max_usable", "type": "DATA_FLOAT"}, "hv_battery_energy_min_usable": {"id": "SRV-BatteryEnergyData-hv_battery_energy_min_usable", "type": "DATA_FLOAT"}, "predicted_hv_batt_energy_available_at_target_soc": {"id": "SRV-BatteryEnergyData-predicted_hv_batt_energy_available_at_target_soc", "type": "DATA_FLOAT"}, "hv_batt_state_of_health": {"id": "SRV-BatteryEnergyData-hv_batt_state_of_health", "type": "DATA_FLOAT"}}}, "f4781b5d8948c88c882e71e8f17b044f": {"query_id": "Q730501009", "data_id_mapping": {"hv_batt_energy_estimated_loss_discharge": {"id": "SRV-BatteryEnergyLoss-hv_batt_energy_estimated_loss_discharge", "type": "DATA_FLOAT"}, "hv_batt_energy_estimated_loss_discharge_route_total": {"id": "SRV-BatteryEnergyLoss-hv_batt_energy_estimated_loss_discharge_route_total", "type": "DATA_FLOAT"}}}, "19f8ebd56e94c886423bdc29db6e830a": {"query_id": "Q730303003", "data_id_mapping": {"seat_climate_zone": {"id": "SRV-SeatClimateStatus-seat_climate_zone", "type": "DATA_JSON_ARRAY"}}}, "93918404a42e394c189d1a2f9b4ca778": {"query_id": "Q730303004", "data_id_mapping": {"hsw_temperature_level": {"id": "SRV-HeatedSteeringWheelStatus-hsw_temperature_level", "type": "DATA_STRING"}, "hsw_control_state": {"id": "SRV-HeatedSteeringWheelStatus-hsw_control_state", "type": "DATA_STRING"}}}, "3db5dacfce80894564b62211620111ac": {"query_id": "Q730303005", "data_id_mapping": {"precondition_mode": {"id": "SRV-VehiclePreconditionState-precondition_mode", "type": "DATA_STRING"}, "precondition_status": {"id": "SRV-VehiclePreconditionState-precondition_status", "type": "DATA_STRING"}, "time_remaining": {"id": "SRV-VehiclePreconditionState-time_remaining", "type": "DATA_FLOAT"}}}, "804778246546f0ffebbe836f855a2d75": {"query_id": "Q730501010", "data_id_mapping": {"charging_status": {"id": "SRV-ChargeState-charging_status", "type": "DATA_STRING"}, "charge_error_state": {"id": "SRV-ChargeState-charge_error_state", "type": "DATA_STRING"}, "charging_inlet_state": {"id": "SRV-ChargeState-charging_inlet_state", "type": "DATA_STRING"}, "charging_method": {"id": "SRV-ChargeInProgressData-charging_method", "type": "DATA_STRING"}, "max_current_limit": {"id": "SRV-ChargeInProgressData-max_current_limit", "type": "DATA_FLOAT"}, "inst_current": {"id": "SRV-ChargeInProgressData-inst_current", "type": "DATA_FLOAT"}, "inst_charge_power": {"id": "SRV-ChargeInProgressData-inst_charge_power", "type": "DATA_FLOAT"}, "charge_rate_mts_ph": {"id": "SRV-ChargeInProgressData-charge_rate_mts_ph", "type": "DATA_FLOAT"}, "charge_rate_percentage_ph": {"id": "SRV-ChargeInProgressData-charge_rate_percentage_ph", "type": "DATA_FLOAT"}, "next_charge_start_time_seconds": {"id": "SRV-PredictedCharge-next_charge_start_time_seconds", "type": "DATA_FLOAT"}, "predicted_charge_data": {"id": "SRV-PredictedCharge-predicted_charge_data", "type": "DATA_JSON_ARRAY"}, "time_to_tgt_soc": {"id": "SRV-PredictedCharge-time_to_tgt_soc", "type": "DATA_FLOAT"}, "tgt_soc_ev_range_km": {"id": "SRV-PredictedCharge-tgt_soc_ev_range_km", "type": "DATA_FLOAT"}, "charge_cable_lock_status": {"id": "SRV-ChargeState-charge_cable_lock_status", "type": "DATA_STRING"}, "charge_troubleshooter": {"id": "SRV-ChargeState-charge_troubleshooter", "type": "DATA_STRING"}, "tgt_soc_reachable_by_departure": {"id": "SRV-ChargeState-tgt_soc_reachable_by_departure", "type": "DATA_BOOL"}}}, "6067cc6156f8504a4dbfccbd5b474e5b": {"query_id": "Q730501011", "data_id_mapping": {"charge_type": {"id": "SRV-ChargeSettings-charge_type", "type": "DATA_STRING"}, "off_peak_start_time_hours": {"id": "SRV-ChargeSettings-off_peak_start_time_hours", "type": "DATA_FLOAT"}, "off_peak_start_time_minutes": {"id": "SRV-ChargeSettings-off_peak_start_time_minutes", "type": "DATA_FLOAT"}, "off_peak_stop_time_hours": {"id": "SRV-ChargeSettings-off_peak_stop_time_hours", "type": "DATA_FLOAT"}, "off_peak_stop_time_minutes": {"id": "SRV-ChargeSettings-off_peak_stop_time_minutes", "type": "DATA_FLOAT"}, "max_battery_soc": {"id": "SRV-ChargeSettings-max_battery_soc", "type": "DATA_FLOAT"}, "actual_ac_charge_rate_limit": {"id": "SRV-ChargeSettings-actual_ac_charge_rate_limit", "type": "DATA_FLOAT"}}}, "a21106dd8ee19ea847add36ae8ba5f47": {"query_id": "Q730501012", "data_id_mapping": {"current_battery_soc": {"id": "SRV-BatteryCurrentStateOfCharge-current_battery_soc", "type": "DATA_FLOAT"}, "hv_batt_energy_available_at_current_soc": {"id": "SRV-BatteryCurrentStateOfCharge-hv_batt_energy_available_at_current_soc", "type": "DATA_FLOAT"}}}, "7280b66970a7b989b22bc9c301e7de67": {"query_id": "Q730501013", "data_id_mapping": {"left_charge_door_status": {"id": "SRV-ChargeDoorStatus-left_charge_door_status", "type": "DATA_STRING"}, "right_charge_door_status": {"id": "SRV-ChargeDoorStatus-right_charge_door_status", "type": "DATA_STRING"}}}, "46e8317857a89e72b5be21643057a4e3": {"query_id": "Q730501014", "data_id_mapping": {"range_added": {"id": "SRV-ChargeSessionAttributes-range_added", "type": "DATA_FLOAT"}, "energy_added": {"id": "SRV-ChargeSessionAttributes-energy_added", "type": "DATA_FLOAT"}, "charging_duration": {"id": "SRV-ChargeSessionAttributes-charging_duration", "type": "DATA_FLOAT"}}}, "dab0cc201abf575de3239d4958138d5e": {"query_id": "Q730501015", "data_id_mapping": {"hv_batt_energy_max_usable": {"id": "SRV-BatteryEnergyData-hv_batt_energy_max_usable", "type": "DATA_FLOAT"}, "hv_battery_energy_min_usable": {"id": "SRV-BatteryEnergyData-hv_battery_energy_min_usable", "type": "DATA_FLOAT"}, "predicted_hv_batt_energy_available_at_target_soc": {"id": "SRV-BatteryEnergyData-predicted_hv_batt_energy_available_at_target_soc", "type": "DATA_FLOAT"}, "hv_batt_state_of_health": {"id": "SRV-BatteryEnergyData-hv_batt_state_of_health", "type": "DATA_FLOAT"}}}, "edd110cd051b6b49a34657181cd6309f": {"query_id": "Q730501016", "data_id_mapping": {"hv_batt_energy_estimated_loss_discharge": {"id": "SRV-BatteryEnergyLoss-hv_batt_energy_estimated_loss_discharge", "type": "DATA_FLOAT"}, "hv_batt_energy_estimated_loss_discharge_route_total": {"id": "SRV-BatteryEnergyLoss-hv_batt_energy_estimated_loss_discharge_route_total", "type": "DATA_FLOAT"}}}, "c1a92052b0771186835a359689a3889f": {"query_id": "Q730303006", "data_id_mapping": {"seat_climate_zone": {"id": "SRV-SeatClimateStatus-seat_climate_zone", "type": "DATA_JSON_ARRAY"}}}, "3f93672143fa5cb9d9175128b0ff2133": {"query_id": "Q730303007", "data_id_mapping": {"hsw_temperature_level": {"id": "SRV-HeatedSteeringWheelStatus-hsw_temperature_level", "type": "DATA_STRING"}, "hsw_control_state": {"id": "SRV-HeatedSteeringWheelStatus-hsw_control_state", "type": "DATA_STRING"}}}, "9d1ea2070340c8e1fb592d4fa4b2cfbb": {"query_id": "Q730303008", "data_id_mapping": {"precondition_mode": {"id": "SRV-VehiclePreconditionState-precondition_mode", "type": "DATA_STRING"}, "precondition_status": {"id": "SRV-VehiclePreconditionState-precondition_status", "type": "DATA_STRING"}, "time_remaining": {"id": "SRV-VehiclePreconditionState-time_remaining", "type": "DATA_FLOAT"}}}, "2f3bf02a83b4735ac3ffe7d043030937": {"query_id": "Q730203003", "data_id_mapping": {"driver_door_status": {"id": "SRV-ApertureLockState-driver_door_status", "type": "DATA_STRING"}, "passenger_door_status": {"id": "SRV-ApertureLockState-passenger_door_status", "type": "DATA_STRING"}, "reardriver_door_status": {"id": "SRV-ApertureLockState-reardriver_door_status", "type": "DATA_STRING"}, "rearpassenger_door_status": {"id": "SRV-ApertureLockState-rearpassenger_door_status", "type": "DATA_STRING"}, "tailgate_status": {"id": "SRV-ApertureLockState-tailgate_status", "type": "DATA_STRING"}, "bonnet_status": {"id": "SRV-ApertureLockState-bonnet_status", "type": "DATA_STRING"}, "central_lock_status": {"id": "SRV-ApertureLockState-central_lock_status", "type": "DATA_STRING"}}}, "4f5b072b0c0803cc0d67bfa91f2ee9a0": {"query_id": "Q730203004", "data_id_mapping": {"driver_door_status": {"id": "SRV-ApertureLockState-driver_door_status", "type": "DATA_STRING"}, "passenger_door_status": {"id": "SRV-ApertureLockState-passenger_door_status", "type": "DATA_STRING"}, "reardriver_door_status": {"id": "SRV-ApertureLockState-reardriver_door_status", "type": "DATA_STRING"}, "rearpassenger_door_status": {"id": "SRV-ApertureLockState-rearpassenger_door_status", "type": "DATA_STRING"}, "tailgate_status": {"id": "SRV-ApertureLockState-tailgate_status", "type": "DATA_STRING"}, "bonnet_status": {"id": "SRV-ApertureLockState-bonnet_status", "type": "DATA_STRING"}, "central_lock_status": {"id": "SRV-ApertureLockState-central_lock_status", "type": "DATA_STRING"}}}, "a978006b1f2662dff0d05ea9564f0183": {"query_id": "Q730203005", "data_id_mapping": {"alarm_status": {"id": "SRV-VehicleAlarmState-alarm_status", "type": "DATA_STRING"}}}, "fcdba832ae8d3f48302a55584e1868ef": {"query_id": "Q730203006", "data_id_mapping": {"alarm_status": {"id": "SRV-VehicleAlarmState-alarm_status", "type": "DATA_STRING"}}}, "17852ab840615af2d7ecdafe43de483b": {"query_id": "Q730303009", "data_id_mapping": {"cabin_air_cleaning_status": {"id": "SRV-VehicleCabinAirCleanState-cabin_air_cleaning_status", "type": "DATA_STRING"}, "cabin_pm2_5_level": {"id": "SRV-VehicleCabinAirCleanState-cabin_pm2_5_level", "type": "DATA_FLOAT"}, "cabin_pm2_5_band": {"id": "SRV-VehicleCabinAirCleanState-cabin_pm2_5_band", "type": "DATA_FLOAT"}, "external_pm2_5_level": {"id": "SRV-VehicleCabinAirCleanState-external_pm2_5_level", "type": "DATA_FLOAT"}, "external_pm2_5_band": {"id": "SRV-VehicleCabinAirCleanState-external_pm2_5_band", "type": "DATA_FLOAT"}, "cabin_air_clean_cycles_remaining": {"id": "SRV-VehicleCabinAirCleanState-cabin_air_clean_cycles_remaining", "type": "DATA_FLOAT"}}}, "ea1a489f9f021df7a59d47f5a03604f1": {"query_id": "Q730303010", "data_id_mapping": {"cabin_air_cleaning_status": {"id": "SRV-VehicleCabinAirCleanState-cabin_air_cleaning_status", "type": "DATA_STRING"}, "cabin_pm2_5_level": {"id": "SRV-VehicleCabinAirCleanState-cabin_pm2_5_level", "type": "DATA_FLOAT"}, "cabin_pm2_5_band": {"id": "SRV-VehicleCabinAirCleanState-cabin_pm2_5_band", "type": "DATA_FLOAT"}, "external_pm2_5_level": {"id": "SRV-VehicleCabinAirCleanState-external_pm2_5_level", "type": "DATA_FLOAT"}, "external_pm2_5_band": {"id": "SRV-VehicleCabinAirCleanState-external_pm2_5_band", "type": "DATA_FLOAT"}, "cabin_air_clean_cycles_remaining": {"id": "SRV-VehicleCabinAirCleanState-cabin_air_clean_cycles_remaining", "type": "DATA_FLOAT"}}}}