[{"PropertyGroupId": "kafka.config", "PropertyMap": {"kafka.topic.ingest.name": "LIVE.data.product.notify.input", "kafka.topic.out.name": "LIVE.notify.output", "kafka.bootstrap.servers": "b-3.vcdpdataengineeringmsk.z9qmvi.c2.kafka.eu-west-2.amazonaws.com:9098,b-1.vcdpdataengineeringmsk.z9qmvi.c2.kafka.eu-west-2.amazonaws.com:9098,b-2.vcdpdataengineeringmsk.z9qmvi.c2.kafka.eu-west-2.amazonaws.com:9098", "kafka.vcdp.bootstrap.servers": "b-3.vcdpdataengineeringmsk.z9qmvi.c2.kafka.eu-west-2.amazonaws.com:9098,b-1.vcdpdataengineeringmsk.z9qmvi.c2.kafka.eu-west-2.amazonaws.com:9098,b-2.vcdpdataengineeringmsk.z9qmvi.c2.kafka.eu-west-2.amazonaws.com:9098", "kafka.topic.ingest.initial.position": "EARLIEST_OFFSET", "kafka.groupid": "data-engine-preprocessor"}}, {"PropertyGroupId": "job.config", "PropertyMap": {}}]