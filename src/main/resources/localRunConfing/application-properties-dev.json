[{"PropertyGroupId": "kafka.config", "PropertyMap": {"kafka.topic.ingest.name": "LIVE.data.product.notify.input", "kafka.topic.out.name": "LIVE.data-product-factory.input", "kafka.bootstrap.servers": "localhost:8097,localhost:8098,localhost:8099", "kafka.vcdp.bootstrap.servers": "localhost:8097,localhost:8098,localhost:8099", "kafka.topic.ingest.initial.position": "EARLIEST_OFFSET", "kafka.groupid": "data-engine-preprocessor"}}, {"PropertyGroupId": "job.config", "PropertyMap": {}}]