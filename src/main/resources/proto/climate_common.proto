// Protocol Buffers
// name - climate_common
// Description - Common type definitions used in services.

// Copyright 2023 Jaguar Land Rover Limited
syntax = "proto3";

package climate_common;

import "seats_common.proto";

// This is a nested message contains seat area,seat state and seat intensity.
message SeatClimateOperation {
    EnumSeatClimateArea seat_area = 1;
    EnumSeatClimateState seat_state = 2;
    EnumSeatClimateIntensity seat_climate_intensity = 3;
}

// This is a nested message allows the application to specify seat state for particular seat selection
message SeatClimateZoneState {
    seats_common.EnumSeatSelection seat_selection = 1;
    repeated SeatClimateOperation seat_operation = 2;
    optional EnumSeatClimateIntensity seat_climate_intensity = 3;
}

// Following enumeration specifies seat climate temperature controlled intensity
enum EnumSeatClimateIntensity {
    ENUM_SEAT_CLIMATE_INTENSITY_UNSPECIFIED = 0;
    ENUM_SEAT_CLIMATE_INTENSITY_OFF = 1;
    ENUM_SEAT_CLIMATE_INTENSITY_HEAT_LEVEL_1 = 2;
    ENUM_SEAT_CLIMATE_INTENSITY_COOL_LEVEL_1 = 3;
    ENUM_SEAT_CLIMATE_INTENSITY_HEAT_LEVEL_2 = 4;
    ENUM_SEAT_CLIMATE_INTENSITY_COOL_LEVEL_2 = 5;
    ENUM_SEAT_CLIMATE_INTENSITY_HEAT_LEVEL_3 = 6;
    ENUM_SEAT_CLIMATE_INTENSITY_COOL_LEVEL_3 = 7;
    ENUM_SEAT_CLIMATE_INTENSITY_HEAT_LEVEL_4 = 8;
    ENUM_SEAT_CLIMATE_INTENSITY_COOL_LEVEL_4 = 9;
    ENUM_SEAT_CLIMATE_INTENSITY_HEAT_LEVEL_5 = 10;
    ENUM_SEAT_CLIMATE_INTENSITY_COOL_LEVEL_5 = 11;
}

// Following enumeration specifies seat area
// When turning ON or turning OFF the Seat Climate, EnumSeatClimateArea shall not be populated.
// ENUM_SEAT_CLIMATE_AREA_ALL is used when want to specifiy all seat controls including peripherals,
// i.e. Cushion, Squab, Foot Rest, Arm Rest, Calf Rest.
enum EnumSeatClimateArea {
    ENUM_SEAT_CLIMATE_AREA_UNSPECIFIED = 0;
    ENUM_SEAT_CLIMATE_AREA_ALL = 1;
    ENUM_SEAT_CLIMATE_AREA_CUSHION = 2;
    ENUM_SEAT_CLIMATE_AREA_SQUAB = 3;
    // Door Arm rest is associated with left or right seat selection based on EnumSeatSelection values.
    ENUM_SEAT_CLIMATE_AREA_DOOR_ARM_REST = 4;
    // ENUM_SEAT_SELECTION_FIRST_ROW_CENTRE or ENUM_SEAT_SELECTION_SECOND_ROW_CENTRE shall be used
    // with ENUM_SEAT_CLIMATE_AREA_CENTER_ARM_REST as applicable.
    ENUM_SEAT_CLIMATE_AREA_CENTER_ARM_REST = 5;
    // Calf rest is only applicable to second row.
    ENUM_SEAT_CLIMATE_AREA_CALF_REST = 6;
    // There is only one Foot rest in the vehicle.
    // So while sending ENUM_SEAT_CLIMATE_AREA_FOOT_REST there is no need to populate seat selection.
    ENUM_SEAT_CLIMATE_AREA_FOOT_REST = 7;
}

// Following enumeration specifies seat area state
enum EnumSeatClimateState {
    ENUM_SEAT_CLIMATE_STATE_UNSPECIFIED = 0;
    ENUM_SEAT_CLIMATE_STATE_ON = 1;
    ENUM_SEAT_CLIMATE_STATE_OFF = 2;
    ENUM_SEAT_CLIMATE_STATE_INHIBIT = 3;
}

// Used to select and retrieve the status of Heated Steering Wheel.
enum EnumHSWTemperatureLevel {
    ENUM_HSW_TEMPERATURE_LEVEL_UNSPECIFIED = 0;
    ENUM_HSW_TEMPERATURE_LEVEL_OFF = 1;
    ENUM_HSW_TEMPERATURE_LEVEL_1 = 2;
    ENUM_HSW_TEMPERATURE_LEVEL_2 = 3;
}
