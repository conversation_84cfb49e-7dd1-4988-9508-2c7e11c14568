// Protocol Buffers
// service name - NGTP Adapter service
// Description - Service only applicable to EVA2 architecture.
// Service defined to provide interface to clients when
// vehicle is in PM0 (ie: vehicle is asleep)

// Following features are supported within this version
// Feature Name - Door Lock/Unlock
// Feature Name - Beep & Flash
// Feature Name - Vehicle Alarm Reset
// Feature Name - Cabin Air Cleaning
// Feature Name - Remote Preconditioning

// Copyright 2022 Jaguar Land Rover Limited

syntax = "proto3";

package ngtp_adpt_service;

/*****************************************************************************/
/*                                                                           */
/* The following section contains all the enumerations used for security     */
/* feature control and status                                                */
/*                                                                           */
/*****************************************************************************/
enum EnumStatus {
    ENUM_STATUS_UNSPECIFIED = 0;
    // Following enums indicate normal return status, no error in operation
    // and/or data is valid
    // Applies to all Response messages (Request/Response)
    // May apply to Notification messages (Pub/Sub) where data quality
    // indications need to be sent
    ENUM_STATUS_OK = 1;

    // Following enums indicate the quality of accompanying data
    // May apply to Response messages (Request/Response)
    // May apply to Notification messages (Pub/Sub)
    ENUM_STATUS_DATA_DEGRADED = 2;
    ENUM_STATUS_DATA_UNRELIABLE = 3;
    ENUM_STATUS_DATA_UNAVAILABLE = 4;

    // Following enums indicate operation errored and blocked due to some
    // ‘inhibiting condition’
    // Applies to Request/Response calls
    ENUM_STATUS_ERROR_INVALID_SERVICE_STATE = 5;
    ENUM_STATUS_ERROR_INVALID_VEHICLE_STATE = 6;
    ENUM_STATUS_ERROR_INVALID_CAR_CONFIGURATION = 7;

    // Following enums indicate message input field errors
    // Applies to Request/Response calls
    ENUM_STATUS_ERROR_MISSING_INPUT_FIELD = 8;
    ENUM_STATUS_ERROR_INVALID_INPUT_FIELD = 9;

    // Following enums indicate abnormal nonspecific status
    // May indicate that an operation or method execution cannot be performed
    // or has errored, or is blocked at this time
    // Applies to Request/Response calls
    ENUM_STATUS_NOT_OK = 10;
}

// This is used to indicate the operation that needs to be performed
enum EnumApertureOperation
{
    ENUM_APERTURE_OPERATION_UNSPECIFIED = 0; //Initialisation
    //External Unlock request depending on SPE/MPE setting: Exterior and Interior
    // Handles enabled, alarm will unset. Should be used by request source Exterior
    // or Telematics
    ENUM_APERTURE_OPERATION_EXTERNAL_UNLOCK = 1;
    //External Singlelock request: Exterior Handles inhibited, alarm will set.
    // Should be used by request source Exterior and Telematics.
    ENUM_APERTURE_OPERATION_EXTERNAL_SINGLELOCK = 2;
    //External Doublelock request: Exterior and Interior Handles inhibited,
    // alarm will set. Should be used by request source Exterior.
    ENUM_APERTURE_OPERATION_EXTERNAL_DOUBLELOCK = 3;
    // Central Unlock request for all apertures, inhibited when alarm set.
    // Should only be used by Request_Source_Interior
    ENUM_APERTURE_OPERATION_INTERNAL_CENTRAL_UNLOCK = 4;
    //Central Lock request for all apertures, inhibited when alarm set.
    // Should only be used by Request_Source_Interior
    ENUM_APERTURE_OPERATION_INTERNAL_CENTRAL_LOCK = 5;
}

// This enum is to indicate the source of the action (locking/unlocking)
enum EnumRequestSource {
    ENUM_REQUEST_SOURCE_UNSPECIFIED = 0;
    ENUM_REQUEST_SOURCE_EXTERIOR = 1; //NFC,RKE, Approach-Unlock, Walkaway-Lock etc. except RVI
    ENUM_REQUEST_SOURCE_TELEMATICS = 2; // Telematics-RemoteVehicleInteraction(RVI)
    ENUM_REQUEST_SOURCE_INTERIOR = 3; // IVI
}

// This enum is used when a specific aperture needs to be unlocked (Note: interior
// unlocking of a front passenger door or drivers door will unlock all doors,
// interior unlocking of one of the rear doors will only unlock that
// particular door)
enum EnumComplexLockingAction {
    ENUM_COMPLEX_LOCKING_ACTION_UNSPECIFIED = 0; // Initialisation
    ENUM_COMPLEX_LOCKING_ACTION_UNLOCK = 1;
    // Used when a specific aperture needs to be locked (Note: This action is
    //determined as a central lock request)
    ENUM_COMPLEX_LOCKING_ACTION_LOCK = 2;
}

// This enum is used to indicate the aperture status (lock/unlock)
enum EnumApertureLockStatus {
    ENUM_APERTURE_LOCK_STATUS_UNSPECIFIED = 0;
    ENUM_APERTURE_LOCK_STATUS_UNLOCKED = 1; // Aperture(s) status == unlocked
    ENUM_APERTURE_LOCK_STATUS_SINGLE_LOCKED = 2; // Aperture(s) status single locked
    ENUM_APERTURE_LOCK_STATUS_DOUBLE_LOCKED = 3; // Aperture(s) status double locked
}

// This enum is used to indicate the central lock status
enum EnumCentralLockStatus {
    ENUM_CENTRAL_LOCK_STATUS_UNSPECIFIED = 0;
    ENUM_CENTRAL_LOCK_STATUS_UNLOCKED = 1; // central lock status == unlocked
    ENUM_CENTRAL_LOCK_STATUS_PARTIAL_LOCKED = 2; // central lock status partial locked
    ENUM_CENTRAL_LOCK_STATUS_CENTRAL_LOCKED = 3; // central status central locked
    ENUM_CENTRAL_LOCK_STATUS_DOUBLE_LOCKED = 4; // central status double locked
}

// This is used to request a vehicle alarm function
enum EnumAlarmOperation
{
    ENUM_ALARM_OPERATION_UNSPECIFIED = 0;
    // to request the vehicle alarm to stop when triggered
    ENUM_ALARM_OPERATION_STOP = 1;
}

// This is used to indicate vehicle alarm status
enum EnumAlarmStatus {
    ENUM_ALARM_STATUS_UNSPECIFIED = 0;
    // to indicate the vehicle alarm is armed/set
    ENUM_ALARM_STATUS_ARMED = 1;
    // to indicate the vehicle alarm is disarmed/unset
    ENUM_ALARM_STATUS_DISARMED = 2;
    // to indicate the vehicle alarm is triggered
    ENUM_ALARM_STATUS_TRIGGERED = 3;
    // to indicate the vehicle alarm is faulty
    ENUM_ALARM_STATUS_FAULT = 4;
}

// This enum is used to request the cabin air functionality from the vehicle.
// The user term used for this feature is 'Deep Clean'.
enum EnumCabinAirCleanOperation
{
    ENUM_CABIN_AIR_CLEAN_OPERATION_UNSPECIFIED = 0;
    ENUM_CABIN_AIR_CLEAN_OPERATION_START = 1;
    ENUM_CABIN_AIR_CLEAN_OPERATION_STOP = 2;
}

// This enum is used to indicate the cabin air functionality in the vehicle.
// The user term used for this feature is 'Deep Clean'.
enum EnumCabinAirCleanStatus
{
    ENUM_CABIN_AIR_CLEAN_STS_UNSPECIFIED = 0;
    // CabinCleaningStatHVAC = No Error, CabinCleaningActivStat = Inactive
    ENUM_CABIN_AIR_CLEAN_STS_INACTIVE = 1;
    // CabinCleaningStatHVAC = No Error, CabinCleaningActivStat = Cycle Completed
    ENUM_CABIN_AIR_CLEAN_STS_COMPLETE = 2;
    // CabinCleaningStatHVAC = No Error, CabinCleaningActivStat = Cycle Started
    ENUM_CABIN_AIR_CLEAN_STS_IN_PROGRESS = 3;
    // CabinCleaningStatHVAC = User Stop Requested, CabinCleaningActivStat = Cycle Aborted - Incomplete
    ENUM_CABIN_AIR_CLEAN_STS_WARN_USER_STOP_REQUESTED = 4;
    // CabinCleaningStatHVAC = Failure - Low Battery, CabinCleaningActivStat = X
    ENUM_CABIN_AIR_CLEAN_STS_ERR_LOW_BATTERY = 5;
    // CabinCleaningStatHVAC = Failure - Windows/Doors open/vehicle not secured, CabinCleaningActivStat = X
    ENUM_CABIN_AIR_CLEAN_STS_ERR_VEHICLE_NOT_SECURE = 6;
    // CabinCleaningStatHVAC = Failure - Cleaning Cycles Exhausted, CabinCleaningActivStat = X
    ENUM_CABIN_AIR_CLEAN_STS_ERR_CLEANING_CYCLE_EXHAUSTED = 7;
    // CabinCleaningStatHVAC = Failure - System Faults, CabinCleaningActivStat = X
    ENUM_CABIN_AIR_CLEAN_STS_ERR_SYSTEM_FAULT = 8;
    // CabinCleaningStatHVAC = Failure - Power Mode Transition, CabinCleaningActivStat = X
    ENUM_CABIN_AIR_CLEAN_STS_ERR_VEHICLE_POWER_TRANSITION = 9;
    // CabinCleaningStatHVAC = Failure - Other Climate Feature Active/Requested, CabinCleaningActivStat = X
    ENUM_CABIN_AIR_CLEAN_STS_ERR_OTHER_FEATURE_ACTIVE = 10;
    // CabinCleaningStatHVAC = Failure - Crash Detected, CabinCleaningActivStat = X
    ENUM_CABIN_AIR_CLEAN_STS_ERR_CRASH_DETECTED = 11;
    // CabinCleaningStatHVAC = Failure - External Failure, CabinCleaningActivStat = X
    ENUM_CABIN_AIR_CLEAN_STS_ERR_EXTERNAL_FAILURE = 12;
    // CabinCleaningStatHVAC = Failure - Critical Service Active, CabinCleaningActivStat = X
    ENUM_CABIN_AIR_CLEAN_STS_ERR_CRITICAL_SERVICE_ACTIVE = 13; // svt, ecall, bcall
    // CabinCleaningStatHVAC = Failure - SOTA in  Progress, CabinCleaningActivStat = X
    ENUM_CABIN_AIR_CLEAN_STS_ERR_SW_UPDATE_IN_PROGRESS = 14;
}

// This enum is used to request vehicle preconditioning functionality from the
// vehicle
enum EnumPreconditionOperation
{
    ENUM_PRECONDITION_OPERATION_UNSPECIFIED = 0;
    // start request from remote app
    ENUM_PRECONDITION_OPERATION_OFFBOARD_START = 1;
    // stop request from remote app
    ENUM_PRECONDITION_OPERATION_OFFBOARD_STOP = 2;
}

// This enum is used to indicate the preconditioning mode that the vehicle is in
enum EnumPreConditionCurrentMode
{
    ENUM_PRECONDITION_CURRENT_MODE_UNSPECIFIED = 0;
    ENUM_PRECONDITION_CURRENT_MODE_INACTIVE = 1;
    ENUM_PRECONDITION_CURRENT_MODE_IMMEDIATE = 2;
    ENUM_PRECONDITION_CURRENT_MODE_TIMED = 3;
}

// This enum is used to indicate vehicle preconditioning functionality in the
// vehicle
enum EnumPreConditionStatus
{
    ENUM_PRECONDITION_STS_UNSPECIFIED = 0;
    // PreClimateStatus = PreClim off
    ENUM_PRECONDITION_STS_OFF = 1;
    // PreClimateStatus = PreClim starting up
    ENUM_PRECONDITION_STS_START_UP = 2;
    // PreClimateStatus = PreClim or PreClim Grace Period
    ENUM_PRECONDITION_STS_IN_PROGRESS = 3;
    // PreClimateStatus = PreClim Finished
    ENUM_PRECONDITION_STS_COMPLETE = 4;
    // PreClimateStatus = PreClim Finished Partial Precon
    //precon completed but temperature not reached
    ENUM_PRECONDITION_STS_PARTIAL_COMPLETE = 5;
    // PreClimateStatus = PreClim Inhibit HV power
    ENUM_PRECONDITION_STS_ERR_LOW_BATTERY = 6;
    // PreClimateStatus = PreClim Inhibited system fault
    ENUM_PRECONDITION_STS_ERR_SYSTEM_FAULT = 7;
}

enum EnumBeepFlashOperation
{
    ENUM_BEEP_FLASH_OPERATION_UNSPECIFIED = 0;
    ENUM_BEEP_FLASH_OPERATION_ON = 1;
    ENUM_BEEP_FLASH_OPERATION_OFF = 2;
}

/*****************************************************************************/
/*                                                                           */
/* The following section contains all the nested messages which need to be   */
/* used in various methods and notifications                                 */
/*                                                                           */
/*****************************************************************************/
// This is a repeated message used to define the locking requests for individual DOORs
message DoorLockingOperation {
    EnumComplexLockingAction driver_door = 1;
    EnumComplexLockingAction passenger_door = 2;
    EnumComplexLockingAction rear_driver_door = 3;
    EnumComplexLockingAction rear_passenger_door = 4;
}

/*****************************************************************************/
/*                                                                           */
/* The following section contains all the methods which need to be used      */
/* for security feature control and status                                   */
/*                                                                           */
/*****************************************************************************/
// This operation allows the caller to set the locking state of the vehicle
message SetApertureLockStateRequest {
    EnumRequestSource request_source = 1;
    oneof locking_request {
        EnumApertureOperation aperture_operation = 2;
        DoorLockingOperation individual_door_locking_request = 3;
    }
}

// This operation is used to indicate the success or failure of the
// 'set aperture lock state' request
message SetApertureLockStateResponse {
    EnumStatus status = 1;
}

// This operation is used to request for the lock state of the apertures
// in the vehicle
message GetApertureLockStateRequest {
    // no parameter
}

// This operation actually returns the status of all the apertures in the
// vehicle
message GetApertureLockStateResponse {
    EnumStatus status = 1;
    EnumApertureLockStatus driver_door_status = 2;
    EnumApertureLockStatus passenger_door_status = 3;
    EnumApertureLockStatus reardriver_door_status = 4;
    EnumApertureLockStatus rearpassenger_door_status = 5;
    EnumApertureLockStatus tailgate_status = 6;
    EnumApertureLockStatus bonnet_status = 7;
    EnumCentralLockStatus central_lock_status = 8;
}

// This operation allows the caller to request a beep and flash from the vehicle
// This is a fire and forget request since no confirmation is expected back from
// the target ECU
message SetBeepAndFlashRequest {
    EnumBeepFlashOperation beep_flash_operation = 1;
}

// This operation is used to indicate the success or failure of the
// 'beep and flash' request
message SetBeepAndFlashResponse {
    EnumStatus status = 1;
}

// This operation allows the caller to request reset of vehicle alarm from the vehicle
// This is a fire and forget request since no confirmation is expected back from
// the target ECU
message SetVehicleAlarmRequest {
    EnumAlarmOperation alarm_operation = 1;
}

// This operation is used to indicate the success or failure of the
// 'reset alarm' request
message SetVehicleAlarmResponse {
    EnumStatus status = 1;
}

// This operation allows the caller to get the status of vehicle alarm
// This is a fire and forget request since no confirmation is expected back from
// the target ECU
message GetVehicleAlarmStateRequest {
    // no parameter
}

// This operation is used to indicate the status of the
// vehicle alarm
message GetVehicleAlarmStateResponse {
    EnumAlarmStatus alarm_status = 1;
    EnumStatus status = 2;
}

// This enum is used to request the cabin air functionality from the vehicle.
// The user term used for this feature is 'Deep Clean' and it is expected
// to run for a pre-defined amount of time
message SetVehicleCabinAirCleanRequest {
    EnumCabinAirCleanOperation cabin_air_cleaning_request = 1;
}

// This operation is used to indicate the success or failure of the
// set 'cabin air clean' request
message SetVehicleCabinAirCleanResponse {
    EnumStatus status = 1;
}

// This enum is used to request the cabin air functionality status from the vehicle.
// The user term used for this feature is 'Deep Clean' and it is expected
// to run for a pre-defined amount of time
message GetVehicleCabinAirCleanStateRequest {
    // no parameter
}

// This operation is used to indicate the success or failure of the
// get 'cabin air clean' request
message GetVehicleCabinAirCleanStateResponse {
    EnumCabinAirCleanStatus cabin_air_cleaning_status = 1;
    // This is used to indicate the live in-cabin pm2.5 levels
    optional uint32 cabin_pm2_5_level = 2;
    // This is used to indicate the band appropriate to the cabin pm2.5 level
    optional uint32 cabin_pm2_5_band = 3;
    // This is used to indicate the live external pm2.5 levels
    optional uint32 external_pm2_5_level = 4;
    // This is used to indicate the band appropriate to the external pm2.5 level
    optional uint32 external_pm2_5_band = 5;
    EnumStatus status = 6;
    // Placeholder: This is used to indicate the number of cleaning cycles reamining
    // This data is not available on MLA BEV
    optional uint32 cabin_air_clean_cycles_remaining = 7;
}

// This enum is used to request the precondition from the vehicle.
message SetVehiclePreconditionRequest {
    EnumPreconditionOperation precondition_request = 1;
    // This is used to request the target temperature to set the vehicle to
    // The unit used for this request is deg centigrade
    // Logical value is 0 to 31 deg centigrade in 0.5 steps
    // if the value is not on 0.5 boundary then the physical value shall be truncated
    // after the scale and offset is applied
    // there might be some rounding off with float values recived from client for eg: 20.5 can be 20.499 or 20.511
    // The scale factor and offset will be applied to the recived value
    // for eg: 20.499 is rceived then, it will be rounded to 20.5 * 2 (multiplied by SF=0.5) = 41
    optional float precondition_target_temperature = 2;
}

// This operation is used to indicate the success or failure of the
// set precondition request
message SetVehiclePreconditionResponse {
    EnumStatus status = 1;
}

// This enum is used to request the precondition from the vehicle.
message GetVehiclePreconditionStateRequest {
    // no parameter
}

// This operation is used to indicate the success or failure of the
// get precondition request
message GetVehiclePreconditionStateResponse {
    EnumPreConditionCurrentMode precondition_mode = 1;
    EnumPreConditionStatus precondition_status = 2;
    // fixed preconditioning time in minutes to display the progress if needed
    // this will only provide the time and the countdown timer should be implemented by the client
    optional uint32 time_remaining = 3;
    EnumStatus status = 4;
}

/*****************************************************************************/
/*                                                                           */
/* The following section contains all the notifications which will be sent   */
/* when a change is seen in the received network data                        */
/*                                                                           */
/*****************************************************************************/
// This notification is sent on change of the lock state of the vehicle
message NotifyApertureLockState {
    EnumStatus status = 1;
    EnumApertureLockStatus driver_door_status = 2;
    EnumApertureLockStatus passenger_door_status = 3;
    EnumApertureLockStatus reardriver_door_status = 4;
    EnumApertureLockStatus rearpassenger_door_status = 5;
    EnumApertureLockStatus tailgate_status = 6;
    EnumApertureLockStatus bonnet_status = 7;
    EnumCentralLockStatus central_lock_status = 8;
}

message NotifyBeepAndFlashState {
    EnumStatus status = 1;
}

// This notification is sent on change of the lock state of the vehicle
message NotifyVehicleAlarmState {
    EnumAlarmStatus alarm_status = 1;
    EnumStatus status = 2;
}

// This notification is sent on change of the cabin air functionality of the vehicle
message NotifyVehicleCabinAirCleanState {
    EnumCabinAirCleanStatus cabin_air_cleaning_status = 1;
    // This is used to indicate the live in-cabin pm2.5 levels
    optional uint32 cabin_pm2_5_level = 2;
    // This is used to indicate the band appropriate to the cabin pm2.5 level
    optional uint32 cabin_pm2_5_band = 3;
    // This is used to indicate the live external pm2.5 levels
    optional uint32 external_pm2_5_level = 4;
    // This is used to indicate the band appropriate to the external pm2.5 level
    optional uint32 external_pm2_5_band = 5;
    EnumStatus status = 6;
    // Placeholder: This is used to indicate the number of cleaning cycles reamining
    // This data is not available on MLA BEV
    optional uint32 cabin_air_clean_cycles_remaining = 7;
}

// This notification is sent on change of the precondition functionality of the vehicle
message NotifyVehiclePreconditionState {
    EnumPreConditionCurrentMode precondition_mode = 1;
    EnumPreConditionStatus precondition_status = 2;
    // fixed preconditioning time in minutes to display the progress if needed
    // this will only provide the time and the countdown timer should be implemented by the client
    optional uint32 time_remaining = 3;
    EnumStatus status = 4;
}
