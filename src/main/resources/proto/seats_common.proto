// Protocol Buffers
// name - camera_common
// Description - Common type definitions used in camera services.

// Copyright 2023 Jaguar Land Rover Limited
syntax = "proto3";

package seats_common;

// Following enumeration specifies the seat location
enum EnumSeatSelection {
    ENUM_SEAT_SELECTION_UNSPECIFIED = 0;
    ENUM_SEAT_SELECTION_ALL = 1;
    ENUM_SEAT_SELECTION_FIRST_ROW_LEFT = 2;
    ENUM_SEAT_SELECTION_FIRST_ROW_RIGHT = 3;
    ENUM_SEAT_SELECTION_FIRST_ROW_CENTRE = 4;
    ENUM_SEAT_SELECTION_FIRST_ROW_ALL = 5;
    ENUM_SEAT_SELECTION_SECOND_ROW_LEFT = 6;
    <PERSON><PERSON><PERSON>_SEAT_SELECTION_SECOND_ROW_RIGHT = 7;
    ENUM_SEAT_SELECTION_SECOND_ROW_CENTRE = 8;
    ENUM_SEAT_SELECTION_SECOND_ROW_ALL = 9;
    ENUM_SEAT_SELECTION_THIRD_ROW_LEFT = 10;
    ENUM_SEAT_SELECTION_THIRD_ROW_RIGHT = 11;
    ENUM_SEAT_SELECTION_THIRD_ROW_CENTRE = 12;
    ENUM_SEAT_SELECTION_THIRD_ROW_ALL = 13;
    ENUM_SEAT_SELECTION_REAR_ALL = 14;
}
