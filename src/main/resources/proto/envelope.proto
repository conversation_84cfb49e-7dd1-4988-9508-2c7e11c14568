// Protocol Buffers
// service name - Message Envelope
// Description - Message envelope is applicable to all architectures, but only for use between cloud and the vehicle
// Service defined to provide interface to clients when vehicle is in PM0 (ie: vehicle is asleep)

// Following features are supported within this version
// Feature Name - Message Envelope

// Copyright 2023 Jaguar Land Rover Limited


syntax = "proto3";

package gateway_service;

// Enum returned from any request indicating success or failure
enum EnumOperationStatus {
    ENUM_OPERATION_STATUS_UNSPECIFIED = 0;
    // Normal return status indication, no error in operation and/or data is valid
    ENUM_OPERATION_STATUS_OK = 1;
    // Error in requested service due to absence of Service hash in configuration file
    ENUM_OPERATION_STATUS_HASH_NOT_FOUND = 2;
    // Return status when the service requested is not available within the
    // wait_for_service_in_ms time interval
    ENUM_OPERATION_STATUS_SERVICE_NOT_AVAILABLE = 3;
    // Return status when theere is a failure in security requirements for the service request
    ENUM_OPERATION_STATUS_SECURITY_FAILURE = 4;
    // Return status when the request is out of sequence and therefore not executed
    ENUM_OPERATION_STATUS_REQUEST_OUT_OF_SEQUENCE = 5;
    // Return status when the request is out of required time frame as specified in JWT
    ENUM_OPERATION_STATUS_REQUEST_EXPIRED = 6;
    // Return status when the request was not completed in the time limit given in the request
    ENUM_OPERATION_STATUS_REQUEST_TIMED_OUT = 7;
    // Return status when a network activation failed to be completed in the time limit given in the request
    ENUM_OPERATION_STATUS_REQUEST_NETWORK_ACTIVATION_FAILED = 8;
    // Unspecified error has occurred
    ENUM_OPERATION_STATUS_OPERATION_FAILED = 9;
    // Invalid vehicle unique id provided in request
    ENUM_OPERATION_STATUS_INVALID_VUID = 10;
}

// Enum to specify the Network Demand action to be taken by Service Gateway or RFC
enum EnumNetworkDemandAction {
    ENUM_NETWORD_DEMAND_ACTION_UNSPECIFIED = 0;
    // Action indicating to RFC to acquire Network Demand
    ENUM_NETWORK_DEMAND_ACTION_ACQUIRE = 1;
    // Action indicating to RFC to release Network Demand
    ENUM_NETWORK_DEMAND_ACTION_RELEASE = 2;
}

// The hash in all the messages will be identical to the hash in the configuration file recieved by the service
// gateway. This will be a 16 byte binary MD5 sum that is base64 encoded with padding characters ("=") removed,
// resulting in a 22 byte hash as a string.

// Subscribe to an asychronous vehicle event
// Any data received from the event will be published to VCDP using the EventMessage
message SubscribeMessageRequest {
    // The hash identifier used to lookup the required service in the vehicle service gateway configuration file
    optional string hash = 1;
    // The number of milliseconds for the service gateway to wait for a service to become available
    // Default 100ms if not set, if service is not available within requested time the request is not actioned
    // and a response of ENUM_OPERATION_STATUS_SERVICE_NOT_AVAILABLE is returned
    optional uint32 wait_for_service_in_ms = 2;
}

// The response to a SubscribeMessageRequest
message SubscribeMessageResponse {
    // The status result of the request
    EnumOperationStatus result = 1;
    // The hash identifier used to determine the SubscribeRequest this response relates to.
    optional string hash = 2;
}

// UnSubscribe from an asychronous vehicle event
message UnsubscribeMessageRequest {
    // The hash identifier used to lookup the required service in the vehicle service gateway configuration file
    optional string hash = 1;
    // Note the UnsubscribeMessageRequest does not include a wait_for_service_in_ms because the service should already
    // be available from when the SubscribeMessageRequest was called
}

// The response to a UnsubscribeMessageRequest
message UnsubscribeMessageResponse {
    // The status result of the request
    EnumOperationStatus result = 1;
    // The hash identifier used to determine the UnsubscribeRequest this response relates to.
    optional string hash = 2;
}

// Service Request by VCDP to vehicle service gateway
message RequestMessage {
    // The hash identifier used to lookup the required service in the vehicle service gateway configuration file
    optional string hash = 1;
    // An opaque identifier to identify a unique request, returned in corresponding response
    optional uint32 request_context = 2;
    // The number of milliseconds for the service gateway to wait for a service to become available
    // Default 100ms if not set, if service is not available within requested time the request is not actioned
    // and a response of ENUM_OPERATION_STATUS_SERVICE_NOT_AVAILABLE is returned
    optional uint32 wait_for_service_in_ms = 3;
    // Required. The number of milliseconds for the service gateway to wait for the specified service to respond
    // If the service does not respond within the timeout the service gateway will discard any context, therefore if
    // the service responds after the timeout the service gateway will not update the failure response even though
    // the service may have succeeded
    optional uint32 response_timeout_ms = 4;
    // Protobuf encoded message payload sent to the service
    optional bytes message_payload = 5;
}

// The response to a RequestMessage
message ResponseMessage {
    // The status result of the request
    EnumOperationStatus result = 1;
    // An opaque identifier to identify the request message this response relates to.
    optional uint32 request_context = 2;
    // The hash identifier used to determine the event this message relates to.
    optional string hash = 3;
    // Protobuf encoded message payload returned from the service
    optional bytes message_payload = 4;
}

// Data received from any event that will be published to VCDP
message EventMessage {
    // The hash identifier used to determine the event this message relates to.
    optional string hash = 1;
    // Protobuf encoded message payload provided by the service
    optional bytes message_payload = 2;
}

// RFC messages are used to change the network and service availablity using CVLC
// and then provide another GatewayRequestMessage that is used to implement the request
// Note, normally there will be no response from an RFCRequestMessage because the response will be generated from
// either the SubscribeMessageRequest or RequestMessage
message RFCRequestMessage {
    // An opaque identifier to identify a unique request, returned in corresponding response
    optional uint32 request_context = 1;
    // The network demand action to be taken by RFC or Service Gateway
    EnumNetworkDemandAction network_action = 2;
    // Network demand required by RFC to make the relevant CVLC request
    optional uint32 network_demand = 3;
    // The time for RFC to keep the network demand alive
    optional uint32 network_demand_time_ms = 4;
    // The gateway request message to forward to service gateway on successful wakeup of any services
    oneof message {
        SubscribeMessageRequest subscribe_request = 5;
        RequestMessage request_message = 6;
    }
}

// The response to a failed RFCRequestMessage
// Note, this will only be sent if the RFC request failed because the service gateway request
// message being dropped and therefore have no response
message RFCResponseMessage {
    // The status result of the request
    EnumOperationStatus result = 1;
    // An opaque identifier to identify the RFC request message this response relates to.
    optional uint32 request_context = 2;
}

// Request messages received from the cloud backend
message GatewayRequestMessage {
    oneof message {
        SubscribeMessageRequest subscribe_request = 1;
        UnsubscribeMessageRequest unsubscribe_request = 2;
        RequestMessage request_message = 3;
        RFCRequestMessage rfc_message = 4;
    };
    // The message creation time in UTC seconds from UNIX epoch
    optional uint64 creation_time = 5;
    // Time to live in seconds from creation time
    // If request is received after the time to live has expired then the request will not be processed
    // and the the response will be ENUM_OPERATION_STATUS_REQUEST_EXPIRED
    optional uint32 time_to_live = 6;
    // Sequence number increments with every request
    // If a sequence number is received that is less than or equal to the last sequence number received then the request
    // will not be processed and the response will be NUM_OPERATION_STATUS_REQUEST_OUT_OF_SEQUENCE
    // Sequence number will start at 0 whenever the service gateway is restarted and will be fast-forwarded to the value of
    // the first request when it is received
    optional uint32 sequence_no = 7;
    // Vehicle unique id, used to ensure that the request is for this vehicle and this vehicle alone
    // Will be the unique id that is provided to OBG during OBG's authentication process
    // If the vuid is not correct then the request will not be processed and the response will be ENUM_OPERATION_STATUS_INVALID_VUID
    optional string vuid = 8;
}

// Response messages sent to the cloud backend in response to one of the requests
message GatewayResponseMessage {
    oneof message {
        SubscribeMessageResponse subscribe_response = 1;
        UnsubscribeMessageResponse unsubscribe_response = 2;
        ResponseMessage response_message = 3;
        EventMessage event_message = 4;
        RFCResponseMessage rfc_response = 5;
    };
    optional uint64 timestamp_ms = 6;//Time in ms from UNIX epoch
}
