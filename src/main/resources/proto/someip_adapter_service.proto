// Protocol Buffers
// service name - SOME/IP Adapter service
// Description - EVA2 based service defined to provide SOME/IP interfaces
// to all functions required by offboard

// Following features are supported within this version
// Feature Name - Charge Start/Stop/Schedule
// Feature Name - Charging Feedback
// Feature Name - Customer Selectable State of Charge
// Feature Name - Charging information while charging
// Feature Name - Set Max A/C Charge Rate Limit
// Feature Name - Predicted Time To Charge
// Feature Name - Remote Charge Door Opening
// Feature Name - V1G Smart Charging
// Feature Name - Plug N Charge
// Feature Name - Charge cable unlock
// Feature Name - Charge Troubleshooter
// Feature Name - Charging summary (only energy added supported on MLA BEV)
// Feature Name - Charge Mode Toggle
// Feature Name - Insufficient Charging period(Depature time)
// Feature Name - HV battery information (Battery energy loss APIs not supported in MLA BEV)
// Feature Name - Plug and charge Session ID

// Copyright 2022 Jaguar Land Rover Limited

syntax = "proto3";

package someip_adpt_service;

import "charging_common.proto";
import "climate_common.proto";
import "seats_common.proto";

/*****************************************************************************/
/*                                                                           */
/* The following section contains all the enumerations used for hvac         */
/* feature control and status                                                */
/*                                                                           */
/*****************************************************************************/
// Used to select and retrieve the status of Heated Steering Wheel.
enum EnumHSWTemperatureLevel {
    ENUM_HSW_TEMPERATURE_LEVEL_UNSPECIFIED = 0;
    ENUM_HSW_TEMPERATURE_LEVEL_OFF = 1;
    ENUM_HSW_TEMPERATURE_LEVEL_1 = 2;
    ENUM_HSW_TEMPERATURE_LEVEL_2 = 3;
}

// This is a nested message contains seat area,seat state and seat intensity.
message SeatClimateOperation {
    EnumSeatClimateArea seat_area = 1;
    EnumSeatClimateState seat_state = 2;
    EnumSeatClimateIntensity seat_climate_intensity = 3;
}

// Following enumeration specifies seat climate temperature controlled intensity
enum EnumSeatClimateIntensity {
    ENUM_SEAT_CLIMATE_INTENSITY_UNSPECIFIED = 0;
    ENUM_SEAT_CLIMATE_INTENSITY_OFF = 1;
    ENUM_SEAT_CLIMATE_INTENSITY_HEAT_LEVEL_1 = 2;
    ENUM_SEAT_CLIMATE_INTENSITY_COOL_LEVEL_1 = 3;
    ENUM_SEAT_CLIMATE_INTENSITY_HEAT_LEVEL_2 = 4;
    ENUM_SEAT_CLIMATE_INTENSITY_COOL_LEVEL_2 = 5;
    ENUM_SEAT_CLIMATE_INTENSITY_HEAT_LEVEL_3 = 6;
    ENUM_SEAT_CLIMATE_INTENSITY_COOL_LEVEL_3 = 7;
    ENUM_SEAT_CLIMATE_INTENSITY_HEAT_LEVEL_4 = 8;
    ENUM_SEAT_CLIMATE_INTENSITY_COOL_LEVEL_4 = 9;
    ENUM_SEAT_CLIMATE_INTENSITY_HEAT_LEVEL_5 = 10;
    ENUM_SEAT_CLIMATE_INTENSITY_COOL_LEVEL_5 = 11;
}

// This enum is used to indicate if the any control is inhibted for user interaction
enum EnumControlState
{
    ENUM_CONTROL_STATE_UNSPECIFIED = 0;
    ENUM_CONTROL_STATE_INHIBITED = 1;
    ENUM_CONTROL_STATE_AVAILABLE = 2;
}

// Following enumeration specifies seat area
//DONT_CARE is used when turning on the Seat Climate for the seat but dont want to specify the area.
//Area_ALL is used when want to specifiy all peripherals of the seat like Cushion, Squab , Foot Rest , Arm Rest , Calf Rest.
enum EnumSeatClimateArea {
    ENUM_SEAT_CLIMATE_AREA_UNSPECIFIED = 0;
    ENUM_SEAT_CLIMATE_AREA_ALL = 1;
    ENUM_SEAT_CLIMATE_AREA_CUSHION = 2;
    ENUM_SEAT_CLIMATE_AREA_SQUAB = 3;
}

// Following enumeration specifies seat area state
enum EnumSeatClimateState {
    ENUM_SEAT_CLIMATE_STATE_UNSPECIFIED = 0;
    ENUM_SEAT_CLIMATE_STATE_ON = 1;
    ENUM_SEAT_CLIMATE_STATE_OFF = 2;
    ENUM_SEAT_CLIMATE_STATE_INHIBIT = 3;
}

// This enum is used to request vehicle preconditioning functionality from the
// vehicle
enum EnumPreconditionOperation
{
    ENUM_PRECONDITION_OPERATION_UNSPECIFIED = 0;
    // start request from remote app
    ENUM_PRECONDITION_OPERATION_OFFBOARD_START = 1;
    // stop request from remote app
    ENUM_PRECONDITION_OPERATION_OFFBOARD_STOP = 2;
}

// This enum is used to indicate the preconditioning mode that the vehicle is in
enum EnumPreConditionCurrentMode
{
    ENUM_PRECONDITION_CURRENT_MODE_UNSPECIFIED = 0;
    ENUM_PRECONDITION_CURRENT_MODE_INACTIVE = 1;
    ENUM_PRECONDITION_CURRENT_MODE_IMMEDIATE = 2;
    ENUM_PRECONDITION_CURRENT_MODE_TIMED = 3;
}

// This enum is used to indicate vehicle preconditioning functionality in the
// vehicle
enum EnumPreConditionStatus
{
    ENUM_PRECONDITION_STS_UNSPECIFIED = 0;
    // PreClimateStatus = PreClim off
    ENUM_PRECONDITION_STS_OFF = 1;
    // PreClimateStatus = PreClim starting up
    ENUM_PRECONDITION_STS_START_UP = 2;
    // PreClimateStatus = PreClim or PreClim Grace Period
    ENUM_PRECONDITION_STS_IN_PROGRESS = 3;
    // PreClimateStatus = PreClim Finished
    ENUM_PRECONDITION_STS_COMPLETE = 4;
    // PreClimateStatus = PreClim Finished Partial Precon
    //precon completed but temperature not reached
    ENUM_PRECONDITION_STS_PARTIAL_COMPLETE = 5;
    // PreClimateStatus = PreClim Inhibit HV power
    ENUM_PRECONDITION_STS_ERR_LOW_BATTERY = 6;
    // PreClimateStatus = PreClim Inhibited system fault
    ENUM_PRECONDITION_STS_ERR_SYSTEM_FAULT = 7;
}

enum EnumEventTimeType
{
    ENUM_EVENT_TIME_TYPE_UNSPECIFIED = 0;
    ENUM_EVENT_TIME_TYPE_BEGIN = 1;
    ENUM_EVENT_TIME_TYPE_END = 2;
}

// This enum is used to indicate the aperture status (lock/unlock)
enum EnumApertureLockStatus {
    ENUM_APERTURE_LOCK_STATUS_UNSPECIFIED = 0;
    ENUM_APERTURE_LOCK_STATUS_UNLOCKED = 1; // Aperture(s) status == unlocked
    ENUM_APERTURE_LOCK_STATUS_SINGLE_LOCKED = 2; // Aperture(s) status single locked
    ENUM_APERTURE_LOCK_STATUS_DOUBLE_LOCKED = 3; // Aperture(s) status double locked
}

// This enum is used to indicate the central lock status
enum EnumCentralLockStatus {
    ENUM_CENTRAL_LOCK_STATUS_UNSPECIFIED = 0;
    ENUM_CENTRAL_LOCK_STATUS_UNLOCKED = 1; // central lock status == unlocked
    ENUM_CENTRAL_LOCK_STATUS_PARTIAL_LOCKED = 2; // central lock status partial locked
    ENUM_CENTRAL_LOCK_STATUS_CENTRAL_LOCKED = 3; // central status central locked
    ENUM_CENTRAL_LOCK_STATUS_DOUBLE_LOCKED = 4; // central status double locked
}

// This is used to indicate vehicle alarm status
enum EnumAlarmStatus {
    ENUM_ALARM_STATUS_UNSPECIFIED = 0;
    // to indicate the vehicle alarm is armed/set
    ENUM_ALARM_STATUS_ARMED = 1;
    // to indicate the vehicle alarm is disarmed/unset
    ENUM_ALARM_STATUS_DISARMED = 2;
    // to indicate the vehicle alarm is triggered
    ENUM_ALARM_STATUS_TRIGGERED = 3;
    // to indicate the vehicle alarm is faulty
    ENUM_ALARM_STATUS_FAULT = 4;
}

// This enum is used to indicate the cabin air functionality in the vehicle.
// The user term used for this feature is 'Deep Clean'.
enum EnumCabinAirCleanStatus
{
    ENUM_CABIN_AIR_CLEAN_STS_UNSPECIFIED = 0;
    // CabinCleaningStatHVAC = No Error, CabinCleaningActivStat = Inactive
    ENUM_CABIN_AIR_CLEAN_STS_INACTIVE = 1;
    // CabinCleaningStatHVAC = No Error, CabinCleaningActivStat = Cycle Completed
    ENUM_CABIN_AIR_CLEAN_STS_COMPLETE = 2;
    // CabinCleaningStatHVAC = No Error, CabinCleaningActivStat = Cycle Started
    ENUM_CABIN_AIR_CLEAN_STS_IN_PROGRESS = 3;
    // CabinCleaningStatHVAC = User Stop Requested, CabinCleaningActivStat = Cycle Aborted - Incomplete
    ENUM_CABIN_AIR_CLEAN_STS_WARN_USER_STOP_REQUESTED = 4;
    // CabinCleaningStatHVAC = Failure - Low Battery, CabinCleaningActivStat = X
    ENUM_CABIN_AIR_CLEAN_STS_ERR_LOW_BATTERY = 5;
    // CabinCleaningStatHVAC = Failure - Windows/Doors open/vehicle not secured, CabinCleaningActivStat = X
    ENUM_CABIN_AIR_CLEAN_STS_ERR_VEHICLE_NOT_SECURE = 6;
    // CabinCleaningStatHVAC = Failure - Cleaning Cycles Exhausted, CabinCleaningActivStat = X
    ENUM_CABIN_AIR_CLEAN_STS_ERR_CLEANING_CYCLE_EXHAUSTED = 7;
    // CabinCleaningStatHVAC = Failure - System Faults, CabinCleaningActivStat = X
    ENUM_CABIN_AIR_CLEAN_STS_ERR_SYSTEM_FAULT = 8;
    // CabinCleaningStatHVAC = Failure - Power Mode Transition, CabinCleaningActivStat = X
    ENUM_CABIN_AIR_CLEAN_STS_ERR_VEHICLE_POWER_TRANSITION = 9;
    // CabinCleaningStatHVAC = Failure - Other Climate Feature Active/Requested, CabinCleaningActivStat = X
    ENUM_CABIN_AIR_CLEAN_STS_ERR_OTHER_FEATURE_ACTIVE = 10;
    // CabinCleaningStatHVAC = Failure - Crash Detected, CabinCleaningActivStat = X
    ENUM_CABIN_AIR_CLEAN_STS_ERR_CRASH_DETECTED = 11;
    // CabinCleaningStatHVAC = Failure - External Failure, CabinCleaningActivStat = X
    ENUM_CABIN_AIR_CLEAN_STS_ERR_EXTERNAL_FAILURE = 12;
    // CabinCleaningStatHVAC = Failure - Critical Service Active, CabinCleaningActivStat = X
    ENUM_CABIN_AIR_CLEAN_STS_ERR_CRITICAL_SERVICE_ACTIVE = 13; // svt, ecall, bcall
    // CabinCleaningStatHVAC = Failure - SOTA in  Progress, CabinCleaningActivStat = X
    ENUM_CABIN_AIR_CLEAN_STS_ERR_SW_UPDATE_IN_PROGRESS = 14;
}

/*****************************************************************************/
/*                                                                           */
/* The following section contains all the nested messages which need to be   */
/* used in various methods and notifications                                 */
/*                                                                           */
/*****************************************************************************/
// This is a nested message allows the application to specify seat state for particular seat selection
message SeatClimateZoneState {
    seats_common.EnumSeatSelection seat_selection = 1;
    repeated SeatClimateOperation seat_operation = 2;
    optional EnumSeatClimateIntensity seat_climate_intensity = 3;
}

message DepartureTaskPreconditioning
{
    // This is used to request the target temperature to set the vehicle to
    // The unit used for this request is deg centigrade
    // Logical value is 0 to 31 deg centigrade in 0.5 steps
    // if the value is not on 0.5 boundary then the physical value shall be truncated
    // after the scale and offset is applied
    // there might be some rounding off with float values recived from client for eg: 20.5 can be 20.499 or 20.511
    // The scale factor and offset will be applied to the recived value
    // for eg: 20.499 is received then, it will be rounded to 20.5 * 2 (multiplied by SF=0.5) = 41
    optional float precondition_target_temperature = 1;

    // This is used to request the seat climate to set the vehicle to
    repeated climate_common.SeatClimateZoneState seat_climate_zone = 2;

    // This is used to request the heated steering wheel temperature level to set the vehicle to
    optional climate_common.EnumHSWTemperatureLevel hsw_temperature_level = 3;
}

message DepartureTaskCabinClean
{
    // no paramters
}

message DepartureTask
{
    oneof value
    {
        // preconditioning: Includes cabin/battery preconditioning as required
        DepartureTaskPreconditioning preconditioning = 1;
        DepartureTaskCabinClean cabin_clean = 2;
    }
}

message DepartureEvent
{
    // Departure time in seconds from UNIX epoch (non-specific local time)
    optional uint64 departure_time = 1;
    repeated DepartureTask departure_tasks = 2;
}

/*****************************************************************************/
/*                                                                           */
/* The following section contains all the methods which need to be used      */
/* for offboard control of feature for EVA2                                  */
/*                                                                           */
/*****************************************************************************/
// Operation to request the vehicle to actuate the charging system
message SetChargeControlRequest {
    // This is used to request the charge mode of the vehicle
    charging_common.EnumChargeControlOperation charge_control_request = 1;
    // This is used to indicate how the charge control request was initiated
    charging_common.EnumChargeContext charge_context = 2;
}

// This is used to respond back to a set charge state request
message SetChargeControlResponse {
    charging_common.EnumStatus status = 1;
}

// Operation to get information from the charging system
message GetChargeStateRequest {
    // No parameters
}

// This is used to respond back to a get charge state request
message GetChargeStateResponse {
    charging_common.EnumStatus status = 1;
    // The charging status
    charging_common.EnumChargeState charging_status = 2;
    // Details of any errors in the charge state
    charging_common.EnumChargeErrorMode charge_error_state = 3;
    // Status of wired connection between vehicle and charging plug
    charging_common.EnumChargingInletState charging_inlet_state = 4;
    // Details of the ongoing method of charging
    charging_common.EnumChargingMethod charging_method = 5;
    // max current limit while charging in milliamps, range: 0 to 100000
    // the data in max_current_limit will need to be converted to appropriate
    // units based on need by the receiver of this information
    optional uint32 max_current_limit = 6;
    // instantaneous current rcvd from grid in milliamps, range: 0 to 100000
    // the data in inst_current will need to be converted to appropriate
    // units based on need by the receiver of this information
    optional uint32 inst_current = 7;
    // Can be negative for bidirectional discharging
    // positive for adding charge to the HV battery
    // instantaneous charge inst_charge_power in watts, range: -42200 to 500000
    // the data in inst_power will need to be converted to appropriate
    // units based on need by the receiver of this information
    optional sint32 inst_charge_power = 8;
    // charge rate of HV battery in metres/h, range: 0 - 3200000
    // i.e; Maximum range is equilvalent to  3200 km/hr,2000 miles/hr, 53 km/min, 33 miles/min
    // the data in charge_rate_mts_ph will need to be converted to appropriate
    // units based on need by the receiver of this information
    optional uint32 charge_rate_mts_ph = 9;
    // charge rate of HV Battery in % per hour, range:  0 - 307 %/hrs (5 %/ min)
    optional uint32 charge_rate_percentage_ph = 10;
    // next charge start date and time in seconds, in UTC format
    optional uint64 next_charge_start_time_seconds = 11;
    // this will indicate the number of nested messages on predicted charge information
    // uint32 predicted_data_length = 12;
    reserved 12;
    // this will indicate all the information on predicted charge including
    // soc percentage, predicted range and predicted time
    repeated charging_common.PredictedChargeData predicted_charge_data = 13;
    // total time required to reach the target soc in minutes, range 0 - 3599
    optional uint32 time_to_tgt_soc = 14;
    // predicted range based on current soc value in km, range 0 - 1023
    optional uint32 tgt_soc_ev_range_km = 15;
    // used to indicate the lock/unlock status of the charge cable
    charging_common.EnumChargeCableOperation charge_cable_lock_status = 16;
    // Charge troubleshooter messages and plug and charge troubleshooter messages
    charging_common.EnumChrgTroubleSht charge_troubleshooter = 17;
    // This is used to indicate if the target SoC charging will be reached by departure time
    // [0 = Not reachable, 1 = Reachable]
    optional bool tgt_soc_reachable_by_departure = 18;
}

// Operation to request a specific timed charge settings to be set within the vehicle
message SetTimedChargeSettingsRequest {
    charging_common.EnumChargeType charge_type = 1;
    // start time for charging in hours and minutes
    // based on a 24 hour clock which ranges from 00:01 min to 23:59 with 15 min intervals
    // data range on the vehicle network will be from 0 - 1425
    optional uint32 off_peak_start_time_hours = 2;
    optional uint32 off_peak_start_time_minutes = 3;
    // stop time for charging in hours and minutes
    // based on a 24 hour clock which ranges from 00:01 min to 23:59 with 15 min intervals
    // data range on the vehicle network will be from 0 - 1425
    optional uint32 off_peak_stop_time_hours = 4;
    optional uint32 off_peak_stop_time_minutes = 5;
}

// This is used to respond back to a SetTimedChargeSettings request
message SetTimedChargeSettingsResponse {
    charging_common.EnumStatus status = 1;
}

// Operation to request the settings currently active on the charging system
message GetChargeSettingsRequest {
    // No parameters
}

// This is used to respond back to a GetChargeSettingsRequest request
message GetChargeSettingsResponse {
    charging_common.EnumStatus status = 1;
    charging_common.EnumChargeType charge_type = 2;
    // start time for charging in hours and minutes
    // based on a 24 hour clock which ranges from 00:01 min to 23:59 with 15 min intervals
    // data range on the vehicle network will be from 0 - 1425
    optional uint32 off_peak_start_time_hours = 3;
    optional uint32 off_peak_start_time_minutes = 4;
    // stop time for charging in hours and minutes
    // based on a 24 hour clock which ranges from 00:01 min to 23:59 with 15 min intervals
    // data range on the vehicle network will be from 0 - 1425
    optional uint32 off_peak_stop_time_hours = 5;
    optional uint32 off_peak_stop_time_minutes = 6;
    // this is in percentage, range: 0 to 100
    optional uint32 max_battery_soc = 7;
    // ac charge rate limit in milliamps, range: 0 to 100000
    // the data in ac_charge_rate_limit will need to be converted to appropriate
    // units based on need by the receiver of this information
    optional uint32 actual_ac_charge_rate_limit = 8;
}

// This allows the user to set the maximum value that the battery is allowed to charge to
message SetBatteryMaxStateOfChargeRequest {
    // this is in percentage, range: 0 to 100
    optional uint32 max_battery_soc = 1;
}

// This is used to respond back to a SetBatteryMaxStateOfChargeRequest request
message SetBatteryMaxStateOfChargeResponse {
    charging_common.EnumStatus status = 1;
}

// Operation to request the current state of charge from the battery system
message GetBatteryCurrentStateOfChargeRequest {
    // No parameters
}

// This is used to respond back with information on current battery state of charge
message GetBatteryCurrentStateOfChargeResponse {
    charging_common.EnumStatus status = 1;
    // this is in percentage, range: 0 to 100
    optional uint32 current_battery_soc = 2;
    // This provides the battery energy available at current SoC
    // Range : 0 to 200,000 Wh
    optional uint32 hv_batt_energy_available_at_current_soc = 3;
}

// This allows the user to set the maximum value that the battery is allowed to charge to
message SetACChargeRateLimitRequest {
    // this is in Amps, range: 0 to 100
    optional uint32 max_ac_charge_rate_limit = 1;
    // Additional parameter needed for user type
}

// This is used to respond back to a SetACChargeRateLimitRequest request
message SetACChargeRateLimitResponse {
    charging_common.EnumStatus status = 1;
}

// This allows the user to operate (open/close) the charge door
message SetChargeDoorOperationRequest {
    charging_common.EnumChargeDoor charge_door_side = 1;
    charging_common.EnumChargeDoorOperation charge_door_operation = 2;
}

// This is used to respond back to a SetChargeDoorOperationRequest request
message SetChargeDoorOperationResponse {
    charging_common.EnumStatus status = 1;
}

// Operation to request the current charge door state from the battery system
message GetChargeDoorStatusRequest {
    // No parameters
}

// This is used to respond back to request to GetChargeDoorStatusRequest
message GetChargeDoorStatusResponse {
    charging_common.EnumStatus status = 1;
    charging_common.EnumChargeDoorOperation left_charge_door_status = 2;
    charging_common.EnumChargeDoorOperation right_charge_door_status = 3;
}

// This allows the user to operate (lock/unlock) the charge cable
message SetChargeCableOperationRequest {
    charging_common.EnumChargeCableOperation charge_cable_op = 1;
}

// This is used to respond back to a SetChargeCableOperationResponse request
message SetChargeCableOperationResponse {
    charging_common.EnumStatus status = 1;
}

// This allows the user to set a payment choice on the vehicle
message SetPnCPaymentChoiceRequest {
    charging_common.EnumPnCPaymentChoice payment_choice = 1;
}

// This is used to respond back to a SetPnCPaymentChoice request
message SetPnCPaymentChoiceResponse {
    charging_common.EnumStatus status = 1;
}

// Operation to request the Plug n Charge status on the vehicle
message GetPnCStatusRequest {
    // no parameters
}

// This is used to respond back to a GetPnCStatusRequest request
message GetPnCStatusResponse {
    charging_common.EnumStatus status = 1;
    // indicates the currently active payment method
    charging_common.EnumPnCPaymentMethod pnc_payment_method = 2;
    // indicate the pnc feature status
    charging_common.EnumPnCStatus pnc_feature_status = 3;
}

// Operation to request Plug N Charge feature to be enable/disable
// Availability is not CCF driven but determined dynamically
// based on what user has selected as well as whether all
// conditions are met for the feature to be available
message GetPnCFeatureAvailabilityRequest {
    // no parameters
}

// This is used to respond back to a GetPnCFeatureAvailability request
message GetPnCFeatureAvailabilityResponse {
    charging_common.EnumStatus status = 1;
    // indicates the PnC availability
    charging_common.EnumPnCFeatureAvailability pnc_feature_availability = 2;
}

// This allows the user to set VIN Share for China Specific region
message SetPnCVINShareRequest {
    charging_common.EnumPnCVINShare vin_share = 1;
}

// This is used to respond back to a SetPnCVINShare request
message SetPnCVINShareResponse {
    charging_common.EnumStatus status = 1;
}

// This allows the user to install/update/delete contract certificate for PnC
message SetPnCContractCertOperationRequest {
    // indicate the pnc contract certificate request
    charging_common.EnumPnCContractCertOperation contract_cert_operation = 1;
    // EMAID of the certificate
    string emaid = 2;
    // Contract Certificate Bundle
    bytes certificate_installation_res = 3;
}

// This is used to respond back to a SetPnCContractCertOperationRequest
message SetPnCContractCertOperationResponse {
    charging_common.EnumStatus status = 1;
}

// Operation to request the status of Plug N Charge Contract certificate
message GetPnCContractStatusRequest {
    // no parameters
}

// This is used to respond back to a GetPnCContractStatus request
message GetPnCContractStatusResponse {
    charging_common.EnumStatus status = 1;
    // indicates the status of contract certificate
    charging_common.EnumPnCContractStatus contract_status = 2;
    // Contract Certificate ID for PnC corresponding to the Contract Certificate Status
    string pnc_contract_cert_id_res = 3;
}

// Operation to request the status of Plug N Charge Vehicle to Grid (V2G) Root certificate
message GetPnCV2GRootStatusRequest {
    // no parameters
}

// This is used to respond back to a GetPnCV2GRootStatusRequest
message GetPnCV2GRootStatusResponse {
    charging_common.EnumStatus status = 1;
    // indicates the status of Vehicle to Grid (V2G) root certificate
    charging_common.EnumPnCV2GRootCertificateStatus v2g_root_cert_status = 2;
    // Vehicle to Grid (V2G) Root ID for PnC corresponding to the V2G Root Certificate Status
    string pnc_v2g_root_id = 3;
}

// Operaion to request information related to charging session, this includes the EV range added,
// energy added, the time it took to charge.
// This is data is live
message GetChargeSessionAttributesRequest {
    // no parameter
}

// This used to respond back to GetChargeAttributesRequest
message GetChargeSessionAttributesResponse {
    charging_common.EnumStatus status = 1;
    // This is the EV Range added in Kms (valid range is 0 to 1000)
    optional uint32 range_added = 2;
    // This is the energy added in Wh (valid range is 0 to 200000)
    optional uint32 energy_added = 3;
    // This is the charging duration in minutes (valid range is 0 to 3000)
    optional uint32 charging_duration = 4;
}

// Operation to request the HV battery energy information in various cases from the battery system
message GetBatteryEnergyDataRequest {
    // No parameters
}

// This is used to respond back with information about battery energy information
message GetBatteryEnergyDataResponse {
    charging_common.EnumStatus status = 1;
    // this is the maximum amount of usable energy in the battery
    // Range 0 to 200,000 Wh
    optional uint32 hv_batt_energy_max_usable = 2;
    // this is the minimum amount of usable energy in the battery
    // Range : 0 to 100,000 Wh
    optional uint32 hv_battery_energy_min_usable = 3;
    // this is the amount of energy available in the battery when the battery reaches target state of charge
    // Range : 0 to 200,000 Wh
    optional uint32 predicted_hv_batt_energy_available_at_target_soc = 4;
    // this is the state of health of the battery, this decreases over time, this affects the battery capacity
    // Range 0 to 100%
    optional uint32 hv_batt_state_of_health = 5;
}

// Battery Energy loss APIs are not supported in MLA BEV
// Operation to request the HV battery energy losses by discharge in various cases from the battery system
message GetBatteryEnergyLossRequest {
    // No parameters
}

// This is used to respond back with information about battery energy losses
message GetBatteryEnergyLossResponse {
    charging_common.EnumStatus status = 1;
    // this is the estimated amount of battery energy lost during discharging from current SoC to (Min + reserve) SoC
    // Range : 0 to 5000 Wh
    optional uint32 hv_batt_energy_estimated_loss_discharge = 2;
    // this is the estimated amount of total(tractive+aux) battery energy lost during discharging when
    // travelling on navigation route
    // Range : 0 to 5000 Wh
    optional uint32 hv_batt_energy_estimated_loss_discharge_route_total = 3;
}

// This is used to provide a unique Id for Hubject(3rd party Certificate provider for Plug and charge feature)
// for each charging session
// Hubject wants to attach a unique ID to each contract certificate they create
message GetChargingSessionIDRequest {
    // No parameters
}

// This operation is used to respond back to GetChargingSessionIDRequest
message GetChargingSessionIDResponse {
    charging_common.EnumStatus status = 1;
    optional uint64 pnc_session_id = 2;
}

// This operation allows application to control climatic seats - heating and
// cooling functionalities for different seat zones
message SetSeatClimateRequest {
    repeated SeatClimateZoneState seat_climate_zone = 1;
}

// Response to the Client request for changing the climtic Seats operations.
message SetSeatClimateResponse {
    seats_common.EnumSeatSelection seat_selection = 1;
    charging_common.EnumStatus status = 2;
}

// This operation allows the caller to retrieve the status of climatic seats status
//  for particular seat
message GetSeatClimateStatusRequest {
    seats_common.EnumSeatSelection seat_selection = 1;
}

// Response to GetSeatClimateStatusRequest indicating the climatic Seat status
// of selected seat (s)
message GetSeatClimateStatusResponse {
    charging_common.EnumStatus status = 1;
    repeated SeatClimateZoneState seat_climate_zone = 2;
}

// Operation to enable/disable heated steering wheel
message SetHeatedSteeringWheelRequest {
    EnumHSWTemperatureLevel hsw_temperature_level = 1;
}

// Response to operation of heated steering wheel
message SetHeatedSteeringWheelResponse {
    charging_common.EnumStatus status = 1;
}

// Request to get the current status of heated steering wheel
message GetHeatedSteeringWheelStatusRequest {
    // no parameters
}

// Response to request of heated steering wheel
message GetHeatedSteeringWheelStatusResponse {
    EnumHSWTemperatureLevel hsw_temperature_level = 1;
    charging_common.EnumStatus status = 2;
    // heated steering wheel control inhibit state
    EnumControlState hsw_control_state = 3;
}

// This enum is used to request the precondition from the vehicle.
message SetVehiclePreconditionRequest {
    EnumPreconditionOperation precondition_request = 1;
    // This is used to request the target temperature to set the vehicle to
    // The unit used for this request is deg centigrade
    // Logical value is 0 to 31 deg centigrade in 0.5 steps
    // if the value is not on 0.5 boundary then the physical value shall be truncated
    // after the scale and offset is applied
    // there might be some rounding off with float values recived from client for eg: 20.5 can be 20.499 or 20.511
    // The scale factor and offset will be applied to the recived value
    // for eg: 20.499 is rceived then, it will be rounded to 20.5 * 2 (multiplied by SF=0.5) = 41
    optional float precondition_target_temperature = 2;
}

// This operation is used to indicate the success or failure of the
// set precondition request
message SetVehiclePreconditionResponse {
    charging_common.EnumStatus status = 1;
}

// This enum is used to request the precondition from the vehicle.
message GetVehiclePreconditionStateRequest {
    // no parameter
}

// This operation is used to indicate the success or failure of the
// get precondition request
message GetVehiclePreconditionStateResponse {
    EnumPreConditionCurrentMode precondition_mode = 1;
    EnumPreConditionStatus precondition_status = 2;
    // fixed preconditioning time in minutes to display the progress if needed
    // this will only provide the time and the countdown timer should be implemented by the client
    optional uint32 time_remaining = 3;
    charging_common.EnumStatus status = 4;
}

// This method allows requester to set the departure schedule
message SetDepartureScheduleRequest
{
    // Contains up to 14 departure events
    repeated DepartureEvent departure_events = 1;
}

// This method is a response to the set departure schedule request
message SetDepartureScheduleResponse
{
    charging_common.EnumStatus status = 1;
}

// This operation is used to request for the lock state of the apertures
// in the vehicle
message GetApertureLockStateRequest {
    // no parameter
}

// This operation actually returns the status of all the apertures in the
// vehicle
message GetApertureLockStateResponse {
    charging_common.EnumStatus status = 1;
    EnumApertureLockStatus driver_door_status = 2;
    EnumApertureLockStatus passenger_door_status = 3;
    EnumApertureLockStatus reardriver_door_status = 4;
    EnumApertureLockStatus rearpassenger_door_status = 5;
    EnumApertureLockStatus tailgate_status = 6;
    EnumApertureLockStatus bonnet_status = 7;
    EnumCentralLockStatus central_lock_status = 8;
}

// This operation allows the caller to get the status of vehicle alarm
// This is a fire and forget request since no confirmation is expected back from
// the target ECU
message GetVehicleAlarmStateRequest {
    // no parameter
}

// This operation is used to indicate the status of the
// vehicle alarm
message GetVehicleAlarmStateResponse {
    EnumAlarmStatus alarm_status = 1;
    charging_common.EnumStatus status = 2;
}

// This enum is used to request the cabin air functionality status from the vehicle.
// The user term used for this feature is 'Deep Clean' and it is expected
// to run for a pre-defined amount of time
message GetVehicleCabinAirCleanStateRequest {
    // no parameter
}

// This operation is used to indicate the success or failure of the
// get 'cabin air clean' request
message GetVehicleCabinAirCleanStateResponse {
    EnumCabinAirCleanStatus cabin_air_cleaning_status = 1;
    // This is used to indicate the live in-cabin pm2.5 levels
    optional uint32 cabin_pm2_5_level = 2;
    // This is used to indicate the band appropriate to the cabin pm2.5 level
    optional uint32 cabin_pm2_5_band = 3;
    // This is used to indicate the live external pm2.5 levels
    optional uint32 external_pm2_5_level = 4;
    // This is used to indicate the band appropriate to the external pm2.5 level
    optional uint32 external_pm2_5_band = 5;
    charging_common.EnumStatus status = 6;
    // Placeholder: This is used to indicate the number of cleaning cycles reamining
    // This data is not available on MLA BEV
    optional uint32 cabin_air_clean_cycles_remaining = 7;
}

/*****************************************************************************/
/*                                                                           */
/* The following section contains all the notifications which will be sent   */
/* when a change is seen in the received network data                        */
/*                                                                           */
/*****************************************************************************/
// This is used to indicate any dynamic changing data in the charging system
message NotifyChargeState {
    charging_common.EnumStatus status = 1;
    // The charging status
    charging_common.EnumChargeState charging_status = 2;
    // Details of any errors in the charge state
    charging_common.EnumChargeErrorMode charge_error_state = 3;
    // Status of wired connection between vehicle and charging plug
    charging_common.EnumChargingInletState charging_inlet_state = 4;
    // used to indicate the lock/unlock status of the charge cable
    charging_common.EnumChargeCableOperation charge_cable_lock_status = 5;
    // Charge troubleshooter messages and plug and charge troubleshooter messages
    charging_common.EnumChrgTroubleSht charge_troubleshooter = 6;
    // This is used to indicate if the target SoC charging will be reached by departure time
    // [0 = Not reachable, 1 = Reachable]
    optional bool tgt_soc_reachable_by_departure = 7;
}

// This is used to indicate any changes in any static settings of the charging system
message NotifyChargeSettings {
    charging_common.EnumStatus status = 1;
    charging_common.EnumChargeType charge_type = 2;
    // start time for charging in hours and minutes
    // based on a 24 hour clock which ranges from 00:01 min to 23:59 with 15 min intervals
    // data range on the vehicle network will be from 0 - 1425
    optional uint32 off_peak_start_time_hours = 3;
    optional uint32 off_peak_start_time_minutes = 4;
    // stop time for charging in hours and minutes
    // based on a 24 hour clock which ranges from 00:01 min to 23:59 with 15 min intervals
    // data range on the vehicle network will be from 0 - 1425
    optional uint32 off_peak_stop_time_hours = 5;
    optional uint32 off_peak_stop_time_minutes = 6;
    // this is in percentage, range: 0 to 100
    optional uint32 max_battery_soc = 7;
    // ac charge rate limit in milliamps, range: 0 to 100000
    // the data in ac_charge_rate_limit will need to be converted to appropriate
    // units based on need by the receiver of this information
    optional uint32 actual_ac_charge_rate_limit = 8;
}

// This is used to indicate current state of charge on the battery
message NotifyBatteryCurrentStateOfCharge {
    charging_common.EnumStatus status = 1;
    // this is in percentage, range: 0 to 100
    optional uint32 current_battery_soc = 2;
    // This provides the battery energy available at current SoC
    // Range : 0 to 200,000 Wh
    optional uint32 hv_batt_energy_available_current_soc = 3;
}

// This is used to indicate all information about the ongoing charging session
message NotifyChargeInProgressData {
    charging_common.EnumStatus status = 1;
    // Details of the ongoing method of charging
    charging_common.EnumChargingMethod charging_method = 2;
    // max current limit while charging in milliamps, range: 0 to 100000
    // the data in max_current_limit will need to be converted to appropriate
    // units based on need by the receiver of this information
    optional uint32 max_current_limit = 3;
    // instantaneous current rcvd from grid in milliamps, range: 0 to 100000
    // the data in inst_current will need to be converted to appropriate
    // units based on need by the receiver of this information
    optional uint32 inst_current = 4;
    // Can be negative for bidirectional discharging
    // positive for adding charge to the HV battery
    // instantaneous charge inst_charge_power in watts, range: -42200 to 500000
    // the data in inst_power will need to be converted to appropriate
    // units based on need by the receiver of this information
    optional sint32 inst_charge_power = 5;
    // charge rate of HV battery in metres/h, range: 0 - 3200000
    // i.e; Maximum range is equilvalent to  3200 km/hr,2000 miles/hr, 53 km/min, 33 miles/min
    // the data in charge_rate_mts_ph will need to be converted to appropriate
    // units based on need by the receiver of this information
    optional uint32 charge_rate_mts_ph = 6;
    // charge rate of HV Battery in % per hour, range: 0 - 307 %/hrs (5 %/ min)
    optional uint32 charge_rate_percentage_ph = 7;
}

// This is used to indicate all the predicted information from the charging system
message NotifyPredictedCharge {
    charging_common.EnumStatus status = 1;
    // next charge start date and time in seconds, in UTC format
    optional uint64 next_charge_start_time_seconds = 2;
    // this will indicate the number of nested messages on predicted charge information
    // uint32 predicted_data_length = 3;
    reserved 3;
    // this will indicate all the information on predicted charge including
    // soc percentage, predicted range and predicted time
    repeated charging_common.PredictedChargeData predicted_charge_data = 4;
    // total time required to reach the target soc in minutes, range 0 - 3599
    optional uint32 time_to_tgt_soc = 5;
    // predicted range based on current soc value in km, range 0 - 1023
    optional uint32 tgt_soc_ev_range_km = 6;
}

// This is used to indicate current stats of the charge door
message NotifyChargeDoorStatus {
    charging_common.EnumStatus status = 1;
    charging_common.EnumChargeDoorOperation left_charge_door_status = 2;
    charging_common.EnumChargeDoorOperation right_charge_door_status = 3;
}

// This is used to indicate Plug N Charge feature is enabled/disabled
// Availability is not CCF driven but determined dynamically
// based on what user has selected as well as whether all
// conditions are met for the feature to be available
message NotifyPnCFeatureAvailability {
    charging_common.EnumStatus status = 1;
    charging_common.EnumPnCFeatureAvailability pnc_feature_availability = 2;
}

// This is used to indicate the user payment choice on the vehicle
message NotifyPnCPaymentMethod {
    charging_common.EnumStatus status = 1;
    charging_common.EnumPnCPaymentMethod pnc_payment_method = 2;
}

// This is used to indicate the PnC feature status on the vehicle
message NotifyPnCStatus {
    charging_common.EnumStatus status = 1;
    charging_common.EnumPnCStatus pnc_feature_status = 2;
}

// This is used to indicate the status of Plug N Charge Contract certificate
message NotifyPnCContractStatus {
    charging_common.EnumStatus status = 1;
    charging_common.EnumPnCContractStatus contract_status = 2;
    string pnc_contract_cert_id_res = 3;
}

// This is used to indicate the status of Plug N Charge Vehicle to Grid (V2G) Root certificate
message NotifyPnCV2GRootStatus {
    charging_common.EnumStatus status = 1;
    charging_common.EnumPnCV2GRootCertificateStatus v2g_root_cert_status = 2;
    string pnc_v2g_root_id = 3;
}

// This is used to notify information related to charging session, this includes the EV range added,
// energy added, the time it took to charge.
// This is data is live
message NotifyChargeSessionAttributes {
    charging_common.EnumStatus status = 1;
    // This is the EV Range added in Kms (valid range is 0 to 1000)
    optional uint32 range_added = 2;
    // This is the energy added in Wh (valid range is 0 to 200000)
    optional uint32 energy_added = 3;
    // This is the charging duration in minutes (valid range is 0 to 3600)
    optional uint32 charging_duration = 4;
}

// This is used to notify information about battery energy information in various cases
message NotifyBatteryEnergyData {
    charging_common.EnumStatus status = 1;
    // this is the maximum amount of usable energy in the battery
    // Range 0 to 200,000 Wh
    optional uint32 hv_batt_energy_max_usable = 2;
    // this is the minimum amount of usable energy in the battery
    // Range : 0 to 100,000 Wh
    optional uint32 hv_battery_energy_min_usable = 3;
    // this is the amount of energy available in the battery when the battery reaches target state of charge
    // Range : 0 to 200,000 Wh
    optional uint32 predicted_hv_batt_energy_available_at_target_soc = 4;
    // this is the state of health of the battery, this decreases over time, this affects the battery capacity
    // Range 0 to 100%
    optional uint32 hv_batt_state_of_health = 5;
}

// Battery Energy loss APIs are not supported in MLA BEV
// This is used to notify information related to battery energy losses
message NotifyBatteryEnergyLoss {
    charging_common.EnumStatus status = 1;
    // this is the estimated amount of battery energy lost during discharging from current SoC to (Min + reserve) SoC
    // Range : 0 to 5000 Wh
    optional uint32 hv_batt_energy_estimated_loss_discharge = 2;
    // this is the estimated amount of total(tractive+aux) battery energy lost during discharging when
    // travelling on navigation route
    // Range : 0 to 5000 Wh
    optional uint32 hv_batt_energy_estimated_loss_discharge_route_total = 3;
}

// This is used to provide a unique Id for Hubject(3rd party Certificate provider for Plug and charge feature)
// for each charging session
// Hubject wants to attach a unique ID to each contract certificate they create
message NotifyChargingSessionID {
    charging_common.EnumStatus status = 1;
    optional uint64 pnc_session_id = 2;
}

// The method is used to notify ( from the partial power state climate service)
// the status of climatic seat (s) when it changes
message NotifySeatClimateStatus {
    charging_common.EnumStatus status = 1;
    repeated SeatClimateZoneState seat_climate_zone = 2;
}

// Notification to indicate status of heated steering wheel
message NotifyHeatedSteeringWheelStatus {
    EnumHSWTemperatureLevel hsw_temperature_level = 1;
    charging_common.EnumStatus status = 2;
    // heated steering wheel control inhibit state
    EnumControlState hsw_control_state = 3;
}

// This notification is sent on change of the precondition functionality of the vehicle
message NotifyVehiclePreconditionState {
    EnumPreConditionCurrentMode precondition_mode = 1;
    EnumPreConditionStatus precondition_status = 2;
    // fixed preconditioning time in minutes to display the progress if needed
    // this will only provide the time and the countdown timer should be implemented by the client
    optional uint32 time_remaining = 3;
    charging_common.EnumStatus status = 4;
}

// This notification is sent to indicate the status of the departure schedule set
// request made to the vehicle
message NotifyDepartureScheduleStoredStatus {
    charging_common.EnumStatus status = 1;
}

// This notification is sent on change of the lock state of the vehicle
message NotifyApertureLockState {
    charging_common.EnumStatus status = 1;
    EnumApertureLockStatus driver_door_status = 2;
    EnumApertureLockStatus passenger_door_status = 3;
    EnumApertureLockStatus reardriver_door_status = 4;
    EnumApertureLockStatus rearpassenger_door_status = 5;
    EnumApertureLockStatus tailgate_status = 6;
    EnumApertureLockStatus bonnet_status = 7;
    EnumCentralLockStatus central_lock_status = 8;
}

// This notification is sent on change of the lock state of the vehicle
message NotifyVehicleAlarmState {
    EnumAlarmStatus alarm_status = 1;
    charging_common.EnumStatus status = 2;
}

// This notification is sent on change of the cabin air functionality of the vehicle
message NotifyVehicleCabinAirCleanState {
    EnumCabinAirCleanStatus cabin_air_cleaning_status = 1;
    // This is used to indicate the live in-cabin pm2.5 levels
    optional uint32 cabin_pm2_5_level = 2;
    // This is used to indicate the band appropriate to the cabin pm2.5 level
    optional uint32 cabin_pm2_5_band = 3;
    // This is used to indicate the live external pm2.5 levels
    optional uint32 external_pm2_5_level = 4;
    // This is used to indicate the band appropriate to the external pm2.5 level
    optional uint32 external_pm2_5_band = 5;
    charging_common.EnumStatus status = 6;
    // Placeholder: This is used to indicate the number of cleaning cycles reamining
    // This data is not available on MLA BEV
    optional uint32 cabin_air_clean_cycles_remaining = 7;
}
