// Protocol Buffers
// name - charging_common
// Description - Common type definitions used in different services.

// Copyright 2023 Jaguar Land Rover Limited
syntax = "proto3";

package charging_common;

/*****************************************************************************/
/*                                                                           */
/* The following section contains all the enumerations used for charging     */
/* feature control and status                                                */
/*                                                                           */
/*****************************************************************************/

enum EnumStatus {
    ENUM_STATUS_UNSPECIFIED = 0;
    // Normal return status indication, no error in operation and/or data is valid
    // Applies to all Response messages (Request/Response)
    // May apply to Notification messages (Pub/Sub) where data quality indications are also used – OK indicting good data
    ENUM_STATUS_OK = 1;
    // Data quality indication return status enumerations
    // May apply to Response messages (Request/Response)
    // May apply to Notification messages (Pub/Sub)
    ENUM_STATUS_DATA_DEGRADED = 2;
    ENUM_STATUS_DATA_UNRELIABLE = 3;
    ENUM_STATUS_DATA_UNAVAILABLE = 4;
    // Operation errored and blocked due to some ‘inhibiting condition’ return status enumerations
    // Applies to Request/Response calls
    ENUM_STATUS_ERROR_INVALID_SERVICE_STATE = 5;
    ENUM_STATUS_ERROR_INVALID_VEHICLE_STATE = 6;
    ENUM_STATUS_ERROR_INVALID_CAR_CONFIGURATION = 7;
    // Request message input field error return status enumerations
    // Applies to Request/Response calls
    ENUM_STATUS_ERROR_MISSING_INPUT_FIELD = 8;
    ENUM_STATUS_ERROR_INVALID_INPUT_FIELD = 9;
    // Abnormal nonspecific return status indication
    // May indicate that an operation or method execution cannot be performed, or has errored, or is blocked at this time
    // Applies to Request/Response calls
    ENUM_STATUS_NOT_OK = 10;
    // The following field numbers are reserved for future enhancements. Any feature specfic statuses need to be
    // defined from 101 onwards
    reserved 11 to 100;
}

// This is used to request or indicate the charge mode of the vehicle
enum EnumChargeControlOperation {
    ENUM_CHARGE_CONTROL_OPERATION_UNSPECIFIED = 0;
    ENUM_CHARGE_CONTROL_OPERATION_START = 1;
    ENUM_CHARGE_CONTROL_OPERATION_STOP = 2;
    // This enum is used to revert the charge mode between scheduled charging and immediate charging, vice-versa
    // This operation will be supported only when charging schedules are active in the vehicle
    // The client is expected to determine appropriate usage of "ENUM_CHARGE_CONTROL_OPERATION_STOP" Vs
    // "ENUM_CHARGE_CONTROL_OPERATION_REVERT"
    ENUM_CHARGE_CONTROL_OPERATION_REVERT = 3;
}

// This is used to indicate how the charge control request was initiated
enum EnumChargeContext {
    ENUM_CHARGE_CONTEXT_UNSPECIFIED = 0;
    // to indicate when charge control (start/stop) is initiated by the user
    ENUM_CHARGE_CONTEXT_USER = 1;
    // to indicate when charge control (start/stop) is initiated by an external
    // system (eg: Octopus Energy)
    ENUM_CHARGE_CONTEXT_SYSTEM = 2;
}

// This is used to indicate the status of the charging system within the vehicle
enum EnumChargeState {
    ENUM_CHARGE_STATE_UNSPECIFIED = 0;
    // No active/ongoing charge session
    ENUM_CHARGE_STATE_DEFAULT = 1;
    // used to indicate when the charging system is initializing
    ENUM_CHARGE_STATE_INITIALIZING = 2;
    // used to indicate when charging system is connected to a charger
    // but charging has not yet started
    ENUM_CHARGE_STATE_WAITING_TO_CHARGE = 3;
    // indicates when charging system is connected to a charger
    // and charging is triggered by the user but charging station
    // is yet to trigger current
    ENUM_CHARGE_STATE_WAITING_FOR_CHARGE_STATION = 4;
    // indicates when charging is in progress
    ENUM_CHARGE_STATE_CHARGE_IN_PROGRESS = 5;
    // Charging session is paused  i.e; Charging won't happen until vehicle
    // receives charge start requests such as start command from user OR Charge
    // cable removed and plug it back again
    ENUM_CHARGE_STATE_CHARGE_STOPPED = 6;
    // indicates when charging system has reached the target charge level
    ENUM_CHARGE_STATE_CHARGE_COMPLETE = 7;
    // indicates when charging system has encountered a charging fault
    // EnumChargeErrorMode is used to indicate the exact error encountered
    ENUM_CHARGE_STATE_CHARGE_ERROR = 8;
    // indicates when the charging system is waiting to discharge
    ENUM_CHARGE_STATE_WAITING_TO_DISCHARGE = 9;
    // indicates when discharging is in progress
    ENUM_CHARGE_STATE_DISCHARGE_IN_PROGRESS = 10;
    // indicates when discharge is complete
    ENUM_CHARGE_STATE_DISCHARGE_COMPLETE = 11;
    // indicates when charging system has encountered a discharging fault
    // EnumChargeErrorMode is used to indicate the exact error encountered
    ENUM_CHARGE_STATE_DISCHARGE_ERROR = 12;
    // this enum is used to indicate when charging system is forced to charge due to
    // user requesting for immediate charge even though a schedule is set
    // this is a temporary state and the charging system will revert back to scheduled
    // charge once the charging is complete or user cancels the ongoing forced charge
    // session
    ENUM_CHARGE_STATE_FORCED_CHARGE_IN_PROGRESS = 13;
    // indicates when the charging system is waiting for payment
    ENUM_CHARGE_STATE_WAITING_FOR_PAYMENT = 14;
}

// This is used to indicate any errors in the charging mode
enum EnumChargeErrorMode {
    ENUM_CHARGE_ERROR_MODE_UNSPECIFIED = 0;
    ENUM_CHARGE_ERROR_MODE_NO_ERROR = 1;
    ENUM_CHARGE_ERROR_MODE_SYSTEM_ERROR = 2;
    ENUM_CHARGE_ERROR_MODE_CHARGE_STATION_ERROR = 3;
    ENUM_CHARGE_ERROR_MODE_DISCHARGE_ERROR = 4;
    ENUM_CHARGE_ERROR_MODE_PLUG_LOCK_FAILURE = 5;
    ENUM_CHARGE_ERROR_MODE_PLUG_UNLOCK_FAILURE = 6;
    ENUM_CHARGE_ERROR_MODE_MANUAL_PAYMENT_FAILED = 7;
    ENUM_CHARGE_ERROR_MODE_AUTOMATE_PAYMENT_FAILED = 8;
    ENUM_CHARGE_ERROR_MODE_BATTERY_TEMP_WARNING = 9;
    ENUM_CHARGE_ERROR_MODE_GENERAL_INFO = 10;
    ENUM_CHARGE_ERROR_MODE_CHARGE_DOOR_ERROR = 11;
}

// This is used to indicate the status of the charging inlet
enum EnumChargingInletState {
    ENUM_CHARGING_INLET_STATE_UNSPECIFIED = 0;
    // Charger is unplugged
    ENUM_CHARGING_INLET_STATE_UNPLUGGED = 1;
    // Charger is plugged in
    ENUM_CHARGING_INLET_STATE_PLUGGED = 2;
    // V2L adaptor is plugged in
    ENUM_CHARGING_INLET_STATE_ADAPTOR_PLUGGED = 3;
}

// This is used to request or indicate the charge mode of the vehicle
enum EnumChargeType {
    ENUM_CHARGE_TYPE_UNSPECIFIED = 0;
    // Start charging as soon charger is connected
    ENUM_CHARGE_TYPE_IMMEDIATE = 1;
    // Fixed schedule is as per time set by user (this does not consider target soc and dep time)
    ENUM_CHARGE_TYPE_FIXED_SCHEDULE = 2;
    // This will consider the target soc to achieve, dep time and low cost hours to determine start time
    ENUM_CHARGE_TYPE_SCHEDULE_PLUS = 3;
    // This is based on third party control of charging hours
    ENUM_CHARGE_TYPE_SMART_SCHEDULE = 4;
    ENUM_CHARGE_TYPE_BIDIRECTIONAL_SCHEDULE = 5;
}

// This is used to indicate how the vehicle is being charged
enum EnumChargingMethod {
    ENUM_CHARGING_METHOD_UNSPECIFIED = 0;
    // Unknown charging type
    ENUM_CHARGING_METHOD_NOT_CHARGING = 1;
    // AC Single phase, 2 phase or 3 phase charging
    ENUM_CHARGING_METHOD_AC_CHARGING = 2;
    // DC CCS1 charging, CCS2 charging, GBT charging, CHAdeMO charging
    ENUM_CHARGING_METHOD_DC_CHARGING = 3;
    // Wireless charging
    ENUM_CHARGING_METHOD_WIRELESS_CHARGING = 4;
}

// This is used to identify the charge flap of the charging inlet
// Charge ports will be market/program specific
// In some cases they may be on the right, in some cases on the left
// while in some cases both may be present for different
// technologies/protocols of charging
// Eg: EMA and/or MLA may have either one or both sides supported for the NA market
// Likewise EMA and/or MLA may have either one or both sides supported for the Japan market
enum EnumChargeDoor {
    ENUM_CHARGE_DOOR_UNSPECIFIED = 0;
    // Charge door on the left side of the vehicle
    ENUM_CHARGE_DOOR_LEFT = 1;
    // Charge door on the right side of the vehicle
    ENUM_CHARGE_DOOR_RIGHT = 2;
}

// This is used to request or indicate the operation on the charge door of the vehicle
enum EnumChargeDoorOperation {
    ENUM_CHARGE_DOOR_OPERATION_UNSPECIFIED = 0;
    // Open charge door
    // can also be used to indicate a warning to the user if charge door is open
    // when it is supposed to be closed
    ENUM_CHARGE_DOOR_OPERATION_OPEN = 1;
    // Close charge door
    ENUM_CHARGE_DOOR_OPERATION_CLOSE = 2;
    // charge door operation ongoing
    ENUM_CHARGE_DOOR_OPERATION_IN_PROGRESS = 3;
    // charge door error
    ENUM_CHARGE_DOOR_OPERATION_ERROR = 4;
}

// This is used to request or indicate the operation on the charge door of the vehicle
enum EnumChargeCableOperation {
    ENUM_CHARGE_CABLE_OPERATION_UNSPECIFIED = 0;
    // charge cable unlock
    ENUM_CHARGE_CABLE_OPERATION_UNLOCK = 1;
    // charge cable lock
    ENUM_CHARGE_CABLE_OPERATION_LOCK = 2;
    // charge cable error
    ENUM_CHARGE_CABLE_OPERATION_ERROR = 3;
}

// This is used to request or indicate the user payment choice on the vehicle
enum EnumPnCPaymentChoice {
    ENUM_PNC_PAYMENT_CHOICE_UNSPECIFIED = 0;
    // Plug N Charge payment choice is disabled
    ENUM_PNC_PAYMENT_CHOICE_DISABLED = 1;
    // Plug N Charge payment choice is enabled
    ENUM_PNC_PAYMENT_CHOICE_ENABLED = 2;
}

// This is used to request or indicate the payment method is Plug n Charge
// or External Identification Mode
enum EnumPnCPaymentMethod {
    ENUM_PNC_PAYMENT_METHOD_UNSPECIFIED = 0;
    // payment method selected as Plug N Charge or Automatic
    ENUM_PNC_PAYMENT_METHOD_AUTOMATIC = 1;
    // payment method selected as External Identification Means or Manual
    ENUM_PNC_PAYMENT_METHOD_MANUAL = 2;
}

// This is used to indicate the PnC feature status on the vehicle
enum EnumPnCStatus {
    ENUM_PNC_STATUS_UNSPECIFIED = 0;
    // Plug N Charge feature is active
    ENUM_PNC_STATUS_ACTIVE = 1;
    // Plug N Charge feature is inactive
    ENUM_PNC_STATUS_INACTIVE = 2;
    // Plug N Charge feature has an error
    ENUM_PNC_STATUS_ERROR = 3;
}

// This is used to indicate PnC feature is enabled or disabled
// Availability is not CCF driven but determined dynamically
// based on what user has selected as well as whether all
// conditions are met for the feature to be available
enum EnumPnCFeatureAvailability {
    ENUM_PNC_FEATURE_UNSPECIFIED = 0;
    // Plug N Charge feature is not supported
    ENUM_PNC_FEATURE_NOT_SUPPORTED = 1;
    // Plug N Charge feature is disabled
    ENUM_PNC_FEATURE_DISABLED = 2;
    // Plug N Charge feature is enabled
    ENUM_PNC_FEATURE_ENABLED = 3;
}

// This is used to request or indicate Vehicle Identification Number(VIN) for China specific region
enum EnumPnCVINShare {
    ENUM_PNC_VIN_SHARE_UNSPECIFIED = 0;
    // PnC VIN share is disabled
    ENUM_PNC_VIN_SHARE_DISABLED = 1;
    // PnC VIN share is enabled
    ENUM_PNC_VIN_SHARE_ENABLED = 2;
}

// This is used to request or indicate the Vehicle to Grid (V2G) Root certificate status
enum EnumPnCV2GRootCertificateStatus {
    ENUM_PNC_V2G_ROOT_CERTIFICATE_UNSPECIFIED = 0;
    // V2G root certificate corrupted
    ENUM_PNC_V2G_ROOT_CERTIFICATE_CORRUPTED = 1;
    // V2G root certificate not installed
    ENUM_PNC_V2G_ROOT_CERTIFICATE_NOT_INSTALLED = 2;
    // V2G root certificate expired
    ENUM_PNC_V2G_ROOT_CERTIFICATE_EXPIRED = 3;
    // V2G root certificate missing
    ENUM_PNC_V2G_ROOT_CERTIFICATE_MISSING = 4;
    // V2G root certificate expiring soon
    ENUM_PNC_V2G_ROOT_CERTIFICATE_EXPIRING_SOON = 5;
    // V2G root certificate corrupted
    ENUM_PNC_V2G_ROOT_CERTIFICATE_INSTALLED = 6;
}

// This is used to indicate the Charge troubleshooter messages and plug and charge troubleshooter messages
enum EnumChrgTroubleSht {
    ENUM_CHRG_TROUBLESHT_UNSPECIFIED = 0;
    ENUM_CHRG_TROUBLESHT_CLEAR_MESSAGE = 1;
    ENUM_CHRG_TROUBLESHT_PWR_SOURCE_FAULT = 2;
    ENUM_CHRG_TROUBLESHT_UNSUITABLE_PWR_SOURCE = 3;
    ENUM_CHRG_TROUBLESHT_OVERCURRENT_DETECTED = 4;
    ENUM_CHRG_TROUBLESHT_UNABLE_TO_LOCK_PIN_DUAL = 5;
    ENUM_CHRG_TROUBLESHT_UNABLE_TO_LOCK_PIN_SINGLE = 6;
    ENUM_CHRG_TROUBLESHT_UNABLE_TO_UNLOCK_PIN = 7;
    ENUM_CHRG_TROUBLESHT_FLAP_STUCK_OPEN = 8;
    ENUM_CHRG_TROUBLESHT_FLAP_STUCK_OPEN_DRIVING = 9;
    ENUM_CHRG_TROUBLESHT_FLAP_STUCK_CLOSED = 10;
    ENUM_CHRG_TROUBLESHT_BOTH_INLETS_USED = 11;
    ENUM_CHRG_TROUBLESHT_NO_AC_CHARGING = 12;
    ENUM_CHRG_TROUBLESHT_NO_DC_CHARGING = 13;
    ENUM_CHRG_TROUBLESHT_ALL_CHARGING_PREVENTED = 14;
    ENUM_CHRG_TROUBLESHT_CHARGING_INLET_ISSUE = 15;
    ENUM_CHRG_TROUBLESHT_DC_800V = 16;
    ENUM_CHRG_TROUBLESHT_PWR_SOURCE_DECREASE_CHARG_RATE = 17;
    ENUM_CHRG_TROUBLESHT_LOW_BATT_TEMP = 18;
    ENUM_CHRG_TROUBLESHT_CRITICAL_LOW_BATT_TEMP = 19;
    ENUM_CHRG_TROUBLESHT_HIGH_BATT_TEMP = 20;
    ENUM_CHRG_TROUBLESHT_USER_SELECT_AC_LIMIT = 21;
    ENUM_CHRG_TROUBLESHT_SLOW_AC_CHARGING = 22;
    ENUM_CHRG_TROUBLESHT_NO_CONTRACT_CERT = 23;
    ENUM_CHRG_TROUBLESHT_CONTRACT_CERT_EXP_CANCEL = 24;
    // ENUM_CHRG_TROUBLESHT_REINSTALL_CONTRACT_CERT = 25;
    // ENUM_CHRG_TROUBLESHT_JLR_CERT_TARIFF_ERROR = 26;
    reserved 25, 26;
    ENUM_CHRG_TROUBLESHT_EVSE_CERT_ERROR = 27;
    ENUM_CHRG_TROUBLESHT_GENERIC_PNC_V2G_ERROR = 28;
}

// This is used to request or indicate the contract certificate request is
// install, update or delete
enum EnumPnCContractCertOperation {
    ENUM_PNC_CONTRACT_CERT_OPERATION_UNSPECIFIED = 0;
    // Install Contract certificate for PnC
    ENUM_PNC_CONTRACT_CERT_OPERATION_INSTALL = 1;
    // Update Contract certificate for PnC
    ENUM_PNC_CONTRACT_CERT_OPERATION_UPDATE = 2;
    // Delete Contract certificate for PnC
    ENUM_PNC_CONTRACT_CERT_OPERATION_DELETE = 3;
}

// This is used to indicate the contract certificate status
enum EnumPnCContractStatus {
    ENUM_PNC_CONTRACT_STATUS_UNSPECIFIED = 0;
    // No Contract certificate is installed in vehicle for PnC
    ENUM_PNC_CONTRACT_NO_CERTIFICATE_INSTALLED = 1;
    // Valid Contract certificate available for PnC
    ENUM_PNC_CONTRACT_CERTIFICATE_VALID = 2;
    // Contract Certificate expired
    ENUM_PNC_CONTRACT_CERTIFICATE_EXPIRED = 3;
    // Contract Certificate is expiring soon
    ENUM_PNC_CONTRACT_CERTIFICATE_EXPIRING_SOON = 4;
    // Processing the last request
    ENUM_PNC_CONTRACT_REQUEST_PROCESSING = 5;
    // Contract Certificate validation failed
    ENUM_PNC_CONTRACT_CERTIFICATE_INVALID = 6;
    // Contract Certificate corrupted during certificate installation process
    ENUM_PNC_CERTIFICATE_CORRUPTED_WHILE_INSTALL = 7;
    // Contract Certificate corrupted during certificate deletion process
    ENUM_PNC_CERTIFICATE_CORRUPTED_WHILE_DELETE = 8;
    // Failed to process the last request
    ENUM_PNC_CONTRACT_REQUEST_PROCESSING_FAILED = 9;
    // Successfully processed the last request
    ENUM_PNC_CONTRACT_REQUEST_PROCESSED_OK = 10;
}

// This is used to indicate the state of Vehicle 2 load discharge state
// HVPO stands for high voltage power outlet
enum EnumHVPOStatus {
    ENUM_HVPO_STATUS_UNSPECIFIED = 0;
    // This state shall indicate that no discharging is requested
    ENUM_HVPO_STATUS_NO_DISCHARGE_REQUEST = 1;
    // This state shall indicate that discharge is in progress
    ENUM_HVPO_STATUS_DISCHARGE_IN_PROGRESS = 2;
    // This state shall indicate that there is a SOC limit on the discharge, once the state of charge is reached
    // once the state of charge is reached discharge will stop
    ENUM_HVPO_STATUS_DISCHARGE_SOC_LIMIT_REACHED = 3;
    // This state shall indicate that discharging is not available e.g. due to AC charging or Non native Dc charging
    ENUM_HVPO_STATUS_DISCHARGE_UNAVAILABLE = 4;
    // This state shall indicate that there is in an error in the HVPO discharge system
    ENUM_HVPO_STATUS_DISCHARGE_ERROR = 5;
}

// This is used to indicate the setting of Auto unlock of the charging cable (AC charging)
enum EnumChargeCableAutoUnlock {
    ENUM_CHARGE_CABLE_AUTO_UNLOCK_UNSPECIFIED = 0;
    ENUM_CHARGE_CABLE_AUTO_UNLOCK_ON = 1;
    ENUM_CHARGE_CABLE_AUTO_UNLOCK_OFF = 2;
}

// This is used to indicate the setting of Charging cable - Approach unlocking
enum EnumChargeCableApproachUnlock {
    ENUM_CHARGE_CABLE_APPROACH_UNSPECIFIED = 0;
    ENUM_CHARGE_CABLE_APPROACH_UNLOCK_ON = 1;
    ENUM_CHARGE_CABLE_APPROACH_UNLOCK_OFF = 2;
}

// This is used to indicate the setting of Charging status - permanent charging light
enum EnumChargeLightPermanent {
    ENUM_CHARGE_LIGHT_PERMANENT_UNSPECIFIED = 0;
    ENUM_CHARGE_LIGHT_PERMANENT_ON = 1;
    ENUM_CHARGE_LIGHT_PERMANENT_OFF = 2;
}

// This is used to indicate the setting of Auto close of the charging door
enum EnumChargeDoorAutoClose {
    ENUM_CHARGE_DOOR_AUTO_CLOSE_UNSPECIFIED = 0;
    ENUM_CHARGE_DOOR_AUTO_CLOSE_ON = 1;
    ENUM_CHARGE_DOOR_AUTO_CLOSE_OFF = 2;
}

// This is used to indicate the battery level
enum EnumBatteryLevel {
    ENUM_BATTERY_LEVEL_UNSPECIFIED = 0;
    ENUM_BATTERY_LEVEL_LOW = 1;
    ENUM_BATTERY_LEVEL_NORMAL = 2;
}

// This is used to indicate the different errors that can occur in the plug and charge system
enum EnumPnCErrorCode {
    ENUM_PNC_ERROR_CODE_UNSPECIFIED = 0;
    ENUM_PNC_ERROR_CODE_NO_ERROR = 1;
    ENUM_PNC_ERROR_CODE_FAILED_NO_CERTIFICATE_AVAILABLE = 2;
    ENUM_PNC_ERROR_CODE_FAILED_CONTRACT_CANCELED = 3;
    ENUM_PNC_ERROR_CODE_FAILED_CERTIFICATE_REVOKED = 4;
    ENUM_PNC_ERROR_CODE_FAILED_CERTIFICATE_EXPIRED = 5;
    ENUM_PNC_ERROR_CODE_FAILED_CERT_CHAIN_ERROR = 6;
    ENUM_PNC_ERROR_CODE_FAILED_PAYMENT_SELECTION_INVALID = 7;
    ENUM_PNC_ERROR_CODE_FAILED_SIGNATURE_ERROR = 8;
    ENUM_PNC_ERROR_CODE_FAILED_CHALLENGE_INVALID = 9;
    ENUM_PNC_ERROR_CODE_FAILED_TARIFF_SELECTION_INVALID = 10;
    ENUM_PNC_ERROR_CODE_FAILED_CHARGING_PROFILE_INVALID = 11;
    ENUM_PNC_ERROR_CODE_FAILED_UNKNOWN_SESSION = 12;
    ENUM_PNC_ERROR_CODE_FAILED_SERVICE_SELECTION_INVALID = 13;
    ENUM_PNC_ERROR_CODE_FAILED_SERVICE_ID_INVALID = 14;
    ENUM_PNC_ERROR_CODE_FAILED_NO_CHARGE_SERVICE_SELECTED = 15;
    ENUM_PNC_ERROR_CODE_FAILED_POWER_DELIVERY_NOT_APPLIED = 16;
    ENUM_PNC_ERROR_CODE_FAILED_CONTACT_ERROR = 17;
    ENUM_PNC_ERROR_CODE_SEQUENCE_ERROR = 18;
    ENUM_PNC_ERROR_CODE_FAILED_CERTIFICATE_NOT_ALLOWED_AT_THISEVSE = 19;
    ENUM_PNC_ERROR_CODE_FAILED_WRONG_ENERGY_TRANSFER_MODE = 20;
    ENUM_PNC_ERROR_CODE_FAILED_WRONG_CHARGE_PARAMETER = 21;
    ENUM_PNC_ERROR_CODE_FAILED_METERING_SIGNATURE_NOT_VALID = 22;
    ENUM_PNC_ERROR_CODE_FAILED_NO_NEGOTIATION = 23;
    ENUM_PNC_ERROR_CODE_EVSE_CERTIFICATION_EXPIRED = 24;
    ENUM_PNC_ERROR_CODE_EVSE_MALFUNCTION = 25;
    ENUM_PNC_ERROR_CODE_EVSE_SHUTDOWN = 26;
    ENUM_PNC_ERROR_CODE_EVSE_UTILITY_INTERUPT_EVENT = 27;
    ENUM_PNC_ERROR_CODE_EVSE_EMERGENCY_SHUTDOWN = 28;
    ENUM_PNC_ERROR_CODE_EVSE_ISOLATION_STATUS_WARNING = 29;
    ENUM_PNC_ERROR_CODE_EVSE_ISOLATION_STATUS_FAULT = 30;
    ENUM_PNC_ERROR_CODE_RCD_TRUE = 31;
    ENUM_PNC_ERROR_CODE_SUPPORTED_APP_PROTOCOL_REQ_TIMOUT = 32;
    ENUM_PNC_ERROR_CODE_SESSION_SETUP_REQ_TIMEOUT = 33;
    ENUM_PNC_ERROR_CODE_SERVICE_DISCOVERY_REQ_TIMEOUT = 34;
    ENUM_PNC_ERROR_CODE_SERVICE_DETAIL_REQ_TIMEOUT = 35;
    ENUM_PNC_ERROR_CODE_SERVICE_PAYMENT_SELECTION_REQ_TIMEOUT = 36;
    ENUM_PNC_ERROR_CODE_PAYMENT_DETAIL_REQ_TIMEOUT = 37;
    ENUM_PNC_ERROR_CODE_ATHORIZATION_REQ_TIMEOUT = 38;
    ENUM_PNC_ERROR_CODE_CHARGE_PARAM_DISCOVERY_REQ_TIMEOUT = 39;
    ENUM_PNC_ERROR_CODE_POWER_DELIVERY_REQ_TIMEOUT = 40;
    ENUM_PNC_ERROR_CODE_CHARGING_STATUS_REQ_TIMEOUT = 41;
    ENUM_PNC_ERROR_CODE_METERING_RECEIPT_REQ_TIMEOUT = 42;
    ENUM_PNC_ERROR_CODE_CABLE_CHECK_REQ_TIMEOUT = 43;
    ENUM_PNC_ERROR_CODE_WELDING_DETECTION_REQ_TIMEOUT = 44;
    ENUM_PNC_ERROR_CODE_PRECHARGE_REQ_TIMEOUT = 45;
    ENUM_PNC_ERROR_CODE_CURRENT_DEMAND_REQ_TIMEOUT = 46;
    ENUM_PNC_ERROR_CODE_SESSION_STOP_REQ_TIMEOUT = 47;
    ENUM_PNC_ERROR_CODE_SUPPORTED_APP_PROTOCOL_RES_FAILED_TIMOUT = 48;
    ENUM_PNC_ERROR_CODE_SESSION_SETUP_RES_FAILED__TIMEOUT = 49;
    ENUM_PNC_ERROR_CODE_SERVICE_DISCOVERY_RES_FAILED_TIMEOUT = 50;
    ENUM_PNC_ERROR_CODE_SERVICE_DETAIL_RES_FAILED_TIMEOUT = 51;
    ENUM_PNC_ERROR_CODE_SERVICE_PAYMENT_SELECTION_RES_FAILED_TIMEOUT = 52;
    ENUM_PNC_ERROR_CODE_PAYMENT_DETAILS_RES_FAILED_TIMEOUT = 53;
    ENUM_PNC_ERROR_CODE_ATHORIZATION_RES_FAILED_TIMEOUT = 54;
    ENUM_PNC_ERROR_CODE_CHARGE_PARAM_DISCOVERY_RES_FAILED_TIMEOUT = 55;
    ENUM_PNC_ERROR_CODE_POWER_DELIVERY_RES_FAILED_TIMEOUT = 56;
    ENUM_PNC_ERROR_CODE_CHARGING_STATUS_RES_FAILED_TIMEOUT = 57;
    ENUM_PNC_ERROR_CODE_METERING_RECEIPT_RES_FAILED_TIMEOUT = 58;
    ENUM_PNC_ERROR_CODE_CABLE_CHECK_RES_FAILED_TIMEOUT = 59;
    ENUM_PNC_ERROR_CODE_WELDING_DETECTION_RES_FAILED_TIMEOUT = 60;
    ENUM_PNC_ERROR_CODE_PRECHARGE_RES_FAILED_TIMEOUT = 61;
    ENUM_PNC_ERROR_CODE_CURRENT_DEMAND_RES_FAILED_TIMEOUT = 62;
    ENUM_PNC_ERROR_CODE_SESSION_STOP_RES_FAILED_TIMEOUT = 63;
    ENUM_PNC_ERROR_CODE_INPUT_SIGNAL_ERR_CCF_CHARGE_PNC = 64;
    ENUM_PNC_ERROR_CODE_INPUT_SIGNAL_ERR_PNC_TOGGLE_USER_REQ = 65;
    ENUM_PNC_ERROR_CODE_INPUT_SIGNAL_ERR_PNC_CHINA_VIN_SHARE = 66;
    ENUM_PNC_ERROR_CODE_INPUT_SIGNAL_ERR_PNC_CONTRACT_CERT_REQ = 67;
    ENUM_PNC_ERROR_CODE_INPUT_SIGNAL_ERR_UTC_TIME_DATE = 68;
    ENUM_PNC_ERROR_CODE_INPUT_SIGNAL_ERR_MULTIPLE_SIGNALS = 69;
    ENUM_PNC_ERROR_CODE_V2G_ROOT_CERT_EXPIRING_SOON = 70;
    ENUM_PNC_ERROR_CODE_V2G_ROOT_CERT_CORRUPTED = 71;
    ENUM_PNC_ERROR_CODE_V2G_ROOT_CERT_EXPIRED = 72;
    ENUM_PNC_ERROR_CODE_V2G_ROOT_CERT_NOT_INSTALLED = 73;
    ENUM_PNC_ERROR_CODE_V2G_ROOT_CERT_MISSING = 74;
    ENUM_PNC_ERROR_CODE_OEM_ROOT_CERT_EXPIRING_SOON = 75;
    ENUM_PNC_ERROR_CODE_OEM_ROOT_CERT_CORRUPTED = 76;
    ENUM_PNC_ERROR_CODE_OEM_ROOT_CERT_EXPIRED = 77;
    ENUM_PNC_ERROR_CODE_OEM_ROOT_CERT_NOT_INSTALLED = 78;
    ENUM_PNC_ERROR_CODE_OEM_PROVISION_CERT_SUB_CA1_EXPIRING_SOON = 79;
    ENUM_PNC_ERROR_CODE_OEM_PROVISION_CERT_SUB_CA1_CORRUPTED = 80;
    ENUM_PNC_ERROR_CODE_OEM_PROVISION_CERT_SUB_CA1_EXPIRED = 81;
    ENUM_PNC_ERROR_CODE_OEM_PROVISION_CERT_SUB_CA1_NOT_INSTALLED = 82;
    ENUM_PNC_ERROR_CODE_OEM_PROVISION_CERT_SUB_CA2_EXPIRING_SOON = 83;
    ENUM_PNC_ERROR_CODE_OEM_PROVISION_CERT_SUB_CA2_CORRUPTED = 84;
    ENUM_PNC_ERROR_CODE_OEM_PROVISION_CERT_SUB_CA2_EXPIRED = 85;
    ENUM_PNC_ERROR_CODE_OEM_PROVISION_CERT_SUB_CA2_NOT_INSTALLED = 86;
    ENUM_PNC_ERROR_CODE_OEM_PROVISION_CERT_LEAF_EXPIRING_SOON = 87;
    ENUM_PNC_ERROR_CODE_OEM_PROVISION_CERT_LEAF_CORRUPTED = 88;
    ENUM_PNC_ERROR_CODE_OEM_PROVISION_CERT_LEAF_EXPIRED = 89;
    ENUM_PNC_ERROR_CODE_OEM_PROVISION_CERT_LEAF_NOT_INSTALLED = 90;
    ENUM_PNC_ERROR_CODE_OEM_PROVISION_CERT_PRIVATE_KEY_NOT_INSTALLED = 91;
    ENUM_PNC_ERROR_CODE_OEM_PROVISION_CERT_PRIVATE_KEY_CORRUPTED = 92;
}

/*****************************************************************************/
/*                                                                           */
/* The following section contains all the nested messages which need to be   */
/* used in various methods and notifications                                 */
/*                                                                           */
/*****************************************************************************/
message PredictedChargeData {
    // soc percentage at the indicated level in percentage (0 to 100)
    uint32 battery_level = 1;
    // predicted range at the indicated level in kms (0 to 1000)
    uint32 predicted_range = 2;
    // predicted time to reach the predicted range at the indicated level in minutes (0 to 3600)
    uint32 predicted_time = 3;
}

message ChargingCurveData {
    // state of charge as per the curve data
    // state of charge shall be sent in percentage (0 to 100%)
    optional uint32 state_of_charge = 1;
    // max power shall be sent in watts
    // maximum power  as per the curve data
    optional uint32 max_power = 2;
}
