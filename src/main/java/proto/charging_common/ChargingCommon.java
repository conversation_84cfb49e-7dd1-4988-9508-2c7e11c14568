// Generated by the protocol buffer compiler.  DO NOT EDIT!
// NO CHECKED-IN PROTOBUF GENCODE
// source: charging_common.proto
// Protobuf Java Version: 4.28.2

package charging_common;

public final class ChargingCommon {
  private ChargingCommon() {}
  static {
    com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
      com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
      /* major= */ 4,
      /* minor= */ 28,
      /* patch= */ 2,
      /* suffix= */ "",
      ChargingCommon.class.getName());
  }
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  /**
   * Protobuf enum {@code charging_common.EnumStatus}
   */
  public enum EnumStatus
      implements com.google.protobuf.ProtocolMessageEnum {
    /**
     * <code>ENUM_STATUS_UNSPECIFIED = 0;</code>
     */
    ENUM_STATUS_UNSPECIFIED(0),
    /**
     * <pre>
     * Normal return status indication, no error in operation and/or data is valid
     * Applies to all Response messages (Request/Response)
     * May apply to Notification messages (Pub/Sub) where data quality indications are also used – OK indicting good data
     * </pre>
     *
     * <code>ENUM_STATUS_OK = 1;</code>
     */
    ENUM_STATUS_OK(1),
    /**
     * <pre>
     * Data quality indication return status enumerations
     * May apply to Response messages (Request/Response)
     * May apply to Notification messages (Pub/Sub)
     * </pre>
     *
     * <code>ENUM_STATUS_DATA_DEGRADED = 2;</code>
     */
    ENUM_STATUS_DATA_DEGRADED(2),
    /**
     * <code>ENUM_STATUS_DATA_UNRELIABLE = 3;</code>
     */
    ENUM_STATUS_DATA_UNRELIABLE(3),
    /**
     * <code>ENUM_STATUS_DATA_UNAVAILABLE = 4;</code>
     */
    ENUM_STATUS_DATA_UNAVAILABLE(4),
    /**
     * <pre>
     * Operation errored and blocked due to some ‘inhibiting condition’ return status enumerations
     * Applies to Request/Response calls
     * </pre>
     *
     * <code>ENUM_STATUS_ERROR_INVALID_SERVICE_STATE = 5;</code>
     */
    ENUM_STATUS_ERROR_INVALID_SERVICE_STATE(5),
    /**
     * <code>ENUM_STATUS_ERROR_INVALID_VEHICLE_STATE = 6;</code>
     */
    ENUM_STATUS_ERROR_INVALID_VEHICLE_STATE(6),
    /**
     * <code>ENUM_STATUS_ERROR_INVALID_CAR_CONFIGURATION = 7;</code>
     */
    ENUM_STATUS_ERROR_INVALID_CAR_CONFIGURATION(7),
    /**
     * <pre>
     * Request message input field error return status enumerations
     * Applies to Request/Response calls
     * </pre>
     *
     * <code>ENUM_STATUS_ERROR_MISSING_INPUT_FIELD = 8;</code>
     */
    ENUM_STATUS_ERROR_MISSING_INPUT_FIELD(8),
    /**
     * <code>ENUM_STATUS_ERROR_INVALID_INPUT_FIELD = 9;</code>
     */
    ENUM_STATUS_ERROR_INVALID_INPUT_FIELD(9),
    /**
     * <pre>
     * Abnormal nonspecific return status indication
     * May indicate that an operation or method execution cannot be performed, or has errored, or is blocked at this time
     * Applies to Request/Response calls
     * </pre>
     *
     * <code>ENUM_STATUS_NOT_OK = 10;</code>
     */
    ENUM_STATUS_NOT_OK(10),
    UNRECOGNIZED(-1),
    ;

    static {
      com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
        com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
        /* major= */ 4,
        /* minor= */ 28,
        /* patch= */ 2,
        /* suffix= */ "",
        EnumStatus.class.getName());
    }
    /**
     * <code>ENUM_STATUS_UNSPECIFIED = 0;</code>
     */
    public static final int ENUM_STATUS_UNSPECIFIED_VALUE = 0;
    /**
     * <pre>
     * Normal return status indication, no error in operation and/or data is valid
     * Applies to all Response messages (Request/Response)
     * May apply to Notification messages (Pub/Sub) where data quality indications are also used – OK indicting good data
     * </pre>
     *
     * <code>ENUM_STATUS_OK = 1;</code>
     */
    public static final int ENUM_STATUS_OK_VALUE = 1;
    /**
     * <pre>
     * Data quality indication return status enumerations
     * May apply to Response messages (Request/Response)
     * May apply to Notification messages (Pub/Sub)
     * </pre>
     *
     * <code>ENUM_STATUS_DATA_DEGRADED = 2;</code>
     */
    public static final int ENUM_STATUS_DATA_DEGRADED_VALUE = 2;
    /**
     * <code>ENUM_STATUS_DATA_UNRELIABLE = 3;</code>
     */
    public static final int ENUM_STATUS_DATA_UNRELIABLE_VALUE = 3;
    /**
     * <code>ENUM_STATUS_DATA_UNAVAILABLE = 4;</code>
     */
    public static final int ENUM_STATUS_DATA_UNAVAILABLE_VALUE = 4;
    /**
     * <pre>
     * Operation errored and blocked due to some ‘inhibiting condition’ return status enumerations
     * Applies to Request/Response calls
     * </pre>
     *
     * <code>ENUM_STATUS_ERROR_INVALID_SERVICE_STATE = 5;</code>
     */
    public static final int ENUM_STATUS_ERROR_INVALID_SERVICE_STATE_VALUE = 5;
    /**
     * <code>ENUM_STATUS_ERROR_INVALID_VEHICLE_STATE = 6;</code>
     */
    public static final int ENUM_STATUS_ERROR_INVALID_VEHICLE_STATE_VALUE = 6;
    /**
     * <code>ENUM_STATUS_ERROR_INVALID_CAR_CONFIGURATION = 7;</code>
     */
    public static final int ENUM_STATUS_ERROR_INVALID_CAR_CONFIGURATION_VALUE = 7;
    /**
     * <pre>
     * Request message input field error return status enumerations
     * Applies to Request/Response calls
     * </pre>
     *
     * <code>ENUM_STATUS_ERROR_MISSING_INPUT_FIELD = 8;</code>
     */
    public static final int ENUM_STATUS_ERROR_MISSING_INPUT_FIELD_VALUE = 8;
    /**
     * <code>ENUM_STATUS_ERROR_INVALID_INPUT_FIELD = 9;</code>
     */
    public static final int ENUM_STATUS_ERROR_INVALID_INPUT_FIELD_VALUE = 9;
    /**
     * <pre>
     * Abnormal nonspecific return status indication
     * May indicate that an operation or method execution cannot be performed, or has errored, or is blocked at this time
     * Applies to Request/Response calls
     * </pre>
     *
     * <code>ENUM_STATUS_NOT_OK = 10;</code>
     */
    public static final int ENUM_STATUS_NOT_OK_VALUE = 10;


    public final int getNumber() {
      if (this == UNRECOGNIZED) {
        throw new java.lang.IllegalArgumentException(
            "Can't get the number of an unknown enum value.");
      }
      return value;
    }

    /**
     * @param value The numeric wire value of the corresponding enum entry.
     * @return The enum associated with the given numeric wire value.
     * @deprecated Use {@link #forNumber(int)} instead.
     */
    @java.lang.Deprecated
    public static EnumStatus valueOf(int value) {
      return forNumber(value);
    }

    /**
     * @param value The numeric wire value of the corresponding enum entry.
     * @return The enum associated with the given numeric wire value.
     */
    public static EnumStatus forNumber(int value) {
      switch (value) {
        case 0: return ENUM_STATUS_UNSPECIFIED;
        case 1: return ENUM_STATUS_OK;
        case 2: return ENUM_STATUS_DATA_DEGRADED;
        case 3: return ENUM_STATUS_DATA_UNRELIABLE;
        case 4: return ENUM_STATUS_DATA_UNAVAILABLE;
        case 5: return ENUM_STATUS_ERROR_INVALID_SERVICE_STATE;
        case 6: return ENUM_STATUS_ERROR_INVALID_VEHICLE_STATE;
        case 7: return ENUM_STATUS_ERROR_INVALID_CAR_CONFIGURATION;
        case 8: return ENUM_STATUS_ERROR_MISSING_INPUT_FIELD;
        case 9: return ENUM_STATUS_ERROR_INVALID_INPUT_FIELD;
        case 10: return ENUM_STATUS_NOT_OK;
        default: return null;
      }
    }

    public static com.google.protobuf.Internal.EnumLiteMap<EnumStatus>
        internalGetValueMap() {
      return internalValueMap;
    }
    private static final com.google.protobuf.Internal.EnumLiteMap<
        EnumStatus> internalValueMap =
          new com.google.protobuf.Internal.EnumLiteMap<EnumStatus>() {
            public EnumStatus findValueByNumber(int number) {
              return EnumStatus.forNumber(number);
            }
          };

    public final com.google.protobuf.Descriptors.EnumValueDescriptor
        getValueDescriptor() {
      if (this == UNRECOGNIZED) {
        throw new java.lang.IllegalStateException(
            "Can't get the descriptor of an unrecognized enum value.");
      }
      return getDescriptor().getValues().get(ordinal());
    }
    public final com.google.protobuf.Descriptors.EnumDescriptor
        getDescriptorForType() {
      return getDescriptor();
    }
    public static final com.google.protobuf.Descriptors.EnumDescriptor
        getDescriptor() {
      return charging_common.ChargingCommon.getDescriptor().getEnumTypes().get(0);
    }

    private static final EnumStatus[] VALUES = values();

    public static EnumStatus valueOf(
        com.google.protobuf.Descriptors.EnumValueDescriptor desc) {
      if (desc.getType() != getDescriptor()) {
        throw new java.lang.IllegalArgumentException(
          "EnumValueDescriptor is not for this type.");
      }
      if (desc.getIndex() == -1) {
        return UNRECOGNIZED;
      }
      return VALUES[desc.getIndex()];
    }

    private final int value;

    private EnumStatus(int value) {
      this.value = value;
    }

    // @@protoc_insertion_point(enum_scope:charging_common.EnumStatus)
  }

  /**
   * <pre>
   * This is used to request or indicate the charge mode of the vehicle
   * </pre>
   *
   * Protobuf enum {@code charging_common.EnumChargeControlOperation}
   */
  public enum EnumChargeControlOperation
      implements com.google.protobuf.ProtocolMessageEnum {
    /**
     * <code>ENUM_CHARGE_CONTROL_OPERATION_UNSPECIFIED = 0;</code>
     */
    ENUM_CHARGE_CONTROL_OPERATION_UNSPECIFIED(0),
    /**
     * <code>ENUM_CHARGE_CONTROL_OPERATION_START = 1;</code>
     */
    ENUM_CHARGE_CONTROL_OPERATION_START(1),
    /**
     * <code>ENUM_CHARGE_CONTROL_OPERATION_STOP = 2;</code>
     */
    ENUM_CHARGE_CONTROL_OPERATION_STOP(2),
    /**
     * <pre>
     * This enum is used to revert the charge mode between scheduled charging and immediate charging, vice-versa
     * This operation will be supported only when charging schedules are active in the vehicle
     * The client is expected to determine appropriate usage of "ENUM_CHARGE_CONTROL_OPERATION_STOP" Vs
     * "ENUM_CHARGE_CONTROL_OPERATION_REVERT"
     * </pre>
     *
     * <code>ENUM_CHARGE_CONTROL_OPERATION_REVERT = 3;</code>
     */
    ENUM_CHARGE_CONTROL_OPERATION_REVERT(3),
    UNRECOGNIZED(-1),
    ;

    static {
      com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
        com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
        /* major= */ 4,
        /* minor= */ 28,
        /* patch= */ 2,
        /* suffix= */ "",
        EnumChargeControlOperation.class.getName());
    }
    /**
     * <code>ENUM_CHARGE_CONTROL_OPERATION_UNSPECIFIED = 0;</code>
     */
    public static final int ENUM_CHARGE_CONTROL_OPERATION_UNSPECIFIED_VALUE = 0;
    /**
     * <code>ENUM_CHARGE_CONTROL_OPERATION_START = 1;</code>
     */
    public static final int ENUM_CHARGE_CONTROL_OPERATION_START_VALUE = 1;
    /**
     * <code>ENUM_CHARGE_CONTROL_OPERATION_STOP = 2;</code>
     */
    public static final int ENUM_CHARGE_CONTROL_OPERATION_STOP_VALUE = 2;
    /**
     * <pre>
     * This enum is used to revert the charge mode between scheduled charging and immediate charging, vice-versa
     * This operation will be supported only when charging schedules are active in the vehicle
     * The client is expected to determine appropriate usage of "ENUM_CHARGE_CONTROL_OPERATION_STOP" Vs
     * "ENUM_CHARGE_CONTROL_OPERATION_REVERT"
     * </pre>
     *
     * <code>ENUM_CHARGE_CONTROL_OPERATION_REVERT = 3;</code>
     */
    public static final int ENUM_CHARGE_CONTROL_OPERATION_REVERT_VALUE = 3;


    public final int getNumber() {
      if (this == UNRECOGNIZED) {
        throw new java.lang.IllegalArgumentException(
            "Can't get the number of an unknown enum value.");
      }
      return value;
    }

    /**
     * @param value The numeric wire value of the corresponding enum entry.
     * @return The enum associated with the given numeric wire value.
     * @deprecated Use {@link #forNumber(int)} instead.
     */
    @java.lang.Deprecated
    public static EnumChargeControlOperation valueOf(int value) {
      return forNumber(value);
    }

    /**
     * @param value The numeric wire value of the corresponding enum entry.
     * @return The enum associated with the given numeric wire value.
     */
    public static EnumChargeControlOperation forNumber(int value) {
      switch (value) {
        case 0: return ENUM_CHARGE_CONTROL_OPERATION_UNSPECIFIED;
        case 1: return ENUM_CHARGE_CONTROL_OPERATION_START;
        case 2: return ENUM_CHARGE_CONTROL_OPERATION_STOP;
        case 3: return ENUM_CHARGE_CONTROL_OPERATION_REVERT;
        default: return null;
      }
    }

    public static com.google.protobuf.Internal.EnumLiteMap<EnumChargeControlOperation>
        internalGetValueMap() {
      return internalValueMap;
    }
    private static final com.google.protobuf.Internal.EnumLiteMap<
        EnumChargeControlOperation> internalValueMap =
          new com.google.protobuf.Internal.EnumLiteMap<EnumChargeControlOperation>() {
            public EnumChargeControlOperation findValueByNumber(int number) {
              return EnumChargeControlOperation.forNumber(number);
            }
          };

    public final com.google.protobuf.Descriptors.EnumValueDescriptor
        getValueDescriptor() {
      if (this == UNRECOGNIZED) {
        throw new java.lang.IllegalStateException(
            "Can't get the descriptor of an unrecognized enum value.");
      }
      return getDescriptor().getValues().get(ordinal());
    }
    public final com.google.protobuf.Descriptors.EnumDescriptor
        getDescriptorForType() {
      return getDescriptor();
    }
    public static final com.google.protobuf.Descriptors.EnumDescriptor
        getDescriptor() {
      return charging_common.ChargingCommon.getDescriptor().getEnumTypes().get(1);
    }

    private static final EnumChargeControlOperation[] VALUES = values();

    public static EnumChargeControlOperation valueOf(
        com.google.protobuf.Descriptors.EnumValueDescriptor desc) {
      if (desc.getType() != getDescriptor()) {
        throw new java.lang.IllegalArgumentException(
          "EnumValueDescriptor is not for this type.");
      }
      if (desc.getIndex() == -1) {
        return UNRECOGNIZED;
      }
      return VALUES[desc.getIndex()];
    }

    private final int value;

    private EnumChargeControlOperation(int value) {
      this.value = value;
    }

    // @@protoc_insertion_point(enum_scope:charging_common.EnumChargeControlOperation)
  }

  /**
   * <pre>
   * This is used to indicate how the charge control request was initiated
   * </pre>
   *
   * Protobuf enum {@code charging_common.EnumChargeContext}
   */
  public enum EnumChargeContext
      implements com.google.protobuf.ProtocolMessageEnum {
    /**
     * <code>ENUM_CHARGE_CONTEXT_UNSPECIFIED = 0;</code>
     */
    ENUM_CHARGE_CONTEXT_UNSPECIFIED(0),
    /**
     * <pre>
     * to indicate when charge control (start/stop) is initiated by the user
     * </pre>
     *
     * <code>ENUM_CHARGE_CONTEXT_USER = 1;</code>
     */
    ENUM_CHARGE_CONTEXT_USER(1),
    /**
     * <pre>
     * to indicate when charge control (start/stop) is initiated by an external
     * system (eg: Octopus Energy)
     * </pre>
     *
     * <code>ENUM_CHARGE_CONTEXT_SYSTEM = 2;</code>
     */
    ENUM_CHARGE_CONTEXT_SYSTEM(2),
    UNRECOGNIZED(-1),
    ;

    static {
      com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
        com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
        /* major= */ 4,
        /* minor= */ 28,
        /* patch= */ 2,
        /* suffix= */ "",
        EnumChargeContext.class.getName());
    }
    /**
     * <code>ENUM_CHARGE_CONTEXT_UNSPECIFIED = 0;</code>
     */
    public static final int ENUM_CHARGE_CONTEXT_UNSPECIFIED_VALUE = 0;
    /**
     * <pre>
     * to indicate when charge control (start/stop) is initiated by the user
     * </pre>
     *
     * <code>ENUM_CHARGE_CONTEXT_USER = 1;</code>
     */
    public static final int ENUM_CHARGE_CONTEXT_USER_VALUE = 1;
    /**
     * <pre>
     * to indicate when charge control (start/stop) is initiated by an external
     * system (eg: Octopus Energy)
     * </pre>
     *
     * <code>ENUM_CHARGE_CONTEXT_SYSTEM = 2;</code>
     */
    public static final int ENUM_CHARGE_CONTEXT_SYSTEM_VALUE = 2;


    public final int getNumber() {
      if (this == UNRECOGNIZED) {
        throw new java.lang.IllegalArgumentException(
            "Can't get the number of an unknown enum value.");
      }
      return value;
    }

    /**
     * @param value The numeric wire value of the corresponding enum entry.
     * @return The enum associated with the given numeric wire value.
     * @deprecated Use {@link #forNumber(int)} instead.
     */
    @java.lang.Deprecated
    public static EnumChargeContext valueOf(int value) {
      return forNumber(value);
    }

    /**
     * @param value The numeric wire value of the corresponding enum entry.
     * @return The enum associated with the given numeric wire value.
     */
    public static EnumChargeContext forNumber(int value) {
      switch (value) {
        case 0: return ENUM_CHARGE_CONTEXT_UNSPECIFIED;
        case 1: return ENUM_CHARGE_CONTEXT_USER;
        case 2: return ENUM_CHARGE_CONTEXT_SYSTEM;
        default: return null;
      }
    }

    public static com.google.protobuf.Internal.EnumLiteMap<EnumChargeContext>
        internalGetValueMap() {
      return internalValueMap;
    }
    private static final com.google.protobuf.Internal.EnumLiteMap<
        EnumChargeContext> internalValueMap =
          new com.google.protobuf.Internal.EnumLiteMap<EnumChargeContext>() {
            public EnumChargeContext findValueByNumber(int number) {
              return EnumChargeContext.forNumber(number);
            }
          };

    public final com.google.protobuf.Descriptors.EnumValueDescriptor
        getValueDescriptor() {
      if (this == UNRECOGNIZED) {
        throw new java.lang.IllegalStateException(
            "Can't get the descriptor of an unrecognized enum value.");
      }
      return getDescriptor().getValues().get(ordinal());
    }
    public final com.google.protobuf.Descriptors.EnumDescriptor
        getDescriptorForType() {
      return getDescriptor();
    }
    public static final com.google.protobuf.Descriptors.EnumDescriptor
        getDescriptor() {
      return charging_common.ChargingCommon.getDescriptor().getEnumTypes().get(2);
    }

    private static final EnumChargeContext[] VALUES = values();

    public static EnumChargeContext valueOf(
        com.google.protobuf.Descriptors.EnumValueDescriptor desc) {
      if (desc.getType() != getDescriptor()) {
        throw new java.lang.IllegalArgumentException(
          "EnumValueDescriptor is not for this type.");
      }
      if (desc.getIndex() == -1) {
        return UNRECOGNIZED;
      }
      return VALUES[desc.getIndex()];
    }

    private final int value;

    private EnumChargeContext(int value) {
      this.value = value;
    }

    // @@protoc_insertion_point(enum_scope:charging_common.EnumChargeContext)
  }

  /**
   * <pre>
   * This is used to indicate the status of the charging system within the vehicle
   * </pre>
   *
   * Protobuf enum {@code charging_common.EnumChargeState}
   */
  public enum EnumChargeState
      implements com.google.protobuf.ProtocolMessageEnum {
    /**
     * <code>ENUM_CHARGE_STATE_UNSPECIFIED = 0;</code>
     */
    ENUM_CHARGE_STATE_UNSPECIFIED(0),
    /**
     * <pre>
     * No active/ongoing charge session
     * </pre>
     *
     * <code>ENUM_CHARGE_STATE_DEFAULT = 1;</code>
     */
    ENUM_CHARGE_STATE_DEFAULT(1),
    /**
     * <pre>
     * used to indicate when the charging system is initializing
     * </pre>
     *
     * <code>ENUM_CHARGE_STATE_INITIALIZING = 2;</code>
     */
    ENUM_CHARGE_STATE_INITIALIZING(2),
    /**
     * <pre>
     * used to indicate when charging system is connected to a charger
     * but charging has not yet started
     * </pre>
     *
     * <code>ENUM_CHARGE_STATE_WAITING_TO_CHARGE = 3;</code>
     */
    ENUM_CHARGE_STATE_WAITING_TO_CHARGE(3),
    /**
     * <pre>
     * indicates when charging system is connected to a charger
     * and charging is triggered by the user but charging station
     * is yet to trigger current
     * </pre>
     *
     * <code>ENUM_CHARGE_STATE_WAITING_FOR_CHARGE_STATION = 4;</code>
     */
    ENUM_CHARGE_STATE_WAITING_FOR_CHARGE_STATION(4),
    /**
     * <pre>
     * indicates when charging is in progress
     * </pre>
     *
     * <code>ENUM_CHARGE_STATE_CHARGE_IN_PROGRESS = 5;</code>
     */
    ENUM_CHARGE_STATE_CHARGE_IN_PROGRESS(5),
    /**
     * <pre>
     * Charging session is paused  i.e; Charging won't happen until vehicle
     * receives charge start requests such as start command from user OR Charge
     * cable removed and plug it back again
     * </pre>
     *
     * <code>ENUM_CHARGE_STATE_CHARGE_STOPPED = 6;</code>
     */
    ENUM_CHARGE_STATE_CHARGE_STOPPED(6),
    /**
     * <pre>
     * indicates when charging system has reached the target charge level
     * </pre>
     *
     * <code>ENUM_CHARGE_STATE_CHARGE_COMPLETE = 7;</code>
     */
    ENUM_CHARGE_STATE_CHARGE_COMPLETE(7),
    /**
     * <pre>
     * indicates when charging system has encountered a charging fault
     * EnumChargeErrorMode is used to indicate the exact error encountered
     * </pre>
     *
     * <code>ENUM_CHARGE_STATE_CHARGE_ERROR = 8;</code>
     */
    ENUM_CHARGE_STATE_CHARGE_ERROR(8),
    /**
     * <pre>
     * indicates when the charging system is waiting to discharge
     * </pre>
     *
     * <code>ENUM_CHARGE_STATE_WAITING_TO_DISCHARGE = 9;</code>
     */
    ENUM_CHARGE_STATE_WAITING_TO_DISCHARGE(9),
    /**
     * <pre>
     * indicates when discharging is in progress
     * </pre>
     *
     * <code>ENUM_CHARGE_STATE_DISCHARGE_IN_PROGRESS = 10;</code>
     */
    ENUM_CHARGE_STATE_DISCHARGE_IN_PROGRESS(10),
    /**
     * <pre>
     * indicates when discharge is complete
     * </pre>
     *
     * <code>ENUM_CHARGE_STATE_DISCHARGE_COMPLETE = 11;</code>
     */
    ENUM_CHARGE_STATE_DISCHARGE_COMPLETE(11),
    /**
     * <pre>
     * indicates when charging system has encountered a discharging fault
     * EnumChargeErrorMode is used to indicate the exact error encountered
     * </pre>
     *
     * <code>ENUM_CHARGE_STATE_DISCHARGE_ERROR = 12;</code>
     */
    ENUM_CHARGE_STATE_DISCHARGE_ERROR(12),
    /**
     * <pre>
     * this enum is used to indicate when charging system is forced to charge due to
     * user requesting for immediate charge even though a schedule is set
     * this is a temporary state and the charging system will revert back to scheduled
     * charge once the charging is complete or user cancels the ongoing forced charge
     * session
     * </pre>
     *
     * <code>ENUM_CHARGE_STATE_FORCED_CHARGE_IN_PROGRESS = 13;</code>
     */
    ENUM_CHARGE_STATE_FORCED_CHARGE_IN_PROGRESS(13),
    /**
     * <pre>
     * indicates when the charging system is waiting for payment
     * </pre>
     *
     * <code>ENUM_CHARGE_STATE_WAITING_FOR_PAYMENT = 14;</code>
     */
    ENUM_CHARGE_STATE_WAITING_FOR_PAYMENT(14),
    UNRECOGNIZED(-1),
    ;

    static {
      com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
        com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
        /* major= */ 4,
        /* minor= */ 28,
        /* patch= */ 2,
        /* suffix= */ "",
        EnumChargeState.class.getName());
    }
    /**
     * <code>ENUM_CHARGE_STATE_UNSPECIFIED = 0;</code>
     */
    public static final int ENUM_CHARGE_STATE_UNSPECIFIED_VALUE = 0;
    /**
     * <pre>
     * No active/ongoing charge session
     * </pre>
     *
     * <code>ENUM_CHARGE_STATE_DEFAULT = 1;</code>
     */
    public static final int ENUM_CHARGE_STATE_DEFAULT_VALUE = 1;
    /**
     * <pre>
     * used to indicate when the charging system is initializing
     * </pre>
     *
     * <code>ENUM_CHARGE_STATE_INITIALIZING = 2;</code>
     */
    public static final int ENUM_CHARGE_STATE_INITIALIZING_VALUE = 2;
    /**
     * <pre>
     * used to indicate when charging system is connected to a charger
     * but charging has not yet started
     * </pre>
     *
     * <code>ENUM_CHARGE_STATE_WAITING_TO_CHARGE = 3;</code>
     */
    public static final int ENUM_CHARGE_STATE_WAITING_TO_CHARGE_VALUE = 3;
    /**
     * <pre>
     * indicates when charging system is connected to a charger
     * and charging is triggered by the user but charging station
     * is yet to trigger current
     * </pre>
     *
     * <code>ENUM_CHARGE_STATE_WAITING_FOR_CHARGE_STATION = 4;</code>
     */
    public static final int ENUM_CHARGE_STATE_WAITING_FOR_CHARGE_STATION_VALUE = 4;
    /**
     * <pre>
     * indicates when charging is in progress
     * </pre>
     *
     * <code>ENUM_CHARGE_STATE_CHARGE_IN_PROGRESS = 5;</code>
     */
    public static final int ENUM_CHARGE_STATE_CHARGE_IN_PROGRESS_VALUE = 5;
    /**
     * <pre>
     * Charging session is paused  i.e; Charging won't happen until vehicle
     * receives charge start requests such as start command from user OR Charge
     * cable removed and plug it back again
     * </pre>
     *
     * <code>ENUM_CHARGE_STATE_CHARGE_STOPPED = 6;</code>
     */
    public static final int ENUM_CHARGE_STATE_CHARGE_STOPPED_VALUE = 6;
    /**
     * <pre>
     * indicates when charging system has reached the target charge level
     * </pre>
     *
     * <code>ENUM_CHARGE_STATE_CHARGE_COMPLETE = 7;</code>
     */
    public static final int ENUM_CHARGE_STATE_CHARGE_COMPLETE_VALUE = 7;
    /**
     * <pre>
     * indicates when charging system has encountered a charging fault
     * EnumChargeErrorMode is used to indicate the exact error encountered
     * </pre>
     *
     * <code>ENUM_CHARGE_STATE_CHARGE_ERROR = 8;</code>
     */
    public static final int ENUM_CHARGE_STATE_CHARGE_ERROR_VALUE = 8;
    /**
     * <pre>
     * indicates when the charging system is waiting to discharge
     * </pre>
     *
     * <code>ENUM_CHARGE_STATE_WAITING_TO_DISCHARGE = 9;</code>
     */
    public static final int ENUM_CHARGE_STATE_WAITING_TO_DISCHARGE_VALUE = 9;
    /**
     * <pre>
     * indicates when discharging is in progress
     * </pre>
     *
     * <code>ENUM_CHARGE_STATE_DISCHARGE_IN_PROGRESS = 10;</code>
     */
    public static final int ENUM_CHARGE_STATE_DISCHARGE_IN_PROGRESS_VALUE = 10;
    /**
     * <pre>
     * indicates when discharge is complete
     * </pre>
     *
     * <code>ENUM_CHARGE_STATE_DISCHARGE_COMPLETE = 11;</code>
     */
    public static final int ENUM_CHARGE_STATE_DISCHARGE_COMPLETE_VALUE = 11;
    /**
     * <pre>
     * indicates when charging system has encountered a discharging fault
     * EnumChargeErrorMode is used to indicate the exact error encountered
     * </pre>
     *
     * <code>ENUM_CHARGE_STATE_DISCHARGE_ERROR = 12;</code>
     */
    public static final int ENUM_CHARGE_STATE_DISCHARGE_ERROR_VALUE = 12;
    /**
     * <pre>
     * this enum is used to indicate when charging system is forced to charge due to
     * user requesting for immediate charge even though a schedule is set
     * this is a temporary state and the charging system will revert back to scheduled
     * charge once the charging is complete or user cancels the ongoing forced charge
     * session
     * </pre>
     *
     * <code>ENUM_CHARGE_STATE_FORCED_CHARGE_IN_PROGRESS = 13;</code>
     */
    public static final int ENUM_CHARGE_STATE_FORCED_CHARGE_IN_PROGRESS_VALUE = 13;
    /**
     * <pre>
     * indicates when the charging system is waiting for payment
     * </pre>
     *
     * <code>ENUM_CHARGE_STATE_WAITING_FOR_PAYMENT = 14;</code>
     */
    public static final int ENUM_CHARGE_STATE_WAITING_FOR_PAYMENT_VALUE = 14;


    public final int getNumber() {
      if (this == UNRECOGNIZED) {
        throw new java.lang.IllegalArgumentException(
            "Can't get the number of an unknown enum value.");
      }
      return value;
    }

    /**
     * @param value The numeric wire value of the corresponding enum entry.
     * @return The enum associated with the given numeric wire value.
     * @deprecated Use {@link #forNumber(int)} instead.
     */
    @java.lang.Deprecated
    public static EnumChargeState valueOf(int value) {
      return forNumber(value);
    }

    /**
     * @param value The numeric wire value of the corresponding enum entry.
     * @return The enum associated with the given numeric wire value.
     */
    public static EnumChargeState forNumber(int value) {
      switch (value) {
        case 0: return ENUM_CHARGE_STATE_UNSPECIFIED;
        case 1: return ENUM_CHARGE_STATE_DEFAULT;
        case 2: return ENUM_CHARGE_STATE_INITIALIZING;
        case 3: return ENUM_CHARGE_STATE_WAITING_TO_CHARGE;
        case 4: return ENUM_CHARGE_STATE_WAITING_FOR_CHARGE_STATION;
        case 5: return ENUM_CHARGE_STATE_CHARGE_IN_PROGRESS;
        case 6: return ENUM_CHARGE_STATE_CHARGE_STOPPED;
        case 7: return ENUM_CHARGE_STATE_CHARGE_COMPLETE;
        case 8: return ENUM_CHARGE_STATE_CHARGE_ERROR;
        case 9: return ENUM_CHARGE_STATE_WAITING_TO_DISCHARGE;
        case 10: return ENUM_CHARGE_STATE_DISCHARGE_IN_PROGRESS;
        case 11: return ENUM_CHARGE_STATE_DISCHARGE_COMPLETE;
        case 12: return ENUM_CHARGE_STATE_DISCHARGE_ERROR;
        case 13: return ENUM_CHARGE_STATE_FORCED_CHARGE_IN_PROGRESS;
        case 14: return ENUM_CHARGE_STATE_WAITING_FOR_PAYMENT;
        default: return null;
      }
    }

    public static com.google.protobuf.Internal.EnumLiteMap<EnumChargeState>
        internalGetValueMap() {
      return internalValueMap;
    }
    private static final com.google.protobuf.Internal.EnumLiteMap<
        EnumChargeState> internalValueMap =
          new com.google.protobuf.Internal.EnumLiteMap<EnumChargeState>() {
            public EnumChargeState findValueByNumber(int number) {
              return EnumChargeState.forNumber(number);
            }
          };

    public final com.google.protobuf.Descriptors.EnumValueDescriptor
        getValueDescriptor() {
      if (this == UNRECOGNIZED) {
        throw new java.lang.IllegalStateException(
            "Can't get the descriptor of an unrecognized enum value.");
      }
      return getDescriptor().getValues().get(ordinal());
    }
    public final com.google.protobuf.Descriptors.EnumDescriptor
        getDescriptorForType() {
      return getDescriptor();
    }
    public static final com.google.protobuf.Descriptors.EnumDescriptor
        getDescriptor() {
      return charging_common.ChargingCommon.getDescriptor().getEnumTypes().get(3);
    }

    private static final EnumChargeState[] VALUES = values();

    public static EnumChargeState valueOf(
        com.google.protobuf.Descriptors.EnumValueDescriptor desc) {
      if (desc.getType() != getDescriptor()) {
        throw new java.lang.IllegalArgumentException(
          "EnumValueDescriptor is not for this type.");
      }
      if (desc.getIndex() == -1) {
        return UNRECOGNIZED;
      }
      return VALUES[desc.getIndex()];
    }

    private final int value;

    private EnumChargeState(int value) {
      this.value = value;
    }

    // @@protoc_insertion_point(enum_scope:charging_common.EnumChargeState)
  }

  /**
   * <pre>
   * This is used to indicate any errors in the charging mode
   * </pre>
   *
   * Protobuf enum {@code charging_common.EnumChargeErrorMode}
   */
  public enum EnumChargeErrorMode
      implements com.google.protobuf.ProtocolMessageEnum {
    /**
     * <code>ENUM_CHARGE_ERROR_MODE_UNSPECIFIED = 0;</code>
     */
    ENUM_CHARGE_ERROR_MODE_UNSPECIFIED(0),
    /**
     * <code>ENUM_CHARGE_ERROR_MODE_NO_ERROR = 1;</code>
     */
    ENUM_CHARGE_ERROR_MODE_NO_ERROR(1),
    /**
     * <code>ENUM_CHARGE_ERROR_MODE_SYSTEM_ERROR = 2;</code>
     */
    ENUM_CHARGE_ERROR_MODE_SYSTEM_ERROR(2),
    /**
     * <code>ENUM_CHARGE_ERROR_MODE_CHARGE_STATION_ERROR = 3;</code>
     */
    ENUM_CHARGE_ERROR_MODE_CHARGE_STATION_ERROR(3),
    /**
     * <code>ENUM_CHARGE_ERROR_MODE_DISCHARGE_ERROR = 4;</code>
     */
    ENUM_CHARGE_ERROR_MODE_DISCHARGE_ERROR(4),
    /**
     * <code>ENUM_CHARGE_ERROR_MODE_PLUG_LOCK_FAILURE = 5;</code>
     */
    ENUM_CHARGE_ERROR_MODE_PLUG_LOCK_FAILURE(5),
    /**
     * <code>ENUM_CHARGE_ERROR_MODE_PLUG_UNLOCK_FAILURE = 6;</code>
     */
    ENUM_CHARGE_ERROR_MODE_PLUG_UNLOCK_FAILURE(6),
    /**
     * <code>ENUM_CHARGE_ERROR_MODE_MANUAL_PAYMENT_FAILED = 7;</code>
     */
    ENUM_CHARGE_ERROR_MODE_MANUAL_PAYMENT_FAILED(7),
    /**
     * <code>ENUM_CHARGE_ERROR_MODE_AUTOMATE_PAYMENT_FAILED = 8;</code>
     */
    ENUM_CHARGE_ERROR_MODE_AUTOMATE_PAYMENT_FAILED(8),
    /**
     * <code>ENUM_CHARGE_ERROR_MODE_BATTERY_TEMP_WARNING = 9;</code>
     */
    ENUM_CHARGE_ERROR_MODE_BATTERY_TEMP_WARNING(9),
    /**
     * <code>ENUM_CHARGE_ERROR_MODE_GENERAL_INFO = 10;</code>
     */
    ENUM_CHARGE_ERROR_MODE_GENERAL_INFO(10),
    /**
     * <code>ENUM_CHARGE_ERROR_MODE_CHARGE_DOOR_ERROR = 11;</code>
     */
    ENUM_CHARGE_ERROR_MODE_CHARGE_DOOR_ERROR(11),
    UNRECOGNIZED(-1),
    ;

    static {
      com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
        com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
        /* major= */ 4,
        /* minor= */ 28,
        /* patch= */ 2,
        /* suffix= */ "",
        EnumChargeErrorMode.class.getName());
    }
    /**
     * <code>ENUM_CHARGE_ERROR_MODE_UNSPECIFIED = 0;</code>
     */
    public static final int ENUM_CHARGE_ERROR_MODE_UNSPECIFIED_VALUE = 0;
    /**
     * <code>ENUM_CHARGE_ERROR_MODE_NO_ERROR = 1;</code>
     */
    public static final int ENUM_CHARGE_ERROR_MODE_NO_ERROR_VALUE = 1;
    /**
     * <code>ENUM_CHARGE_ERROR_MODE_SYSTEM_ERROR = 2;</code>
     */
    public static final int ENUM_CHARGE_ERROR_MODE_SYSTEM_ERROR_VALUE = 2;
    /**
     * <code>ENUM_CHARGE_ERROR_MODE_CHARGE_STATION_ERROR = 3;</code>
     */
    public static final int ENUM_CHARGE_ERROR_MODE_CHARGE_STATION_ERROR_VALUE = 3;
    /**
     * <code>ENUM_CHARGE_ERROR_MODE_DISCHARGE_ERROR = 4;</code>
     */
    public static final int ENUM_CHARGE_ERROR_MODE_DISCHARGE_ERROR_VALUE = 4;
    /**
     * <code>ENUM_CHARGE_ERROR_MODE_PLUG_LOCK_FAILURE = 5;</code>
     */
    public static final int ENUM_CHARGE_ERROR_MODE_PLUG_LOCK_FAILURE_VALUE = 5;
    /**
     * <code>ENUM_CHARGE_ERROR_MODE_PLUG_UNLOCK_FAILURE = 6;</code>
     */
    public static final int ENUM_CHARGE_ERROR_MODE_PLUG_UNLOCK_FAILURE_VALUE = 6;
    /**
     * <code>ENUM_CHARGE_ERROR_MODE_MANUAL_PAYMENT_FAILED = 7;</code>
     */
    public static final int ENUM_CHARGE_ERROR_MODE_MANUAL_PAYMENT_FAILED_VALUE = 7;
    /**
     * <code>ENUM_CHARGE_ERROR_MODE_AUTOMATE_PAYMENT_FAILED = 8;</code>
     */
    public static final int ENUM_CHARGE_ERROR_MODE_AUTOMATE_PAYMENT_FAILED_VALUE = 8;
    /**
     * <code>ENUM_CHARGE_ERROR_MODE_BATTERY_TEMP_WARNING = 9;</code>
     */
    public static final int ENUM_CHARGE_ERROR_MODE_BATTERY_TEMP_WARNING_VALUE = 9;
    /**
     * <code>ENUM_CHARGE_ERROR_MODE_GENERAL_INFO = 10;</code>
     */
    public static final int ENUM_CHARGE_ERROR_MODE_GENERAL_INFO_VALUE = 10;
    /**
     * <code>ENUM_CHARGE_ERROR_MODE_CHARGE_DOOR_ERROR = 11;</code>
     */
    public static final int ENUM_CHARGE_ERROR_MODE_CHARGE_DOOR_ERROR_VALUE = 11;


    public final int getNumber() {
      if (this == UNRECOGNIZED) {
        throw new java.lang.IllegalArgumentException(
            "Can't get the number of an unknown enum value.");
      }
      return value;
    }

    /**
     * @param value The numeric wire value of the corresponding enum entry.
     * @return The enum associated with the given numeric wire value.
     * @deprecated Use {@link #forNumber(int)} instead.
     */
    @java.lang.Deprecated
    public static EnumChargeErrorMode valueOf(int value) {
      return forNumber(value);
    }

    /**
     * @param value The numeric wire value of the corresponding enum entry.
     * @return The enum associated with the given numeric wire value.
     */
    public static EnumChargeErrorMode forNumber(int value) {
      switch (value) {
        case 0: return ENUM_CHARGE_ERROR_MODE_UNSPECIFIED;
        case 1: return ENUM_CHARGE_ERROR_MODE_NO_ERROR;
        case 2: return ENUM_CHARGE_ERROR_MODE_SYSTEM_ERROR;
        case 3: return ENUM_CHARGE_ERROR_MODE_CHARGE_STATION_ERROR;
        case 4: return ENUM_CHARGE_ERROR_MODE_DISCHARGE_ERROR;
        case 5: return ENUM_CHARGE_ERROR_MODE_PLUG_LOCK_FAILURE;
        case 6: return ENUM_CHARGE_ERROR_MODE_PLUG_UNLOCK_FAILURE;
        case 7: return ENUM_CHARGE_ERROR_MODE_MANUAL_PAYMENT_FAILED;
        case 8: return ENUM_CHARGE_ERROR_MODE_AUTOMATE_PAYMENT_FAILED;
        case 9: return ENUM_CHARGE_ERROR_MODE_BATTERY_TEMP_WARNING;
        case 10: return ENUM_CHARGE_ERROR_MODE_GENERAL_INFO;
        case 11: return ENUM_CHARGE_ERROR_MODE_CHARGE_DOOR_ERROR;
        default: return null;
      }
    }

    public static com.google.protobuf.Internal.EnumLiteMap<EnumChargeErrorMode>
        internalGetValueMap() {
      return internalValueMap;
    }
    private static final com.google.protobuf.Internal.EnumLiteMap<
        EnumChargeErrorMode> internalValueMap =
          new com.google.protobuf.Internal.EnumLiteMap<EnumChargeErrorMode>() {
            public EnumChargeErrorMode findValueByNumber(int number) {
              return EnumChargeErrorMode.forNumber(number);
            }
          };

    public final com.google.protobuf.Descriptors.EnumValueDescriptor
        getValueDescriptor() {
      if (this == UNRECOGNIZED) {
        throw new java.lang.IllegalStateException(
            "Can't get the descriptor of an unrecognized enum value.");
      }
      return getDescriptor().getValues().get(ordinal());
    }
    public final com.google.protobuf.Descriptors.EnumDescriptor
        getDescriptorForType() {
      return getDescriptor();
    }
    public static final com.google.protobuf.Descriptors.EnumDescriptor
        getDescriptor() {
      return charging_common.ChargingCommon.getDescriptor().getEnumTypes().get(4);
    }

    private static final EnumChargeErrorMode[] VALUES = values();

    public static EnumChargeErrorMode valueOf(
        com.google.protobuf.Descriptors.EnumValueDescriptor desc) {
      if (desc.getType() != getDescriptor()) {
        throw new java.lang.IllegalArgumentException(
          "EnumValueDescriptor is not for this type.");
      }
      if (desc.getIndex() == -1) {
        return UNRECOGNIZED;
      }
      return VALUES[desc.getIndex()];
    }

    private final int value;

    private EnumChargeErrorMode(int value) {
      this.value = value;
    }

    // @@protoc_insertion_point(enum_scope:charging_common.EnumChargeErrorMode)
  }

  /**
   * <pre>
   * This is used to indicate the status of the charging inlet
   * </pre>
   *
   * Protobuf enum {@code charging_common.EnumChargingInletState}
   */
  public enum EnumChargingInletState
      implements com.google.protobuf.ProtocolMessageEnum {
    /**
     * <code>ENUM_CHARGING_INLET_STATE_UNSPECIFIED = 0;</code>
     */
    ENUM_CHARGING_INLET_STATE_UNSPECIFIED(0),
    /**
     * <pre>
     * Charger is unplugged
     * </pre>
     *
     * <code>ENUM_CHARGING_INLET_STATE_UNPLUGGED = 1;</code>
     */
    ENUM_CHARGING_INLET_STATE_UNPLUGGED(1),
    /**
     * <pre>
     * Charger is plugged in
     * </pre>
     *
     * <code>ENUM_CHARGING_INLET_STATE_PLUGGED = 2;</code>
     */
    ENUM_CHARGING_INLET_STATE_PLUGGED(2),
    /**
     * <pre>
     * V2L adaptor is plugged in
     * </pre>
     *
     * <code>ENUM_CHARGING_INLET_STATE_ADAPTOR_PLUGGED = 3;</code>
     */
    ENUM_CHARGING_INLET_STATE_ADAPTOR_PLUGGED(3),
    UNRECOGNIZED(-1),
    ;

    static {
      com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
        com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
        /* major= */ 4,
        /* minor= */ 28,
        /* patch= */ 2,
        /* suffix= */ "",
        EnumChargingInletState.class.getName());
    }
    /**
     * <code>ENUM_CHARGING_INLET_STATE_UNSPECIFIED = 0;</code>
     */
    public static final int ENUM_CHARGING_INLET_STATE_UNSPECIFIED_VALUE = 0;
    /**
     * <pre>
     * Charger is unplugged
     * </pre>
     *
     * <code>ENUM_CHARGING_INLET_STATE_UNPLUGGED = 1;</code>
     */
    public static final int ENUM_CHARGING_INLET_STATE_UNPLUGGED_VALUE = 1;
    /**
     * <pre>
     * Charger is plugged in
     * </pre>
     *
     * <code>ENUM_CHARGING_INLET_STATE_PLUGGED = 2;</code>
     */
    public static final int ENUM_CHARGING_INLET_STATE_PLUGGED_VALUE = 2;
    /**
     * <pre>
     * V2L adaptor is plugged in
     * </pre>
     *
     * <code>ENUM_CHARGING_INLET_STATE_ADAPTOR_PLUGGED = 3;</code>
     */
    public static final int ENUM_CHARGING_INLET_STATE_ADAPTOR_PLUGGED_VALUE = 3;


    public final int getNumber() {
      if (this == UNRECOGNIZED) {
        throw new java.lang.IllegalArgumentException(
            "Can't get the number of an unknown enum value.");
      }
      return value;
    }

    /**
     * @param value The numeric wire value of the corresponding enum entry.
     * @return The enum associated with the given numeric wire value.
     * @deprecated Use {@link #forNumber(int)} instead.
     */
    @java.lang.Deprecated
    public static EnumChargingInletState valueOf(int value) {
      return forNumber(value);
    }

    /**
     * @param value The numeric wire value of the corresponding enum entry.
     * @return The enum associated with the given numeric wire value.
     */
    public static EnumChargingInletState forNumber(int value) {
      switch (value) {
        case 0: return ENUM_CHARGING_INLET_STATE_UNSPECIFIED;
        case 1: return ENUM_CHARGING_INLET_STATE_UNPLUGGED;
        case 2: return ENUM_CHARGING_INLET_STATE_PLUGGED;
        case 3: return ENUM_CHARGING_INLET_STATE_ADAPTOR_PLUGGED;
        default: return null;
      }
    }

    public static com.google.protobuf.Internal.EnumLiteMap<EnumChargingInletState>
        internalGetValueMap() {
      return internalValueMap;
    }
    private static final com.google.protobuf.Internal.EnumLiteMap<
        EnumChargingInletState> internalValueMap =
          new com.google.protobuf.Internal.EnumLiteMap<EnumChargingInletState>() {
            public EnumChargingInletState findValueByNumber(int number) {
              return EnumChargingInletState.forNumber(number);
            }
          };

    public final com.google.protobuf.Descriptors.EnumValueDescriptor
        getValueDescriptor() {
      if (this == UNRECOGNIZED) {
        throw new java.lang.IllegalStateException(
            "Can't get the descriptor of an unrecognized enum value.");
      }
      return getDescriptor().getValues().get(ordinal());
    }
    public final com.google.protobuf.Descriptors.EnumDescriptor
        getDescriptorForType() {
      return getDescriptor();
    }
    public static final com.google.protobuf.Descriptors.EnumDescriptor
        getDescriptor() {
      return charging_common.ChargingCommon.getDescriptor().getEnumTypes().get(5);
    }

    private static final EnumChargingInletState[] VALUES = values();

    public static EnumChargingInletState valueOf(
        com.google.protobuf.Descriptors.EnumValueDescriptor desc) {
      if (desc.getType() != getDescriptor()) {
        throw new java.lang.IllegalArgumentException(
          "EnumValueDescriptor is not for this type.");
      }
      if (desc.getIndex() == -1) {
        return UNRECOGNIZED;
      }
      return VALUES[desc.getIndex()];
    }

    private final int value;

    private EnumChargingInletState(int value) {
      this.value = value;
    }

    // @@protoc_insertion_point(enum_scope:charging_common.EnumChargingInletState)
  }

  /**
   * <pre>
   * This is used to request or indicate the charge mode of the vehicle
   * </pre>
   *
   * Protobuf enum {@code charging_common.EnumChargeType}
   */
  public enum EnumChargeType
      implements com.google.protobuf.ProtocolMessageEnum {
    /**
     * <code>ENUM_CHARGE_TYPE_UNSPECIFIED = 0;</code>
     */
    ENUM_CHARGE_TYPE_UNSPECIFIED(0),
    /**
     * <pre>
     * Start charging as soon charger is connected
     * </pre>
     *
     * <code>ENUM_CHARGE_TYPE_IMMEDIATE = 1;</code>
     */
    ENUM_CHARGE_TYPE_IMMEDIATE(1),
    /**
     * <pre>
     * Fixed schedule is as per time set by user (this does not consider target soc and dep time)
     * </pre>
     *
     * <code>ENUM_CHARGE_TYPE_FIXED_SCHEDULE = 2;</code>
     */
    ENUM_CHARGE_TYPE_FIXED_SCHEDULE(2),
    /**
     * <pre>
     * This will consider the target soc to achieve, dep time and low cost hours to determine start time
     * </pre>
     *
     * <code>ENUM_CHARGE_TYPE_SCHEDULE_PLUS = 3;</code>
     */
    ENUM_CHARGE_TYPE_SCHEDULE_PLUS(3),
    /**
     * <pre>
     * This is based on third party control of charging hours
     * </pre>
     *
     * <code>ENUM_CHARGE_TYPE_SMART_SCHEDULE = 4;</code>
     */
    ENUM_CHARGE_TYPE_SMART_SCHEDULE(4),
    /**
     * <code>ENUM_CHARGE_TYPE_BIDIRECTIONAL_SCHEDULE = 5;</code>
     */
    ENUM_CHARGE_TYPE_BIDIRECTIONAL_SCHEDULE(5),
    UNRECOGNIZED(-1),
    ;

    static {
      com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
        com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
        /* major= */ 4,
        /* minor= */ 28,
        /* patch= */ 2,
        /* suffix= */ "",
        EnumChargeType.class.getName());
    }
    /**
     * <code>ENUM_CHARGE_TYPE_UNSPECIFIED = 0;</code>
     */
    public static final int ENUM_CHARGE_TYPE_UNSPECIFIED_VALUE = 0;
    /**
     * <pre>
     * Start charging as soon charger is connected
     * </pre>
     *
     * <code>ENUM_CHARGE_TYPE_IMMEDIATE = 1;</code>
     */
    public static final int ENUM_CHARGE_TYPE_IMMEDIATE_VALUE = 1;
    /**
     * <pre>
     * Fixed schedule is as per time set by user (this does not consider target soc and dep time)
     * </pre>
     *
     * <code>ENUM_CHARGE_TYPE_FIXED_SCHEDULE = 2;</code>
     */
    public static final int ENUM_CHARGE_TYPE_FIXED_SCHEDULE_VALUE = 2;
    /**
     * <pre>
     * This will consider the target soc to achieve, dep time and low cost hours to determine start time
     * </pre>
     *
     * <code>ENUM_CHARGE_TYPE_SCHEDULE_PLUS = 3;</code>
     */
    public static final int ENUM_CHARGE_TYPE_SCHEDULE_PLUS_VALUE = 3;
    /**
     * <pre>
     * This is based on third party control of charging hours
     * </pre>
     *
     * <code>ENUM_CHARGE_TYPE_SMART_SCHEDULE = 4;</code>
     */
    public static final int ENUM_CHARGE_TYPE_SMART_SCHEDULE_VALUE = 4;
    /**
     * <code>ENUM_CHARGE_TYPE_BIDIRECTIONAL_SCHEDULE = 5;</code>
     */
    public static final int ENUM_CHARGE_TYPE_BIDIRECTIONAL_SCHEDULE_VALUE = 5;


    public final int getNumber() {
      if (this == UNRECOGNIZED) {
        throw new java.lang.IllegalArgumentException(
            "Can't get the number of an unknown enum value.");
      }
      return value;
    }

    /**
     * @param value The numeric wire value of the corresponding enum entry.
     * @return The enum associated with the given numeric wire value.
     * @deprecated Use {@link #forNumber(int)} instead.
     */
    @java.lang.Deprecated
    public static EnumChargeType valueOf(int value) {
      return forNumber(value);
    }

    /**
     * @param value The numeric wire value of the corresponding enum entry.
     * @return The enum associated with the given numeric wire value.
     */
    public static EnumChargeType forNumber(int value) {
      switch (value) {
        case 0: return ENUM_CHARGE_TYPE_UNSPECIFIED;
        case 1: return ENUM_CHARGE_TYPE_IMMEDIATE;
        case 2: return ENUM_CHARGE_TYPE_FIXED_SCHEDULE;
        case 3: return ENUM_CHARGE_TYPE_SCHEDULE_PLUS;
        case 4: return ENUM_CHARGE_TYPE_SMART_SCHEDULE;
        case 5: return ENUM_CHARGE_TYPE_BIDIRECTIONAL_SCHEDULE;
        default: return null;
      }
    }

    public static com.google.protobuf.Internal.EnumLiteMap<EnumChargeType>
        internalGetValueMap() {
      return internalValueMap;
    }
    private static final com.google.protobuf.Internal.EnumLiteMap<
        EnumChargeType> internalValueMap =
          new com.google.protobuf.Internal.EnumLiteMap<EnumChargeType>() {
            public EnumChargeType findValueByNumber(int number) {
              return EnumChargeType.forNumber(number);
            }
          };

    public final com.google.protobuf.Descriptors.EnumValueDescriptor
        getValueDescriptor() {
      if (this == UNRECOGNIZED) {
        throw new java.lang.IllegalStateException(
            "Can't get the descriptor of an unrecognized enum value.");
      }
      return getDescriptor().getValues().get(ordinal());
    }
    public final com.google.protobuf.Descriptors.EnumDescriptor
        getDescriptorForType() {
      return getDescriptor();
    }
    public static final com.google.protobuf.Descriptors.EnumDescriptor
        getDescriptor() {
      return charging_common.ChargingCommon.getDescriptor().getEnumTypes().get(6);
    }

    private static final EnumChargeType[] VALUES = values();

    public static EnumChargeType valueOf(
        com.google.protobuf.Descriptors.EnumValueDescriptor desc) {
      if (desc.getType() != getDescriptor()) {
        throw new java.lang.IllegalArgumentException(
          "EnumValueDescriptor is not for this type.");
      }
      if (desc.getIndex() == -1) {
        return UNRECOGNIZED;
      }
      return VALUES[desc.getIndex()];
    }

    private final int value;

    private EnumChargeType(int value) {
      this.value = value;
    }

    // @@protoc_insertion_point(enum_scope:charging_common.EnumChargeType)
  }

  /**
   * <pre>
   * This is used to indicate how the vehicle is being charged
   * </pre>
   *
   * Protobuf enum {@code charging_common.EnumChargingMethod}
   */
  public enum EnumChargingMethod
      implements com.google.protobuf.ProtocolMessageEnum {
    /**
     * <code>ENUM_CHARGING_METHOD_UNSPECIFIED = 0;</code>
     */
    ENUM_CHARGING_METHOD_UNSPECIFIED(0),
    /**
     * <pre>
     * Unknown charging type
     * </pre>
     *
     * <code>ENUM_CHARGING_METHOD_NOT_CHARGING = 1;</code>
     */
    ENUM_CHARGING_METHOD_NOT_CHARGING(1),
    /**
     * <pre>
     * AC Single phase, 2 phase or 3 phase charging
     * </pre>
     *
     * <code>ENUM_CHARGING_METHOD_AC_CHARGING = 2;</code>
     */
    ENUM_CHARGING_METHOD_AC_CHARGING(2),
    /**
     * <pre>
     * DC CCS1 charging, CCS2 charging, GBT charging, CHAdeMO charging
     * </pre>
     *
     * <code>ENUM_CHARGING_METHOD_DC_CHARGING = 3;</code>
     */
    ENUM_CHARGING_METHOD_DC_CHARGING(3),
    /**
     * <pre>
     * Wireless charging
     * </pre>
     *
     * <code>ENUM_CHARGING_METHOD_WIRELESS_CHARGING = 4;</code>
     */
    ENUM_CHARGING_METHOD_WIRELESS_CHARGING(4),
    UNRECOGNIZED(-1),
    ;

    static {
      com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
        com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
        /* major= */ 4,
        /* minor= */ 28,
        /* patch= */ 2,
        /* suffix= */ "",
        EnumChargingMethod.class.getName());
    }
    /**
     * <code>ENUM_CHARGING_METHOD_UNSPECIFIED = 0;</code>
     */
    public static final int ENUM_CHARGING_METHOD_UNSPECIFIED_VALUE = 0;
    /**
     * <pre>
     * Unknown charging type
     * </pre>
     *
     * <code>ENUM_CHARGING_METHOD_NOT_CHARGING = 1;</code>
     */
    public static final int ENUM_CHARGING_METHOD_NOT_CHARGING_VALUE = 1;
    /**
     * <pre>
     * AC Single phase, 2 phase or 3 phase charging
     * </pre>
     *
     * <code>ENUM_CHARGING_METHOD_AC_CHARGING = 2;</code>
     */
    public static final int ENUM_CHARGING_METHOD_AC_CHARGING_VALUE = 2;
    /**
     * <pre>
     * DC CCS1 charging, CCS2 charging, GBT charging, CHAdeMO charging
     * </pre>
     *
     * <code>ENUM_CHARGING_METHOD_DC_CHARGING = 3;</code>
     */
    public static final int ENUM_CHARGING_METHOD_DC_CHARGING_VALUE = 3;
    /**
     * <pre>
     * Wireless charging
     * </pre>
     *
     * <code>ENUM_CHARGING_METHOD_WIRELESS_CHARGING = 4;</code>
     */
    public static final int ENUM_CHARGING_METHOD_WIRELESS_CHARGING_VALUE = 4;


    public final int getNumber() {
      if (this == UNRECOGNIZED) {
        throw new java.lang.IllegalArgumentException(
            "Can't get the number of an unknown enum value.");
      }
      return value;
    }

    /**
     * @param value The numeric wire value of the corresponding enum entry.
     * @return The enum associated with the given numeric wire value.
     * @deprecated Use {@link #forNumber(int)} instead.
     */
    @java.lang.Deprecated
    public static EnumChargingMethod valueOf(int value) {
      return forNumber(value);
    }

    /**
     * @param value The numeric wire value of the corresponding enum entry.
     * @return The enum associated with the given numeric wire value.
     */
    public static EnumChargingMethod forNumber(int value) {
      switch (value) {
        case 0: return ENUM_CHARGING_METHOD_UNSPECIFIED;
        case 1: return ENUM_CHARGING_METHOD_NOT_CHARGING;
        case 2: return ENUM_CHARGING_METHOD_AC_CHARGING;
        case 3: return ENUM_CHARGING_METHOD_DC_CHARGING;
        case 4: return ENUM_CHARGING_METHOD_WIRELESS_CHARGING;
        default: return null;
      }
    }

    public static com.google.protobuf.Internal.EnumLiteMap<EnumChargingMethod>
        internalGetValueMap() {
      return internalValueMap;
    }
    private static final com.google.protobuf.Internal.EnumLiteMap<
        EnumChargingMethod> internalValueMap =
          new com.google.protobuf.Internal.EnumLiteMap<EnumChargingMethod>() {
            public EnumChargingMethod findValueByNumber(int number) {
              return EnumChargingMethod.forNumber(number);
            }
          };

    public final com.google.protobuf.Descriptors.EnumValueDescriptor
        getValueDescriptor() {
      if (this == UNRECOGNIZED) {
        throw new java.lang.IllegalStateException(
            "Can't get the descriptor of an unrecognized enum value.");
      }
      return getDescriptor().getValues().get(ordinal());
    }
    public final com.google.protobuf.Descriptors.EnumDescriptor
        getDescriptorForType() {
      return getDescriptor();
    }
    public static final com.google.protobuf.Descriptors.EnumDescriptor
        getDescriptor() {
      return charging_common.ChargingCommon.getDescriptor().getEnumTypes().get(7);
    }

    private static final EnumChargingMethod[] VALUES = values();

    public static EnumChargingMethod valueOf(
        com.google.protobuf.Descriptors.EnumValueDescriptor desc) {
      if (desc.getType() != getDescriptor()) {
        throw new java.lang.IllegalArgumentException(
          "EnumValueDescriptor is not for this type.");
      }
      if (desc.getIndex() == -1) {
        return UNRECOGNIZED;
      }
      return VALUES[desc.getIndex()];
    }

    private final int value;

    private EnumChargingMethod(int value) {
      this.value = value;
    }

    // @@protoc_insertion_point(enum_scope:charging_common.EnumChargingMethod)
  }

  /**
   * <pre>
   * This is used to identify the charge flap of the charging inlet
   * Charge ports will be market/program specific
   * In some cases they may be on the right, in some cases on the left
   * while in some cases both may be present for different
   * technologies/protocols of charging
   * Eg: EMA and/or MLA may have either one or both sides supported for the NA market
   * Likewise EMA and/or MLA may have either one or both sides supported for the Japan market
   * </pre>
   *
   * Protobuf enum {@code charging_common.EnumChargeDoor}
   */
  public enum EnumChargeDoor
      implements com.google.protobuf.ProtocolMessageEnum {
    /**
     * <code>ENUM_CHARGE_DOOR_UNSPECIFIED = 0;</code>
     */
    ENUM_CHARGE_DOOR_UNSPECIFIED(0),
    /**
     * <pre>
     * Charge door on the left side of the vehicle
     * </pre>
     *
     * <code>ENUM_CHARGE_DOOR_LEFT = 1;</code>
     */
    ENUM_CHARGE_DOOR_LEFT(1),
    /**
     * <pre>
     * Charge door on the right side of the vehicle
     * </pre>
     *
     * <code>ENUM_CHARGE_DOOR_RIGHT = 2;</code>
     */
    ENUM_CHARGE_DOOR_RIGHT(2),
    UNRECOGNIZED(-1),
    ;

    static {
      com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
        com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
        /* major= */ 4,
        /* minor= */ 28,
        /* patch= */ 2,
        /* suffix= */ "",
        EnumChargeDoor.class.getName());
    }
    /**
     * <code>ENUM_CHARGE_DOOR_UNSPECIFIED = 0;</code>
     */
    public static final int ENUM_CHARGE_DOOR_UNSPECIFIED_VALUE = 0;
    /**
     * <pre>
     * Charge door on the left side of the vehicle
     * </pre>
     *
     * <code>ENUM_CHARGE_DOOR_LEFT = 1;</code>
     */
    public static final int ENUM_CHARGE_DOOR_LEFT_VALUE = 1;
    /**
     * <pre>
     * Charge door on the right side of the vehicle
     * </pre>
     *
     * <code>ENUM_CHARGE_DOOR_RIGHT = 2;</code>
     */
    public static final int ENUM_CHARGE_DOOR_RIGHT_VALUE = 2;


    public final int getNumber() {
      if (this == UNRECOGNIZED) {
        throw new java.lang.IllegalArgumentException(
            "Can't get the number of an unknown enum value.");
      }
      return value;
    }

    /**
     * @param value The numeric wire value of the corresponding enum entry.
     * @return The enum associated with the given numeric wire value.
     * @deprecated Use {@link #forNumber(int)} instead.
     */
    @java.lang.Deprecated
    public static EnumChargeDoor valueOf(int value) {
      return forNumber(value);
    }

    /**
     * @param value The numeric wire value of the corresponding enum entry.
     * @return The enum associated with the given numeric wire value.
     */
    public static EnumChargeDoor forNumber(int value) {
      switch (value) {
        case 0: return ENUM_CHARGE_DOOR_UNSPECIFIED;
        case 1: return ENUM_CHARGE_DOOR_LEFT;
        case 2: return ENUM_CHARGE_DOOR_RIGHT;
        default: return null;
      }
    }

    public static com.google.protobuf.Internal.EnumLiteMap<EnumChargeDoor>
        internalGetValueMap() {
      return internalValueMap;
    }
    private static final com.google.protobuf.Internal.EnumLiteMap<
        EnumChargeDoor> internalValueMap =
          new com.google.protobuf.Internal.EnumLiteMap<EnumChargeDoor>() {
            public EnumChargeDoor findValueByNumber(int number) {
              return EnumChargeDoor.forNumber(number);
            }
          };

    public final com.google.protobuf.Descriptors.EnumValueDescriptor
        getValueDescriptor() {
      if (this == UNRECOGNIZED) {
        throw new java.lang.IllegalStateException(
            "Can't get the descriptor of an unrecognized enum value.");
      }
      return getDescriptor().getValues().get(ordinal());
    }
    public final com.google.protobuf.Descriptors.EnumDescriptor
        getDescriptorForType() {
      return getDescriptor();
    }
    public static final com.google.protobuf.Descriptors.EnumDescriptor
        getDescriptor() {
      return charging_common.ChargingCommon.getDescriptor().getEnumTypes().get(8);
    }

    private static final EnumChargeDoor[] VALUES = values();

    public static EnumChargeDoor valueOf(
        com.google.protobuf.Descriptors.EnumValueDescriptor desc) {
      if (desc.getType() != getDescriptor()) {
        throw new java.lang.IllegalArgumentException(
          "EnumValueDescriptor is not for this type.");
      }
      if (desc.getIndex() == -1) {
        return UNRECOGNIZED;
      }
      return VALUES[desc.getIndex()];
    }

    private final int value;

    private EnumChargeDoor(int value) {
      this.value = value;
    }

    // @@protoc_insertion_point(enum_scope:charging_common.EnumChargeDoor)
  }

  /**
   * <pre>
   * This is used to request or indicate the operation on the charge door of the vehicle
   * </pre>
   *
   * Protobuf enum {@code charging_common.EnumChargeDoorOperation}
   */
  public enum EnumChargeDoorOperation
      implements com.google.protobuf.ProtocolMessageEnum {
    /**
     * <code>ENUM_CHARGE_DOOR_OPERATION_UNSPECIFIED = 0;</code>
     */
    ENUM_CHARGE_DOOR_OPERATION_UNSPECIFIED(0),
    /**
     * <pre>
     * Open charge door
     * can also be used to indicate a warning to the user if charge door is open
     * when it is supposed to be closed
     * </pre>
     *
     * <code>ENUM_CHARGE_DOOR_OPERATION_OPEN = 1;</code>
     */
    ENUM_CHARGE_DOOR_OPERATION_OPEN(1),
    /**
     * <pre>
     * Close charge door
     * </pre>
     *
     * <code>ENUM_CHARGE_DOOR_OPERATION_CLOSE = 2;</code>
     */
    ENUM_CHARGE_DOOR_OPERATION_CLOSE(2),
    /**
     * <pre>
     * charge door operation ongoing
     * </pre>
     *
     * <code>ENUM_CHARGE_DOOR_OPERATION_IN_PROGRESS = 3;</code>
     */
    ENUM_CHARGE_DOOR_OPERATION_IN_PROGRESS(3),
    /**
     * <pre>
     * charge door error
     * </pre>
     *
     * <code>ENUM_CHARGE_DOOR_OPERATION_ERROR = 4;</code>
     */
    ENUM_CHARGE_DOOR_OPERATION_ERROR(4),
    UNRECOGNIZED(-1),
    ;

    static {
      com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
        com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
        /* major= */ 4,
        /* minor= */ 28,
        /* patch= */ 2,
        /* suffix= */ "",
        EnumChargeDoorOperation.class.getName());
    }
    /**
     * <code>ENUM_CHARGE_DOOR_OPERATION_UNSPECIFIED = 0;</code>
     */
    public static final int ENUM_CHARGE_DOOR_OPERATION_UNSPECIFIED_VALUE = 0;
    /**
     * <pre>
     * Open charge door
     * can also be used to indicate a warning to the user if charge door is open
     * when it is supposed to be closed
     * </pre>
     *
     * <code>ENUM_CHARGE_DOOR_OPERATION_OPEN = 1;</code>
     */
    public static final int ENUM_CHARGE_DOOR_OPERATION_OPEN_VALUE = 1;
    /**
     * <pre>
     * Close charge door
     * </pre>
     *
     * <code>ENUM_CHARGE_DOOR_OPERATION_CLOSE = 2;</code>
     */
    public static final int ENUM_CHARGE_DOOR_OPERATION_CLOSE_VALUE = 2;
    /**
     * <pre>
     * charge door operation ongoing
     * </pre>
     *
     * <code>ENUM_CHARGE_DOOR_OPERATION_IN_PROGRESS = 3;</code>
     */
    public static final int ENUM_CHARGE_DOOR_OPERATION_IN_PROGRESS_VALUE = 3;
    /**
     * <pre>
     * charge door error
     * </pre>
     *
     * <code>ENUM_CHARGE_DOOR_OPERATION_ERROR = 4;</code>
     */
    public static final int ENUM_CHARGE_DOOR_OPERATION_ERROR_VALUE = 4;


    public final int getNumber() {
      if (this == UNRECOGNIZED) {
        throw new java.lang.IllegalArgumentException(
            "Can't get the number of an unknown enum value.");
      }
      return value;
    }

    /**
     * @param value The numeric wire value of the corresponding enum entry.
     * @return The enum associated with the given numeric wire value.
     * @deprecated Use {@link #forNumber(int)} instead.
     */
    @java.lang.Deprecated
    public static EnumChargeDoorOperation valueOf(int value) {
      return forNumber(value);
    }

    /**
     * @param value The numeric wire value of the corresponding enum entry.
     * @return The enum associated with the given numeric wire value.
     */
    public static EnumChargeDoorOperation forNumber(int value) {
      switch (value) {
        case 0: return ENUM_CHARGE_DOOR_OPERATION_UNSPECIFIED;
        case 1: return ENUM_CHARGE_DOOR_OPERATION_OPEN;
        case 2: return ENUM_CHARGE_DOOR_OPERATION_CLOSE;
        case 3: return ENUM_CHARGE_DOOR_OPERATION_IN_PROGRESS;
        case 4: return ENUM_CHARGE_DOOR_OPERATION_ERROR;
        default: return null;
      }
    }

    public static com.google.protobuf.Internal.EnumLiteMap<EnumChargeDoorOperation>
        internalGetValueMap() {
      return internalValueMap;
    }
    private static final com.google.protobuf.Internal.EnumLiteMap<
        EnumChargeDoorOperation> internalValueMap =
          new com.google.protobuf.Internal.EnumLiteMap<EnumChargeDoorOperation>() {
            public EnumChargeDoorOperation findValueByNumber(int number) {
              return EnumChargeDoorOperation.forNumber(number);
            }
          };

    public final com.google.protobuf.Descriptors.EnumValueDescriptor
        getValueDescriptor() {
      if (this == UNRECOGNIZED) {
        throw new java.lang.IllegalStateException(
            "Can't get the descriptor of an unrecognized enum value.");
      }
      return getDescriptor().getValues().get(ordinal());
    }
    public final com.google.protobuf.Descriptors.EnumDescriptor
        getDescriptorForType() {
      return getDescriptor();
    }
    public static final com.google.protobuf.Descriptors.EnumDescriptor
        getDescriptor() {
      return charging_common.ChargingCommon.getDescriptor().getEnumTypes().get(9);
    }

    private static final EnumChargeDoorOperation[] VALUES = values();

    public static EnumChargeDoorOperation valueOf(
        com.google.protobuf.Descriptors.EnumValueDescriptor desc) {
      if (desc.getType() != getDescriptor()) {
        throw new java.lang.IllegalArgumentException(
          "EnumValueDescriptor is not for this type.");
      }
      if (desc.getIndex() == -1) {
        return UNRECOGNIZED;
      }
      return VALUES[desc.getIndex()];
    }

    private final int value;

    private EnumChargeDoorOperation(int value) {
      this.value = value;
    }

    // @@protoc_insertion_point(enum_scope:charging_common.EnumChargeDoorOperation)
  }

  /**
   * <pre>
   * This is used to request or indicate the operation on the charge door of the vehicle
   * </pre>
   *
   * Protobuf enum {@code charging_common.EnumChargeCableOperation}
   */
  public enum EnumChargeCableOperation
      implements com.google.protobuf.ProtocolMessageEnum {
    /**
     * <code>ENUM_CHARGE_CABLE_OPERATION_UNSPECIFIED = 0;</code>
     */
    ENUM_CHARGE_CABLE_OPERATION_UNSPECIFIED(0),
    /**
     * <pre>
     * charge cable unlock
     * </pre>
     *
     * <code>ENUM_CHARGE_CABLE_OPERATION_UNLOCK = 1;</code>
     */
    ENUM_CHARGE_CABLE_OPERATION_UNLOCK(1),
    /**
     * <pre>
     * charge cable lock
     * </pre>
     *
     * <code>ENUM_CHARGE_CABLE_OPERATION_LOCK = 2;</code>
     */
    ENUM_CHARGE_CABLE_OPERATION_LOCK(2),
    /**
     * <pre>
     * charge cable error
     * </pre>
     *
     * <code>ENUM_CHARGE_CABLE_OPERATION_ERROR = 3;</code>
     */
    ENUM_CHARGE_CABLE_OPERATION_ERROR(3),
    UNRECOGNIZED(-1),
    ;

    static {
      com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
        com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
        /* major= */ 4,
        /* minor= */ 28,
        /* patch= */ 2,
        /* suffix= */ "",
        EnumChargeCableOperation.class.getName());
    }
    /**
     * <code>ENUM_CHARGE_CABLE_OPERATION_UNSPECIFIED = 0;</code>
     */
    public static final int ENUM_CHARGE_CABLE_OPERATION_UNSPECIFIED_VALUE = 0;
    /**
     * <pre>
     * charge cable unlock
     * </pre>
     *
     * <code>ENUM_CHARGE_CABLE_OPERATION_UNLOCK = 1;</code>
     */
    public static final int ENUM_CHARGE_CABLE_OPERATION_UNLOCK_VALUE = 1;
    /**
     * <pre>
     * charge cable lock
     * </pre>
     *
     * <code>ENUM_CHARGE_CABLE_OPERATION_LOCK = 2;</code>
     */
    public static final int ENUM_CHARGE_CABLE_OPERATION_LOCK_VALUE = 2;
    /**
     * <pre>
     * charge cable error
     * </pre>
     *
     * <code>ENUM_CHARGE_CABLE_OPERATION_ERROR = 3;</code>
     */
    public static final int ENUM_CHARGE_CABLE_OPERATION_ERROR_VALUE = 3;


    public final int getNumber() {
      if (this == UNRECOGNIZED) {
        throw new java.lang.IllegalArgumentException(
            "Can't get the number of an unknown enum value.");
      }
      return value;
    }

    /**
     * @param value The numeric wire value of the corresponding enum entry.
     * @return The enum associated with the given numeric wire value.
     * @deprecated Use {@link #forNumber(int)} instead.
     */
    @java.lang.Deprecated
    public static EnumChargeCableOperation valueOf(int value) {
      return forNumber(value);
    }

    /**
     * @param value The numeric wire value of the corresponding enum entry.
     * @return The enum associated with the given numeric wire value.
     */
    public static EnumChargeCableOperation forNumber(int value) {
      switch (value) {
        case 0: return ENUM_CHARGE_CABLE_OPERATION_UNSPECIFIED;
        case 1: return ENUM_CHARGE_CABLE_OPERATION_UNLOCK;
        case 2: return ENUM_CHARGE_CABLE_OPERATION_LOCK;
        case 3: return ENUM_CHARGE_CABLE_OPERATION_ERROR;
        default: return null;
      }
    }

    public static com.google.protobuf.Internal.EnumLiteMap<EnumChargeCableOperation>
        internalGetValueMap() {
      return internalValueMap;
    }
    private static final com.google.protobuf.Internal.EnumLiteMap<
        EnumChargeCableOperation> internalValueMap =
          new com.google.protobuf.Internal.EnumLiteMap<EnumChargeCableOperation>() {
            public EnumChargeCableOperation findValueByNumber(int number) {
              return EnumChargeCableOperation.forNumber(number);
            }
          };

    public final com.google.protobuf.Descriptors.EnumValueDescriptor
        getValueDescriptor() {
      if (this == UNRECOGNIZED) {
        throw new java.lang.IllegalStateException(
            "Can't get the descriptor of an unrecognized enum value.");
      }
      return getDescriptor().getValues().get(ordinal());
    }
    public final com.google.protobuf.Descriptors.EnumDescriptor
        getDescriptorForType() {
      return getDescriptor();
    }
    public static final com.google.protobuf.Descriptors.EnumDescriptor
        getDescriptor() {
      return charging_common.ChargingCommon.getDescriptor().getEnumTypes().get(10);
    }

    private static final EnumChargeCableOperation[] VALUES = values();

    public static EnumChargeCableOperation valueOf(
        com.google.protobuf.Descriptors.EnumValueDescriptor desc) {
      if (desc.getType() != getDescriptor()) {
        throw new java.lang.IllegalArgumentException(
          "EnumValueDescriptor is not for this type.");
      }
      if (desc.getIndex() == -1) {
        return UNRECOGNIZED;
      }
      return VALUES[desc.getIndex()];
    }

    private final int value;

    private EnumChargeCableOperation(int value) {
      this.value = value;
    }

    // @@protoc_insertion_point(enum_scope:charging_common.EnumChargeCableOperation)
  }

  /**
   * <pre>
   * This is used to request or indicate the user payment choice on the vehicle
   * </pre>
   *
   * Protobuf enum {@code charging_common.EnumPnCPaymentChoice}
   */
  public enum EnumPnCPaymentChoice
      implements com.google.protobuf.ProtocolMessageEnum {
    /**
     * <code>ENUM_PNC_PAYMENT_CHOICE_UNSPECIFIED = 0;</code>
     */
    ENUM_PNC_PAYMENT_CHOICE_UNSPECIFIED(0),
    /**
     * <pre>
     * Plug N Charge payment choice is disabled
     * </pre>
     *
     * <code>ENUM_PNC_PAYMENT_CHOICE_DISABLED = 1;</code>
     */
    ENUM_PNC_PAYMENT_CHOICE_DISABLED(1),
    /**
     * <pre>
     * Plug N Charge payment choice is enabled
     * </pre>
     *
     * <code>ENUM_PNC_PAYMENT_CHOICE_ENABLED = 2;</code>
     */
    ENUM_PNC_PAYMENT_CHOICE_ENABLED(2),
    UNRECOGNIZED(-1),
    ;

    static {
      com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
        com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
        /* major= */ 4,
        /* minor= */ 28,
        /* patch= */ 2,
        /* suffix= */ "",
        EnumPnCPaymentChoice.class.getName());
    }
    /**
     * <code>ENUM_PNC_PAYMENT_CHOICE_UNSPECIFIED = 0;</code>
     */
    public static final int ENUM_PNC_PAYMENT_CHOICE_UNSPECIFIED_VALUE = 0;
    /**
     * <pre>
     * Plug N Charge payment choice is disabled
     * </pre>
     *
     * <code>ENUM_PNC_PAYMENT_CHOICE_DISABLED = 1;</code>
     */
    public static final int ENUM_PNC_PAYMENT_CHOICE_DISABLED_VALUE = 1;
    /**
     * <pre>
     * Plug N Charge payment choice is enabled
     * </pre>
     *
     * <code>ENUM_PNC_PAYMENT_CHOICE_ENABLED = 2;</code>
     */
    public static final int ENUM_PNC_PAYMENT_CHOICE_ENABLED_VALUE = 2;


    public final int getNumber() {
      if (this == UNRECOGNIZED) {
        throw new java.lang.IllegalArgumentException(
            "Can't get the number of an unknown enum value.");
      }
      return value;
    }

    /**
     * @param value The numeric wire value of the corresponding enum entry.
     * @return The enum associated with the given numeric wire value.
     * @deprecated Use {@link #forNumber(int)} instead.
     */
    @java.lang.Deprecated
    public static EnumPnCPaymentChoice valueOf(int value) {
      return forNumber(value);
    }

    /**
     * @param value The numeric wire value of the corresponding enum entry.
     * @return The enum associated with the given numeric wire value.
     */
    public static EnumPnCPaymentChoice forNumber(int value) {
      switch (value) {
        case 0: return ENUM_PNC_PAYMENT_CHOICE_UNSPECIFIED;
        case 1: return ENUM_PNC_PAYMENT_CHOICE_DISABLED;
        case 2: return ENUM_PNC_PAYMENT_CHOICE_ENABLED;
        default: return null;
      }
    }

    public static com.google.protobuf.Internal.EnumLiteMap<EnumPnCPaymentChoice>
        internalGetValueMap() {
      return internalValueMap;
    }
    private static final com.google.protobuf.Internal.EnumLiteMap<
        EnumPnCPaymentChoice> internalValueMap =
          new com.google.protobuf.Internal.EnumLiteMap<EnumPnCPaymentChoice>() {
            public EnumPnCPaymentChoice findValueByNumber(int number) {
              return EnumPnCPaymentChoice.forNumber(number);
            }
          };

    public final com.google.protobuf.Descriptors.EnumValueDescriptor
        getValueDescriptor() {
      if (this == UNRECOGNIZED) {
        throw new java.lang.IllegalStateException(
            "Can't get the descriptor of an unrecognized enum value.");
      }
      return getDescriptor().getValues().get(ordinal());
    }
    public final com.google.protobuf.Descriptors.EnumDescriptor
        getDescriptorForType() {
      return getDescriptor();
    }
    public static final com.google.protobuf.Descriptors.EnumDescriptor
        getDescriptor() {
      return charging_common.ChargingCommon.getDescriptor().getEnumTypes().get(11);
    }

    private static final EnumPnCPaymentChoice[] VALUES = values();

    public static EnumPnCPaymentChoice valueOf(
        com.google.protobuf.Descriptors.EnumValueDescriptor desc) {
      if (desc.getType() != getDescriptor()) {
        throw new java.lang.IllegalArgumentException(
          "EnumValueDescriptor is not for this type.");
      }
      if (desc.getIndex() == -1) {
        return UNRECOGNIZED;
      }
      return VALUES[desc.getIndex()];
    }

    private final int value;

    private EnumPnCPaymentChoice(int value) {
      this.value = value;
    }

    // @@protoc_insertion_point(enum_scope:charging_common.EnumPnCPaymentChoice)
  }

  /**
   * <pre>
   * This is used to request or indicate the payment method is Plug n Charge
   * or External Identification Mode
   * </pre>
   *
   * Protobuf enum {@code charging_common.EnumPnCPaymentMethod}
   */
  public enum EnumPnCPaymentMethod
      implements com.google.protobuf.ProtocolMessageEnum {
    /**
     * <code>ENUM_PNC_PAYMENT_METHOD_UNSPECIFIED = 0;</code>
     */
    ENUM_PNC_PAYMENT_METHOD_UNSPECIFIED(0),
    /**
     * <pre>
     * payment method selected as Plug N Charge or Automatic
     * </pre>
     *
     * <code>ENUM_PNC_PAYMENT_METHOD_AUTOMATIC = 1;</code>
     */
    ENUM_PNC_PAYMENT_METHOD_AUTOMATIC(1),
    /**
     * <pre>
     * payment method selected as External Identification Means or Manual
     * </pre>
     *
     * <code>ENUM_PNC_PAYMENT_METHOD_MANUAL = 2;</code>
     */
    ENUM_PNC_PAYMENT_METHOD_MANUAL(2),
    UNRECOGNIZED(-1),
    ;

    static {
      com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
        com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
        /* major= */ 4,
        /* minor= */ 28,
        /* patch= */ 2,
        /* suffix= */ "",
        EnumPnCPaymentMethod.class.getName());
    }
    /**
     * <code>ENUM_PNC_PAYMENT_METHOD_UNSPECIFIED = 0;</code>
     */
    public static final int ENUM_PNC_PAYMENT_METHOD_UNSPECIFIED_VALUE = 0;
    /**
     * <pre>
     * payment method selected as Plug N Charge or Automatic
     * </pre>
     *
     * <code>ENUM_PNC_PAYMENT_METHOD_AUTOMATIC = 1;</code>
     */
    public static final int ENUM_PNC_PAYMENT_METHOD_AUTOMATIC_VALUE = 1;
    /**
     * <pre>
     * payment method selected as External Identification Means or Manual
     * </pre>
     *
     * <code>ENUM_PNC_PAYMENT_METHOD_MANUAL = 2;</code>
     */
    public static final int ENUM_PNC_PAYMENT_METHOD_MANUAL_VALUE = 2;


    public final int getNumber() {
      if (this == UNRECOGNIZED) {
        throw new java.lang.IllegalArgumentException(
            "Can't get the number of an unknown enum value.");
      }
      return value;
    }

    /**
     * @param value The numeric wire value of the corresponding enum entry.
     * @return The enum associated with the given numeric wire value.
     * @deprecated Use {@link #forNumber(int)} instead.
     */
    @java.lang.Deprecated
    public static EnumPnCPaymentMethod valueOf(int value) {
      return forNumber(value);
    }

    /**
     * @param value The numeric wire value of the corresponding enum entry.
     * @return The enum associated with the given numeric wire value.
     */
    public static EnumPnCPaymentMethod forNumber(int value) {
      switch (value) {
        case 0: return ENUM_PNC_PAYMENT_METHOD_UNSPECIFIED;
        case 1: return ENUM_PNC_PAYMENT_METHOD_AUTOMATIC;
        case 2: return ENUM_PNC_PAYMENT_METHOD_MANUAL;
        default: return null;
      }
    }

    public static com.google.protobuf.Internal.EnumLiteMap<EnumPnCPaymentMethod>
        internalGetValueMap() {
      return internalValueMap;
    }
    private static final com.google.protobuf.Internal.EnumLiteMap<
        EnumPnCPaymentMethod> internalValueMap =
          new com.google.protobuf.Internal.EnumLiteMap<EnumPnCPaymentMethod>() {
            public EnumPnCPaymentMethod findValueByNumber(int number) {
              return EnumPnCPaymentMethod.forNumber(number);
            }
          };

    public final com.google.protobuf.Descriptors.EnumValueDescriptor
        getValueDescriptor() {
      if (this == UNRECOGNIZED) {
        throw new java.lang.IllegalStateException(
            "Can't get the descriptor of an unrecognized enum value.");
      }
      return getDescriptor().getValues().get(ordinal());
    }
    public final com.google.protobuf.Descriptors.EnumDescriptor
        getDescriptorForType() {
      return getDescriptor();
    }
    public static final com.google.protobuf.Descriptors.EnumDescriptor
        getDescriptor() {
      return charging_common.ChargingCommon.getDescriptor().getEnumTypes().get(12);
    }

    private static final EnumPnCPaymentMethod[] VALUES = values();

    public static EnumPnCPaymentMethod valueOf(
        com.google.protobuf.Descriptors.EnumValueDescriptor desc) {
      if (desc.getType() != getDescriptor()) {
        throw new java.lang.IllegalArgumentException(
          "EnumValueDescriptor is not for this type.");
      }
      if (desc.getIndex() == -1) {
        return UNRECOGNIZED;
      }
      return VALUES[desc.getIndex()];
    }

    private final int value;

    private EnumPnCPaymentMethod(int value) {
      this.value = value;
    }

    // @@protoc_insertion_point(enum_scope:charging_common.EnumPnCPaymentMethod)
  }

  /**
   * <pre>
   * This is used to indicate the PnC feature status on the vehicle
   * </pre>
   *
   * Protobuf enum {@code charging_common.EnumPnCStatus}
   */
  public enum EnumPnCStatus
      implements com.google.protobuf.ProtocolMessageEnum {
    /**
     * <code>ENUM_PNC_STATUS_UNSPECIFIED = 0;</code>
     */
    ENUM_PNC_STATUS_UNSPECIFIED(0),
    /**
     * <pre>
     * Plug N Charge feature is active
     * </pre>
     *
     * <code>ENUM_PNC_STATUS_ACTIVE = 1;</code>
     */
    ENUM_PNC_STATUS_ACTIVE(1),
    /**
     * <pre>
     * Plug N Charge feature is inactive
     * </pre>
     *
     * <code>ENUM_PNC_STATUS_INACTIVE = 2;</code>
     */
    ENUM_PNC_STATUS_INACTIVE(2),
    /**
     * <pre>
     * Plug N Charge feature has an error
     * </pre>
     *
     * <code>ENUM_PNC_STATUS_ERROR = 3;</code>
     */
    ENUM_PNC_STATUS_ERROR(3),
    UNRECOGNIZED(-1),
    ;

    static {
      com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
        com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
        /* major= */ 4,
        /* minor= */ 28,
        /* patch= */ 2,
        /* suffix= */ "",
        EnumPnCStatus.class.getName());
    }
    /**
     * <code>ENUM_PNC_STATUS_UNSPECIFIED = 0;</code>
     */
    public static final int ENUM_PNC_STATUS_UNSPECIFIED_VALUE = 0;
    /**
     * <pre>
     * Plug N Charge feature is active
     * </pre>
     *
     * <code>ENUM_PNC_STATUS_ACTIVE = 1;</code>
     */
    public static final int ENUM_PNC_STATUS_ACTIVE_VALUE = 1;
    /**
     * <pre>
     * Plug N Charge feature is inactive
     * </pre>
     *
     * <code>ENUM_PNC_STATUS_INACTIVE = 2;</code>
     */
    public static final int ENUM_PNC_STATUS_INACTIVE_VALUE = 2;
    /**
     * <pre>
     * Plug N Charge feature has an error
     * </pre>
     *
     * <code>ENUM_PNC_STATUS_ERROR = 3;</code>
     */
    public static final int ENUM_PNC_STATUS_ERROR_VALUE = 3;


    public final int getNumber() {
      if (this == UNRECOGNIZED) {
        throw new java.lang.IllegalArgumentException(
            "Can't get the number of an unknown enum value.");
      }
      return value;
    }

    /**
     * @param value The numeric wire value of the corresponding enum entry.
     * @return The enum associated with the given numeric wire value.
     * @deprecated Use {@link #forNumber(int)} instead.
     */
    @java.lang.Deprecated
    public static EnumPnCStatus valueOf(int value) {
      return forNumber(value);
    }

    /**
     * @param value The numeric wire value of the corresponding enum entry.
     * @return The enum associated with the given numeric wire value.
     */
    public static EnumPnCStatus forNumber(int value) {
      switch (value) {
        case 0: return ENUM_PNC_STATUS_UNSPECIFIED;
        case 1: return ENUM_PNC_STATUS_ACTIVE;
        case 2: return ENUM_PNC_STATUS_INACTIVE;
        case 3: return ENUM_PNC_STATUS_ERROR;
        default: return null;
      }
    }

    public static com.google.protobuf.Internal.EnumLiteMap<EnumPnCStatus>
        internalGetValueMap() {
      return internalValueMap;
    }
    private static final com.google.protobuf.Internal.EnumLiteMap<
        EnumPnCStatus> internalValueMap =
          new com.google.protobuf.Internal.EnumLiteMap<EnumPnCStatus>() {
            public EnumPnCStatus findValueByNumber(int number) {
              return EnumPnCStatus.forNumber(number);
            }
          };

    public final com.google.protobuf.Descriptors.EnumValueDescriptor
        getValueDescriptor() {
      if (this == UNRECOGNIZED) {
        throw new java.lang.IllegalStateException(
            "Can't get the descriptor of an unrecognized enum value.");
      }
      return getDescriptor().getValues().get(ordinal());
    }
    public final com.google.protobuf.Descriptors.EnumDescriptor
        getDescriptorForType() {
      return getDescriptor();
    }
    public static final com.google.protobuf.Descriptors.EnumDescriptor
        getDescriptor() {
      return charging_common.ChargingCommon.getDescriptor().getEnumTypes().get(13);
    }

    private static final EnumPnCStatus[] VALUES = values();

    public static EnumPnCStatus valueOf(
        com.google.protobuf.Descriptors.EnumValueDescriptor desc) {
      if (desc.getType() != getDescriptor()) {
        throw new java.lang.IllegalArgumentException(
          "EnumValueDescriptor is not for this type.");
      }
      if (desc.getIndex() == -1) {
        return UNRECOGNIZED;
      }
      return VALUES[desc.getIndex()];
    }

    private final int value;

    private EnumPnCStatus(int value) {
      this.value = value;
    }

    // @@protoc_insertion_point(enum_scope:charging_common.EnumPnCStatus)
  }

  /**
   * <pre>
   * This is used to indicate PnC feature is enabled or disabled
   * Availability is not CCF driven but determined dynamically
   * based on what user has selected as well as whether all
   * conditions are met for the feature to be available
   * </pre>
   *
   * Protobuf enum {@code charging_common.EnumPnCFeatureAvailability}
   */
  public enum EnumPnCFeatureAvailability
      implements com.google.protobuf.ProtocolMessageEnum {
    /**
     * <code>ENUM_PNC_FEATURE_UNSPECIFIED = 0;</code>
     */
    ENUM_PNC_FEATURE_UNSPECIFIED(0),
    /**
     * <pre>
     * Plug N Charge feature is not supported
     * </pre>
     *
     * <code>ENUM_PNC_FEATURE_NOT_SUPPORTED = 1;</code>
     */
    ENUM_PNC_FEATURE_NOT_SUPPORTED(1),
    /**
     * <pre>
     * Plug N Charge feature is disabled
     * </pre>
     *
     * <code>ENUM_PNC_FEATURE_DISABLED = 2;</code>
     */
    ENUM_PNC_FEATURE_DISABLED(2),
    /**
     * <pre>
     * Plug N Charge feature is enabled
     * </pre>
     *
     * <code>ENUM_PNC_FEATURE_ENABLED = 3;</code>
     */
    ENUM_PNC_FEATURE_ENABLED(3),
    UNRECOGNIZED(-1),
    ;

    static {
      com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
        com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
        /* major= */ 4,
        /* minor= */ 28,
        /* patch= */ 2,
        /* suffix= */ "",
        EnumPnCFeatureAvailability.class.getName());
    }
    /**
     * <code>ENUM_PNC_FEATURE_UNSPECIFIED = 0;</code>
     */
    public static final int ENUM_PNC_FEATURE_UNSPECIFIED_VALUE = 0;
    /**
     * <pre>
     * Plug N Charge feature is not supported
     * </pre>
     *
     * <code>ENUM_PNC_FEATURE_NOT_SUPPORTED = 1;</code>
     */
    public static final int ENUM_PNC_FEATURE_NOT_SUPPORTED_VALUE = 1;
    /**
     * <pre>
     * Plug N Charge feature is disabled
     * </pre>
     *
     * <code>ENUM_PNC_FEATURE_DISABLED = 2;</code>
     */
    public static final int ENUM_PNC_FEATURE_DISABLED_VALUE = 2;
    /**
     * <pre>
     * Plug N Charge feature is enabled
     * </pre>
     *
     * <code>ENUM_PNC_FEATURE_ENABLED = 3;</code>
     */
    public static final int ENUM_PNC_FEATURE_ENABLED_VALUE = 3;


    public final int getNumber() {
      if (this == UNRECOGNIZED) {
        throw new java.lang.IllegalArgumentException(
            "Can't get the number of an unknown enum value.");
      }
      return value;
    }

    /**
     * @param value The numeric wire value of the corresponding enum entry.
     * @return The enum associated with the given numeric wire value.
     * @deprecated Use {@link #forNumber(int)} instead.
     */
    @java.lang.Deprecated
    public static EnumPnCFeatureAvailability valueOf(int value) {
      return forNumber(value);
    }

    /**
     * @param value The numeric wire value of the corresponding enum entry.
     * @return The enum associated with the given numeric wire value.
     */
    public static EnumPnCFeatureAvailability forNumber(int value) {
      switch (value) {
        case 0: return ENUM_PNC_FEATURE_UNSPECIFIED;
        case 1: return ENUM_PNC_FEATURE_NOT_SUPPORTED;
        case 2: return ENUM_PNC_FEATURE_DISABLED;
        case 3: return ENUM_PNC_FEATURE_ENABLED;
        default: return null;
      }
    }

    public static com.google.protobuf.Internal.EnumLiteMap<EnumPnCFeatureAvailability>
        internalGetValueMap() {
      return internalValueMap;
    }
    private static final com.google.protobuf.Internal.EnumLiteMap<
        EnumPnCFeatureAvailability> internalValueMap =
          new com.google.protobuf.Internal.EnumLiteMap<EnumPnCFeatureAvailability>() {
            public EnumPnCFeatureAvailability findValueByNumber(int number) {
              return EnumPnCFeatureAvailability.forNumber(number);
            }
          };

    public final com.google.protobuf.Descriptors.EnumValueDescriptor
        getValueDescriptor() {
      if (this == UNRECOGNIZED) {
        throw new java.lang.IllegalStateException(
            "Can't get the descriptor of an unrecognized enum value.");
      }
      return getDescriptor().getValues().get(ordinal());
    }
    public final com.google.protobuf.Descriptors.EnumDescriptor
        getDescriptorForType() {
      return getDescriptor();
    }
    public static final com.google.protobuf.Descriptors.EnumDescriptor
        getDescriptor() {
      return charging_common.ChargingCommon.getDescriptor().getEnumTypes().get(14);
    }

    private static final EnumPnCFeatureAvailability[] VALUES = values();

    public static EnumPnCFeatureAvailability valueOf(
        com.google.protobuf.Descriptors.EnumValueDescriptor desc) {
      if (desc.getType() != getDescriptor()) {
        throw new java.lang.IllegalArgumentException(
          "EnumValueDescriptor is not for this type.");
      }
      if (desc.getIndex() == -1) {
        return UNRECOGNIZED;
      }
      return VALUES[desc.getIndex()];
    }

    private final int value;

    private EnumPnCFeatureAvailability(int value) {
      this.value = value;
    }

    // @@protoc_insertion_point(enum_scope:charging_common.EnumPnCFeatureAvailability)
  }

  /**
   * <pre>
   * This is used to request or indicate Vehicle Identification Number(VIN) for China specific region
   * </pre>
   *
   * Protobuf enum {@code charging_common.EnumPnCVINShare}
   */
  public enum EnumPnCVINShare
      implements com.google.protobuf.ProtocolMessageEnum {
    /**
     * <code>ENUM_PNC_VIN_SHARE_UNSPECIFIED = 0;</code>
     */
    ENUM_PNC_VIN_SHARE_UNSPECIFIED(0),
    /**
     * <pre>
     * PnC VIN share is disabled
     * </pre>
     *
     * <code>ENUM_PNC_VIN_SHARE_DISABLED = 1;</code>
     */
    ENUM_PNC_VIN_SHARE_DISABLED(1),
    /**
     * <pre>
     * PnC VIN share is enabled
     * </pre>
     *
     * <code>ENUM_PNC_VIN_SHARE_ENABLED = 2;</code>
     */
    ENUM_PNC_VIN_SHARE_ENABLED(2),
    UNRECOGNIZED(-1),
    ;

    static {
      com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
        com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
        /* major= */ 4,
        /* minor= */ 28,
        /* patch= */ 2,
        /* suffix= */ "",
        EnumPnCVINShare.class.getName());
    }
    /**
     * <code>ENUM_PNC_VIN_SHARE_UNSPECIFIED = 0;</code>
     */
    public static final int ENUM_PNC_VIN_SHARE_UNSPECIFIED_VALUE = 0;
    /**
     * <pre>
     * PnC VIN share is disabled
     * </pre>
     *
     * <code>ENUM_PNC_VIN_SHARE_DISABLED = 1;</code>
     */
    public static final int ENUM_PNC_VIN_SHARE_DISABLED_VALUE = 1;
    /**
     * <pre>
     * PnC VIN share is enabled
     * </pre>
     *
     * <code>ENUM_PNC_VIN_SHARE_ENABLED = 2;</code>
     */
    public static final int ENUM_PNC_VIN_SHARE_ENABLED_VALUE = 2;


    public final int getNumber() {
      if (this == UNRECOGNIZED) {
        throw new java.lang.IllegalArgumentException(
            "Can't get the number of an unknown enum value.");
      }
      return value;
    }

    /**
     * @param value The numeric wire value of the corresponding enum entry.
     * @return The enum associated with the given numeric wire value.
     * @deprecated Use {@link #forNumber(int)} instead.
     */
    @java.lang.Deprecated
    public static EnumPnCVINShare valueOf(int value) {
      return forNumber(value);
    }

    /**
     * @param value The numeric wire value of the corresponding enum entry.
     * @return The enum associated with the given numeric wire value.
     */
    public static EnumPnCVINShare forNumber(int value) {
      switch (value) {
        case 0: return ENUM_PNC_VIN_SHARE_UNSPECIFIED;
        case 1: return ENUM_PNC_VIN_SHARE_DISABLED;
        case 2: return ENUM_PNC_VIN_SHARE_ENABLED;
        default: return null;
      }
    }

    public static com.google.protobuf.Internal.EnumLiteMap<EnumPnCVINShare>
        internalGetValueMap() {
      return internalValueMap;
    }
    private static final com.google.protobuf.Internal.EnumLiteMap<
        EnumPnCVINShare> internalValueMap =
          new com.google.protobuf.Internal.EnumLiteMap<EnumPnCVINShare>() {
            public EnumPnCVINShare findValueByNumber(int number) {
              return EnumPnCVINShare.forNumber(number);
            }
          };

    public final com.google.protobuf.Descriptors.EnumValueDescriptor
        getValueDescriptor() {
      if (this == UNRECOGNIZED) {
        throw new java.lang.IllegalStateException(
            "Can't get the descriptor of an unrecognized enum value.");
      }
      return getDescriptor().getValues().get(ordinal());
    }
    public final com.google.protobuf.Descriptors.EnumDescriptor
        getDescriptorForType() {
      return getDescriptor();
    }
    public static final com.google.protobuf.Descriptors.EnumDescriptor
        getDescriptor() {
      return charging_common.ChargingCommon.getDescriptor().getEnumTypes().get(15);
    }

    private static final EnumPnCVINShare[] VALUES = values();

    public static EnumPnCVINShare valueOf(
        com.google.protobuf.Descriptors.EnumValueDescriptor desc) {
      if (desc.getType() != getDescriptor()) {
        throw new java.lang.IllegalArgumentException(
          "EnumValueDescriptor is not for this type.");
      }
      if (desc.getIndex() == -1) {
        return UNRECOGNIZED;
      }
      return VALUES[desc.getIndex()];
    }

    private final int value;

    private EnumPnCVINShare(int value) {
      this.value = value;
    }

    // @@protoc_insertion_point(enum_scope:charging_common.EnumPnCVINShare)
  }

  /**
   * <pre>
   * This is used to request or indicate the Vehicle to Grid (V2G) Root certificate status
   * </pre>
   *
   * Protobuf enum {@code charging_common.EnumPnCV2GRootCertificateStatus}
   */
  public enum EnumPnCV2GRootCertificateStatus
      implements com.google.protobuf.ProtocolMessageEnum {
    /**
     * <code>ENUM_PNC_V2G_ROOT_CERTIFICATE_UNSPECIFIED = 0;</code>
     */
    ENUM_PNC_V2G_ROOT_CERTIFICATE_UNSPECIFIED(0),
    /**
     * <pre>
     * V2G root certificate corrupted
     * </pre>
     *
     * <code>ENUM_PNC_V2G_ROOT_CERTIFICATE_CORRUPTED = 1;</code>
     */
    ENUM_PNC_V2G_ROOT_CERTIFICATE_CORRUPTED(1),
    /**
     * <pre>
     * V2G root certificate not installed
     * </pre>
     *
     * <code>ENUM_PNC_V2G_ROOT_CERTIFICATE_NOT_INSTALLED = 2;</code>
     */
    ENUM_PNC_V2G_ROOT_CERTIFICATE_NOT_INSTALLED(2),
    /**
     * <pre>
     * V2G root certificate expired
     * </pre>
     *
     * <code>ENUM_PNC_V2G_ROOT_CERTIFICATE_EXPIRED = 3;</code>
     */
    ENUM_PNC_V2G_ROOT_CERTIFICATE_EXPIRED(3),
    /**
     * <pre>
     * V2G root certificate missing
     * </pre>
     *
     * <code>ENUM_PNC_V2G_ROOT_CERTIFICATE_MISSING = 4;</code>
     */
    ENUM_PNC_V2G_ROOT_CERTIFICATE_MISSING(4),
    /**
     * <pre>
     * V2G root certificate expiring soon
     * </pre>
     *
     * <code>ENUM_PNC_V2G_ROOT_CERTIFICATE_EXPIRING_SOON = 5;</code>
     */
    ENUM_PNC_V2G_ROOT_CERTIFICATE_EXPIRING_SOON(5),
    /**
     * <pre>
     * V2G root certificate corrupted
     * </pre>
     *
     * <code>ENUM_PNC_V2G_ROOT_CERTIFICATE_INSTALLED = 6;</code>
     */
    ENUM_PNC_V2G_ROOT_CERTIFICATE_INSTALLED(6),
    UNRECOGNIZED(-1),
    ;

    static {
      com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
        com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
        /* major= */ 4,
        /* minor= */ 28,
        /* patch= */ 2,
        /* suffix= */ "",
        EnumPnCV2GRootCertificateStatus.class.getName());
    }
    /**
     * <code>ENUM_PNC_V2G_ROOT_CERTIFICATE_UNSPECIFIED = 0;</code>
     */
    public static final int ENUM_PNC_V2G_ROOT_CERTIFICATE_UNSPECIFIED_VALUE = 0;
    /**
     * <pre>
     * V2G root certificate corrupted
     * </pre>
     *
     * <code>ENUM_PNC_V2G_ROOT_CERTIFICATE_CORRUPTED = 1;</code>
     */
    public static final int ENUM_PNC_V2G_ROOT_CERTIFICATE_CORRUPTED_VALUE = 1;
    /**
     * <pre>
     * V2G root certificate not installed
     * </pre>
     *
     * <code>ENUM_PNC_V2G_ROOT_CERTIFICATE_NOT_INSTALLED = 2;</code>
     */
    public static final int ENUM_PNC_V2G_ROOT_CERTIFICATE_NOT_INSTALLED_VALUE = 2;
    /**
     * <pre>
     * V2G root certificate expired
     * </pre>
     *
     * <code>ENUM_PNC_V2G_ROOT_CERTIFICATE_EXPIRED = 3;</code>
     */
    public static final int ENUM_PNC_V2G_ROOT_CERTIFICATE_EXPIRED_VALUE = 3;
    /**
     * <pre>
     * V2G root certificate missing
     * </pre>
     *
     * <code>ENUM_PNC_V2G_ROOT_CERTIFICATE_MISSING = 4;</code>
     */
    public static final int ENUM_PNC_V2G_ROOT_CERTIFICATE_MISSING_VALUE = 4;
    /**
     * <pre>
     * V2G root certificate expiring soon
     * </pre>
     *
     * <code>ENUM_PNC_V2G_ROOT_CERTIFICATE_EXPIRING_SOON = 5;</code>
     */
    public static final int ENUM_PNC_V2G_ROOT_CERTIFICATE_EXPIRING_SOON_VALUE = 5;
    /**
     * <pre>
     * V2G root certificate corrupted
     * </pre>
     *
     * <code>ENUM_PNC_V2G_ROOT_CERTIFICATE_INSTALLED = 6;</code>
     */
    public static final int ENUM_PNC_V2G_ROOT_CERTIFICATE_INSTALLED_VALUE = 6;


    public final int getNumber() {
      if (this == UNRECOGNIZED) {
        throw new java.lang.IllegalArgumentException(
            "Can't get the number of an unknown enum value.");
      }
      return value;
    }

    /**
     * @param value The numeric wire value of the corresponding enum entry.
     * @return The enum associated with the given numeric wire value.
     * @deprecated Use {@link #forNumber(int)} instead.
     */
    @java.lang.Deprecated
    public static EnumPnCV2GRootCertificateStatus valueOf(int value) {
      return forNumber(value);
    }

    /**
     * @param value The numeric wire value of the corresponding enum entry.
     * @return The enum associated with the given numeric wire value.
     */
    public static EnumPnCV2GRootCertificateStatus forNumber(int value) {
      switch (value) {
        case 0: return ENUM_PNC_V2G_ROOT_CERTIFICATE_UNSPECIFIED;
        case 1: return ENUM_PNC_V2G_ROOT_CERTIFICATE_CORRUPTED;
        case 2: return ENUM_PNC_V2G_ROOT_CERTIFICATE_NOT_INSTALLED;
        case 3: return ENUM_PNC_V2G_ROOT_CERTIFICATE_EXPIRED;
        case 4: return ENUM_PNC_V2G_ROOT_CERTIFICATE_MISSING;
        case 5: return ENUM_PNC_V2G_ROOT_CERTIFICATE_EXPIRING_SOON;
        case 6: return ENUM_PNC_V2G_ROOT_CERTIFICATE_INSTALLED;
        default: return null;
      }
    }

    public static com.google.protobuf.Internal.EnumLiteMap<EnumPnCV2GRootCertificateStatus>
        internalGetValueMap() {
      return internalValueMap;
    }
    private static final com.google.protobuf.Internal.EnumLiteMap<
        EnumPnCV2GRootCertificateStatus> internalValueMap =
          new com.google.protobuf.Internal.EnumLiteMap<EnumPnCV2GRootCertificateStatus>() {
            public EnumPnCV2GRootCertificateStatus findValueByNumber(int number) {
              return EnumPnCV2GRootCertificateStatus.forNumber(number);
            }
          };

    public final com.google.protobuf.Descriptors.EnumValueDescriptor
        getValueDescriptor() {
      if (this == UNRECOGNIZED) {
        throw new java.lang.IllegalStateException(
            "Can't get the descriptor of an unrecognized enum value.");
      }
      return getDescriptor().getValues().get(ordinal());
    }
    public final com.google.protobuf.Descriptors.EnumDescriptor
        getDescriptorForType() {
      return getDescriptor();
    }
    public static final com.google.protobuf.Descriptors.EnumDescriptor
        getDescriptor() {
      return charging_common.ChargingCommon.getDescriptor().getEnumTypes().get(16);
    }

    private static final EnumPnCV2GRootCertificateStatus[] VALUES = values();

    public static EnumPnCV2GRootCertificateStatus valueOf(
        com.google.protobuf.Descriptors.EnumValueDescriptor desc) {
      if (desc.getType() != getDescriptor()) {
        throw new java.lang.IllegalArgumentException(
          "EnumValueDescriptor is not for this type.");
      }
      if (desc.getIndex() == -1) {
        return UNRECOGNIZED;
      }
      return VALUES[desc.getIndex()];
    }

    private final int value;

    private EnumPnCV2GRootCertificateStatus(int value) {
      this.value = value;
    }

    // @@protoc_insertion_point(enum_scope:charging_common.EnumPnCV2GRootCertificateStatus)
  }

  /**
   * <pre>
   * This is used to indicate the Charge troubleshooter messages and plug and charge troubleshooter messages
   * </pre>
   *
   * Protobuf enum {@code charging_common.EnumChrgTroubleSht}
   */
  public enum EnumChrgTroubleSht
      implements com.google.protobuf.ProtocolMessageEnum {
    /**
     * <code>ENUM_CHRG_TROUBLESHT_UNSPECIFIED = 0;</code>
     */
    ENUM_CHRG_TROUBLESHT_UNSPECIFIED(0),
    /**
     * <code>ENUM_CHRG_TROUBLESHT_CLEAR_MESSAGE = 1;</code>
     */
    ENUM_CHRG_TROUBLESHT_CLEAR_MESSAGE(1),
    /**
     * <code>ENUM_CHRG_TROUBLESHT_PWR_SOURCE_FAULT = 2;</code>
     */
    ENUM_CHRG_TROUBLESHT_PWR_SOURCE_FAULT(2),
    /**
     * <code>ENUM_CHRG_TROUBLESHT_UNSUITABLE_PWR_SOURCE = 3;</code>
     */
    ENUM_CHRG_TROUBLESHT_UNSUITABLE_PWR_SOURCE(3),
    /**
     * <code>ENUM_CHRG_TROUBLESHT_OVERCURRENT_DETECTED = 4;</code>
     */
    ENUM_CHRG_TROUBLESHT_OVERCURRENT_DETECTED(4),
    /**
     * <code>ENUM_CHRG_TROUBLESHT_UNABLE_TO_LOCK_PIN_DUAL = 5;</code>
     */
    ENUM_CHRG_TROUBLESHT_UNABLE_TO_LOCK_PIN_DUAL(5),
    /**
     * <code>ENUM_CHRG_TROUBLESHT_UNABLE_TO_LOCK_PIN_SINGLE = 6;</code>
     */
    ENUM_CHRG_TROUBLESHT_UNABLE_TO_LOCK_PIN_SINGLE(6),
    /**
     * <code>ENUM_CHRG_TROUBLESHT_UNABLE_TO_UNLOCK_PIN = 7;</code>
     */
    ENUM_CHRG_TROUBLESHT_UNABLE_TO_UNLOCK_PIN(7),
    /**
     * <code>ENUM_CHRG_TROUBLESHT_FLAP_STUCK_OPEN = 8;</code>
     */
    ENUM_CHRG_TROUBLESHT_FLAP_STUCK_OPEN(8),
    /**
     * <code>ENUM_CHRG_TROUBLESHT_FLAP_STUCK_OPEN_DRIVING = 9;</code>
     */
    ENUM_CHRG_TROUBLESHT_FLAP_STUCK_OPEN_DRIVING(9),
    /**
     * <code>ENUM_CHRG_TROUBLESHT_FLAP_STUCK_CLOSED = 10;</code>
     */
    ENUM_CHRG_TROUBLESHT_FLAP_STUCK_CLOSED(10),
    /**
     * <code>ENUM_CHRG_TROUBLESHT_BOTH_INLETS_USED = 11;</code>
     */
    ENUM_CHRG_TROUBLESHT_BOTH_INLETS_USED(11),
    /**
     * <code>ENUM_CHRG_TROUBLESHT_NO_AC_CHARGING = 12;</code>
     */
    ENUM_CHRG_TROUBLESHT_NO_AC_CHARGING(12),
    /**
     * <code>ENUM_CHRG_TROUBLESHT_NO_DC_CHARGING = 13;</code>
     */
    ENUM_CHRG_TROUBLESHT_NO_DC_CHARGING(13),
    /**
     * <code>ENUM_CHRG_TROUBLESHT_ALL_CHARGING_PREVENTED = 14;</code>
     */
    ENUM_CHRG_TROUBLESHT_ALL_CHARGING_PREVENTED(14),
    /**
     * <code>ENUM_CHRG_TROUBLESHT_CHARGING_INLET_ISSUE = 15;</code>
     */
    ENUM_CHRG_TROUBLESHT_CHARGING_INLET_ISSUE(15),
    /**
     * <code>ENUM_CHRG_TROUBLESHT_DC_800V = 16;</code>
     */
    ENUM_CHRG_TROUBLESHT_DC_800V(16),
    /**
     * <code>ENUM_CHRG_TROUBLESHT_PWR_SOURCE_DECREASE_CHARG_RATE = 17;</code>
     */
    ENUM_CHRG_TROUBLESHT_PWR_SOURCE_DECREASE_CHARG_RATE(17),
    /**
     * <code>ENUM_CHRG_TROUBLESHT_LOW_BATT_TEMP = 18;</code>
     */
    ENUM_CHRG_TROUBLESHT_LOW_BATT_TEMP(18),
    /**
     * <code>ENUM_CHRG_TROUBLESHT_CRITICAL_LOW_BATT_TEMP = 19;</code>
     */
    ENUM_CHRG_TROUBLESHT_CRITICAL_LOW_BATT_TEMP(19),
    /**
     * <code>ENUM_CHRG_TROUBLESHT_HIGH_BATT_TEMP = 20;</code>
     */
    ENUM_CHRG_TROUBLESHT_HIGH_BATT_TEMP(20),
    /**
     * <code>ENUM_CHRG_TROUBLESHT_USER_SELECT_AC_LIMIT = 21;</code>
     */
    ENUM_CHRG_TROUBLESHT_USER_SELECT_AC_LIMIT(21),
    /**
     * <code>ENUM_CHRG_TROUBLESHT_SLOW_AC_CHARGING = 22;</code>
     */
    ENUM_CHRG_TROUBLESHT_SLOW_AC_CHARGING(22),
    /**
     * <code>ENUM_CHRG_TROUBLESHT_NO_CONTRACT_CERT = 23;</code>
     */
    ENUM_CHRG_TROUBLESHT_NO_CONTRACT_CERT(23),
    /**
     * <code>ENUM_CHRG_TROUBLESHT_CONTRACT_CERT_EXP_CANCEL = 24;</code>
     */
    ENUM_CHRG_TROUBLESHT_CONTRACT_CERT_EXP_CANCEL(24),
    /**
     * <code>ENUM_CHRG_TROUBLESHT_EVSE_CERT_ERROR = 27;</code>
     */
    ENUM_CHRG_TROUBLESHT_EVSE_CERT_ERROR(27),
    /**
     * <code>ENUM_CHRG_TROUBLESHT_GENERIC_PNC_V2G_ERROR = 28;</code>
     */
    ENUM_CHRG_TROUBLESHT_GENERIC_PNC_V2G_ERROR(28),
    UNRECOGNIZED(-1),
    ;

    static {
      com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
        com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
        /* major= */ 4,
        /* minor= */ 28,
        /* patch= */ 2,
        /* suffix= */ "",
        EnumChrgTroubleSht.class.getName());
    }
    /**
     * <code>ENUM_CHRG_TROUBLESHT_UNSPECIFIED = 0;</code>
     */
    public static final int ENUM_CHRG_TROUBLESHT_UNSPECIFIED_VALUE = 0;
    /**
     * <code>ENUM_CHRG_TROUBLESHT_CLEAR_MESSAGE = 1;</code>
     */
    public static final int ENUM_CHRG_TROUBLESHT_CLEAR_MESSAGE_VALUE = 1;
    /**
     * <code>ENUM_CHRG_TROUBLESHT_PWR_SOURCE_FAULT = 2;</code>
     */
    public static final int ENUM_CHRG_TROUBLESHT_PWR_SOURCE_FAULT_VALUE = 2;
    /**
     * <code>ENUM_CHRG_TROUBLESHT_UNSUITABLE_PWR_SOURCE = 3;</code>
     */
    public static final int ENUM_CHRG_TROUBLESHT_UNSUITABLE_PWR_SOURCE_VALUE = 3;
    /**
     * <code>ENUM_CHRG_TROUBLESHT_OVERCURRENT_DETECTED = 4;</code>
     */
    public static final int ENUM_CHRG_TROUBLESHT_OVERCURRENT_DETECTED_VALUE = 4;
    /**
     * <code>ENUM_CHRG_TROUBLESHT_UNABLE_TO_LOCK_PIN_DUAL = 5;</code>
     */
    public static final int ENUM_CHRG_TROUBLESHT_UNABLE_TO_LOCK_PIN_DUAL_VALUE = 5;
    /**
     * <code>ENUM_CHRG_TROUBLESHT_UNABLE_TO_LOCK_PIN_SINGLE = 6;</code>
     */
    public static final int ENUM_CHRG_TROUBLESHT_UNABLE_TO_LOCK_PIN_SINGLE_VALUE = 6;
    /**
     * <code>ENUM_CHRG_TROUBLESHT_UNABLE_TO_UNLOCK_PIN = 7;</code>
     */
    public static final int ENUM_CHRG_TROUBLESHT_UNABLE_TO_UNLOCK_PIN_VALUE = 7;
    /**
     * <code>ENUM_CHRG_TROUBLESHT_FLAP_STUCK_OPEN = 8;</code>
     */
    public static final int ENUM_CHRG_TROUBLESHT_FLAP_STUCK_OPEN_VALUE = 8;
    /**
     * <code>ENUM_CHRG_TROUBLESHT_FLAP_STUCK_OPEN_DRIVING = 9;</code>
     */
    public static final int ENUM_CHRG_TROUBLESHT_FLAP_STUCK_OPEN_DRIVING_VALUE = 9;
    /**
     * <code>ENUM_CHRG_TROUBLESHT_FLAP_STUCK_CLOSED = 10;</code>
     */
    public static final int ENUM_CHRG_TROUBLESHT_FLAP_STUCK_CLOSED_VALUE = 10;
    /**
     * <code>ENUM_CHRG_TROUBLESHT_BOTH_INLETS_USED = 11;</code>
     */
    public static final int ENUM_CHRG_TROUBLESHT_BOTH_INLETS_USED_VALUE = 11;
    /**
     * <code>ENUM_CHRG_TROUBLESHT_NO_AC_CHARGING = 12;</code>
     */
    public static final int ENUM_CHRG_TROUBLESHT_NO_AC_CHARGING_VALUE = 12;
    /**
     * <code>ENUM_CHRG_TROUBLESHT_NO_DC_CHARGING = 13;</code>
     */
    public static final int ENUM_CHRG_TROUBLESHT_NO_DC_CHARGING_VALUE = 13;
    /**
     * <code>ENUM_CHRG_TROUBLESHT_ALL_CHARGING_PREVENTED = 14;</code>
     */
    public static final int ENUM_CHRG_TROUBLESHT_ALL_CHARGING_PREVENTED_VALUE = 14;
    /**
     * <code>ENUM_CHRG_TROUBLESHT_CHARGING_INLET_ISSUE = 15;</code>
     */
    public static final int ENUM_CHRG_TROUBLESHT_CHARGING_INLET_ISSUE_VALUE = 15;
    /**
     * <code>ENUM_CHRG_TROUBLESHT_DC_800V = 16;</code>
     */
    public static final int ENUM_CHRG_TROUBLESHT_DC_800V_VALUE = 16;
    /**
     * <code>ENUM_CHRG_TROUBLESHT_PWR_SOURCE_DECREASE_CHARG_RATE = 17;</code>
     */
    public static final int ENUM_CHRG_TROUBLESHT_PWR_SOURCE_DECREASE_CHARG_RATE_VALUE = 17;
    /**
     * <code>ENUM_CHRG_TROUBLESHT_LOW_BATT_TEMP = 18;</code>
     */
    public static final int ENUM_CHRG_TROUBLESHT_LOW_BATT_TEMP_VALUE = 18;
    /**
     * <code>ENUM_CHRG_TROUBLESHT_CRITICAL_LOW_BATT_TEMP = 19;</code>
     */
    public static final int ENUM_CHRG_TROUBLESHT_CRITICAL_LOW_BATT_TEMP_VALUE = 19;
    /**
     * <code>ENUM_CHRG_TROUBLESHT_HIGH_BATT_TEMP = 20;</code>
     */
    public static final int ENUM_CHRG_TROUBLESHT_HIGH_BATT_TEMP_VALUE = 20;
    /**
     * <code>ENUM_CHRG_TROUBLESHT_USER_SELECT_AC_LIMIT = 21;</code>
     */
    public static final int ENUM_CHRG_TROUBLESHT_USER_SELECT_AC_LIMIT_VALUE = 21;
    /**
     * <code>ENUM_CHRG_TROUBLESHT_SLOW_AC_CHARGING = 22;</code>
     */
    public static final int ENUM_CHRG_TROUBLESHT_SLOW_AC_CHARGING_VALUE = 22;
    /**
     * <code>ENUM_CHRG_TROUBLESHT_NO_CONTRACT_CERT = 23;</code>
     */
    public static final int ENUM_CHRG_TROUBLESHT_NO_CONTRACT_CERT_VALUE = 23;
    /**
     * <code>ENUM_CHRG_TROUBLESHT_CONTRACT_CERT_EXP_CANCEL = 24;</code>
     */
    public static final int ENUM_CHRG_TROUBLESHT_CONTRACT_CERT_EXP_CANCEL_VALUE = 24;
    /**
     * <code>ENUM_CHRG_TROUBLESHT_EVSE_CERT_ERROR = 27;</code>
     */
    public static final int ENUM_CHRG_TROUBLESHT_EVSE_CERT_ERROR_VALUE = 27;
    /**
     * <code>ENUM_CHRG_TROUBLESHT_GENERIC_PNC_V2G_ERROR = 28;</code>
     */
    public static final int ENUM_CHRG_TROUBLESHT_GENERIC_PNC_V2G_ERROR_VALUE = 28;


    public final int getNumber() {
      if (this == UNRECOGNIZED) {
        throw new java.lang.IllegalArgumentException(
            "Can't get the number of an unknown enum value.");
      }
      return value;
    }

    /**
     * @param value The numeric wire value of the corresponding enum entry.
     * @return The enum associated with the given numeric wire value.
     * @deprecated Use {@link #forNumber(int)} instead.
     */
    @java.lang.Deprecated
    public static EnumChrgTroubleSht valueOf(int value) {
      return forNumber(value);
    }

    /**
     * @param value The numeric wire value of the corresponding enum entry.
     * @return The enum associated with the given numeric wire value.
     */
    public static EnumChrgTroubleSht forNumber(int value) {
      switch (value) {
        case 0: return ENUM_CHRG_TROUBLESHT_UNSPECIFIED;
        case 1: return ENUM_CHRG_TROUBLESHT_CLEAR_MESSAGE;
        case 2: return ENUM_CHRG_TROUBLESHT_PWR_SOURCE_FAULT;
        case 3: return ENUM_CHRG_TROUBLESHT_UNSUITABLE_PWR_SOURCE;
        case 4: return ENUM_CHRG_TROUBLESHT_OVERCURRENT_DETECTED;
        case 5: return ENUM_CHRG_TROUBLESHT_UNABLE_TO_LOCK_PIN_DUAL;
        case 6: return ENUM_CHRG_TROUBLESHT_UNABLE_TO_LOCK_PIN_SINGLE;
        case 7: return ENUM_CHRG_TROUBLESHT_UNABLE_TO_UNLOCK_PIN;
        case 8: return ENUM_CHRG_TROUBLESHT_FLAP_STUCK_OPEN;
        case 9: return ENUM_CHRG_TROUBLESHT_FLAP_STUCK_OPEN_DRIVING;
        case 10: return ENUM_CHRG_TROUBLESHT_FLAP_STUCK_CLOSED;
        case 11: return ENUM_CHRG_TROUBLESHT_BOTH_INLETS_USED;
        case 12: return ENUM_CHRG_TROUBLESHT_NO_AC_CHARGING;
        case 13: return ENUM_CHRG_TROUBLESHT_NO_DC_CHARGING;
        case 14: return ENUM_CHRG_TROUBLESHT_ALL_CHARGING_PREVENTED;
        case 15: return ENUM_CHRG_TROUBLESHT_CHARGING_INLET_ISSUE;
        case 16: return ENUM_CHRG_TROUBLESHT_DC_800V;
        case 17: return ENUM_CHRG_TROUBLESHT_PWR_SOURCE_DECREASE_CHARG_RATE;
        case 18: return ENUM_CHRG_TROUBLESHT_LOW_BATT_TEMP;
        case 19: return ENUM_CHRG_TROUBLESHT_CRITICAL_LOW_BATT_TEMP;
        case 20: return ENUM_CHRG_TROUBLESHT_HIGH_BATT_TEMP;
        case 21: return ENUM_CHRG_TROUBLESHT_USER_SELECT_AC_LIMIT;
        case 22: return ENUM_CHRG_TROUBLESHT_SLOW_AC_CHARGING;
        case 23: return ENUM_CHRG_TROUBLESHT_NO_CONTRACT_CERT;
        case 24: return ENUM_CHRG_TROUBLESHT_CONTRACT_CERT_EXP_CANCEL;
        case 27: return ENUM_CHRG_TROUBLESHT_EVSE_CERT_ERROR;
        case 28: return ENUM_CHRG_TROUBLESHT_GENERIC_PNC_V2G_ERROR;
        default: return null;
      }
    }

    public static com.google.protobuf.Internal.EnumLiteMap<EnumChrgTroubleSht>
        internalGetValueMap() {
      return internalValueMap;
    }
    private static final com.google.protobuf.Internal.EnumLiteMap<
        EnumChrgTroubleSht> internalValueMap =
          new com.google.protobuf.Internal.EnumLiteMap<EnumChrgTroubleSht>() {
            public EnumChrgTroubleSht findValueByNumber(int number) {
              return EnumChrgTroubleSht.forNumber(number);
            }
          };

    public final com.google.protobuf.Descriptors.EnumValueDescriptor
        getValueDescriptor() {
      if (this == UNRECOGNIZED) {
        throw new java.lang.IllegalStateException(
            "Can't get the descriptor of an unrecognized enum value.");
      }
      return getDescriptor().getValues().get(ordinal());
    }
    public final com.google.protobuf.Descriptors.EnumDescriptor
        getDescriptorForType() {
      return getDescriptor();
    }
    public static final com.google.protobuf.Descriptors.EnumDescriptor
        getDescriptor() {
      return charging_common.ChargingCommon.getDescriptor().getEnumTypes().get(17);
    }

    private static final EnumChrgTroubleSht[] VALUES = values();

    public static EnumChrgTroubleSht valueOf(
        com.google.protobuf.Descriptors.EnumValueDescriptor desc) {
      if (desc.getType() != getDescriptor()) {
        throw new java.lang.IllegalArgumentException(
          "EnumValueDescriptor is not for this type.");
      }
      if (desc.getIndex() == -1) {
        return UNRECOGNIZED;
      }
      return VALUES[desc.getIndex()];
    }

    private final int value;

    private EnumChrgTroubleSht(int value) {
      this.value = value;
    }

    // @@protoc_insertion_point(enum_scope:charging_common.EnumChrgTroubleSht)
  }

  /**
   * <pre>
   * This is used to request or indicate the contract certificate request is
   * install, update or delete
   * </pre>
   *
   * Protobuf enum {@code charging_common.EnumPnCContractCertOperation}
   */
  public enum EnumPnCContractCertOperation
      implements com.google.protobuf.ProtocolMessageEnum {
    /**
     * <code>ENUM_PNC_CONTRACT_CERT_OPERATION_UNSPECIFIED = 0;</code>
     */
    ENUM_PNC_CONTRACT_CERT_OPERATION_UNSPECIFIED(0),
    /**
     * <pre>
     * Install Contract certificate for PnC
     * </pre>
     *
     * <code>ENUM_PNC_CONTRACT_CERT_OPERATION_INSTALL = 1;</code>
     */
    ENUM_PNC_CONTRACT_CERT_OPERATION_INSTALL(1),
    /**
     * <pre>
     * Update Contract certificate for PnC
     * </pre>
     *
     * <code>ENUM_PNC_CONTRACT_CERT_OPERATION_UPDATE = 2;</code>
     */
    ENUM_PNC_CONTRACT_CERT_OPERATION_UPDATE(2),
    /**
     * <pre>
     * Delete Contract certificate for PnC
     * </pre>
     *
     * <code>ENUM_PNC_CONTRACT_CERT_OPERATION_DELETE = 3;</code>
     */
    ENUM_PNC_CONTRACT_CERT_OPERATION_DELETE(3),
    UNRECOGNIZED(-1),
    ;

    static {
      com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
        com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
        /* major= */ 4,
        /* minor= */ 28,
        /* patch= */ 2,
        /* suffix= */ "",
        EnumPnCContractCertOperation.class.getName());
    }
    /**
     * <code>ENUM_PNC_CONTRACT_CERT_OPERATION_UNSPECIFIED = 0;</code>
     */
    public static final int ENUM_PNC_CONTRACT_CERT_OPERATION_UNSPECIFIED_VALUE = 0;
    /**
     * <pre>
     * Install Contract certificate for PnC
     * </pre>
     *
     * <code>ENUM_PNC_CONTRACT_CERT_OPERATION_INSTALL = 1;</code>
     */
    public static final int ENUM_PNC_CONTRACT_CERT_OPERATION_INSTALL_VALUE = 1;
    /**
     * <pre>
     * Update Contract certificate for PnC
     * </pre>
     *
     * <code>ENUM_PNC_CONTRACT_CERT_OPERATION_UPDATE = 2;</code>
     */
    public static final int ENUM_PNC_CONTRACT_CERT_OPERATION_UPDATE_VALUE = 2;
    /**
     * <pre>
     * Delete Contract certificate for PnC
     * </pre>
     *
     * <code>ENUM_PNC_CONTRACT_CERT_OPERATION_DELETE = 3;</code>
     */
    public static final int ENUM_PNC_CONTRACT_CERT_OPERATION_DELETE_VALUE = 3;


    public final int getNumber() {
      if (this == UNRECOGNIZED) {
        throw new java.lang.IllegalArgumentException(
            "Can't get the number of an unknown enum value.");
      }
      return value;
    }

    /**
     * @param value The numeric wire value of the corresponding enum entry.
     * @return The enum associated with the given numeric wire value.
     * @deprecated Use {@link #forNumber(int)} instead.
     */
    @java.lang.Deprecated
    public static EnumPnCContractCertOperation valueOf(int value) {
      return forNumber(value);
    }

    /**
     * @param value The numeric wire value of the corresponding enum entry.
     * @return The enum associated with the given numeric wire value.
     */
    public static EnumPnCContractCertOperation forNumber(int value) {
      switch (value) {
        case 0: return ENUM_PNC_CONTRACT_CERT_OPERATION_UNSPECIFIED;
        case 1: return ENUM_PNC_CONTRACT_CERT_OPERATION_INSTALL;
        case 2: return ENUM_PNC_CONTRACT_CERT_OPERATION_UPDATE;
        case 3: return ENUM_PNC_CONTRACT_CERT_OPERATION_DELETE;
        default: return null;
      }
    }

    public static com.google.protobuf.Internal.EnumLiteMap<EnumPnCContractCertOperation>
        internalGetValueMap() {
      return internalValueMap;
    }
    private static final com.google.protobuf.Internal.EnumLiteMap<
        EnumPnCContractCertOperation> internalValueMap =
          new com.google.protobuf.Internal.EnumLiteMap<EnumPnCContractCertOperation>() {
            public EnumPnCContractCertOperation findValueByNumber(int number) {
              return EnumPnCContractCertOperation.forNumber(number);
            }
          };

    public final com.google.protobuf.Descriptors.EnumValueDescriptor
        getValueDescriptor() {
      if (this == UNRECOGNIZED) {
        throw new java.lang.IllegalStateException(
            "Can't get the descriptor of an unrecognized enum value.");
      }
      return getDescriptor().getValues().get(ordinal());
    }
    public final com.google.protobuf.Descriptors.EnumDescriptor
        getDescriptorForType() {
      return getDescriptor();
    }
    public static final com.google.protobuf.Descriptors.EnumDescriptor
        getDescriptor() {
      return charging_common.ChargingCommon.getDescriptor().getEnumTypes().get(18);
    }

    private static final EnumPnCContractCertOperation[] VALUES = values();

    public static EnumPnCContractCertOperation valueOf(
        com.google.protobuf.Descriptors.EnumValueDescriptor desc) {
      if (desc.getType() != getDescriptor()) {
        throw new java.lang.IllegalArgumentException(
          "EnumValueDescriptor is not for this type.");
      }
      if (desc.getIndex() == -1) {
        return UNRECOGNIZED;
      }
      return VALUES[desc.getIndex()];
    }

    private final int value;

    private EnumPnCContractCertOperation(int value) {
      this.value = value;
    }

    // @@protoc_insertion_point(enum_scope:charging_common.EnumPnCContractCertOperation)
  }

  /**
   * <pre>
   * This is used to indicate the contract certificate status
   * </pre>
   *
   * Protobuf enum {@code charging_common.EnumPnCContractStatus}
   */
  public enum EnumPnCContractStatus
      implements com.google.protobuf.ProtocolMessageEnum {
    /**
     * <code>ENUM_PNC_CONTRACT_STATUS_UNSPECIFIED = 0;</code>
     */
    ENUM_PNC_CONTRACT_STATUS_UNSPECIFIED(0),
    /**
     * <pre>
     * No Contract certificate is installed in vehicle for PnC
     * </pre>
     *
     * <code>ENUM_PNC_CONTRACT_NO_CERTIFICATE_INSTALLED = 1;</code>
     */
    ENUM_PNC_CONTRACT_NO_CERTIFICATE_INSTALLED(1),
    /**
     * <pre>
     * Valid Contract certificate available for PnC
     * </pre>
     *
     * <code>ENUM_PNC_CONTRACT_CERTIFICATE_VALID = 2;</code>
     */
    ENUM_PNC_CONTRACT_CERTIFICATE_VALID(2),
    /**
     * <pre>
     * Contract Certificate expired
     * </pre>
     *
     * <code>ENUM_PNC_CONTRACT_CERTIFICATE_EXPIRED = 3;</code>
     */
    ENUM_PNC_CONTRACT_CERTIFICATE_EXPIRED(3),
    /**
     * <pre>
     * Contract Certificate is expiring soon
     * </pre>
     *
     * <code>ENUM_PNC_CONTRACT_CERTIFICATE_EXPIRING_SOON = 4;</code>
     */
    ENUM_PNC_CONTRACT_CERTIFICATE_EXPIRING_SOON(4),
    /**
     * <pre>
     * Processing the last request
     * </pre>
     *
     * <code>ENUM_PNC_CONTRACT_REQUEST_PROCESSING = 5;</code>
     */
    ENUM_PNC_CONTRACT_REQUEST_PROCESSING(5),
    /**
     * <pre>
     * Contract Certificate validation failed
     * </pre>
     *
     * <code>ENUM_PNC_CONTRACT_CERTIFICATE_INVALID = 6;</code>
     */
    ENUM_PNC_CONTRACT_CERTIFICATE_INVALID(6),
    /**
     * <pre>
     * Contract Certificate corrupted during certificate installation process
     * </pre>
     *
     * <code>ENUM_PNC_CERTIFICATE_CORRUPTED_WHILE_INSTALL = 7;</code>
     */
    ENUM_PNC_CERTIFICATE_CORRUPTED_WHILE_INSTALL(7),
    /**
     * <pre>
     * Contract Certificate corrupted during certificate deletion process
     * </pre>
     *
     * <code>ENUM_PNC_CERTIFICATE_CORRUPTED_WHILE_DELETE = 8;</code>
     */
    ENUM_PNC_CERTIFICATE_CORRUPTED_WHILE_DELETE(8),
    /**
     * <pre>
     * Failed to process the last request
     * </pre>
     *
     * <code>ENUM_PNC_CONTRACT_REQUEST_PROCESSING_FAILED = 9;</code>
     */
    ENUM_PNC_CONTRACT_REQUEST_PROCESSING_FAILED(9),
    /**
     * <pre>
     * Successfully processed the last request
     * </pre>
     *
     * <code>ENUM_PNC_CONTRACT_REQUEST_PROCESSED_OK = 10;</code>
     */
    ENUM_PNC_CONTRACT_REQUEST_PROCESSED_OK(10),
    UNRECOGNIZED(-1),
    ;

    static {
      com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
        com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
        /* major= */ 4,
        /* minor= */ 28,
        /* patch= */ 2,
        /* suffix= */ "",
        EnumPnCContractStatus.class.getName());
    }
    /**
     * <code>ENUM_PNC_CONTRACT_STATUS_UNSPECIFIED = 0;</code>
     */
    public static final int ENUM_PNC_CONTRACT_STATUS_UNSPECIFIED_VALUE = 0;
    /**
     * <pre>
     * No Contract certificate is installed in vehicle for PnC
     * </pre>
     *
     * <code>ENUM_PNC_CONTRACT_NO_CERTIFICATE_INSTALLED = 1;</code>
     */
    public static final int ENUM_PNC_CONTRACT_NO_CERTIFICATE_INSTALLED_VALUE = 1;
    /**
     * <pre>
     * Valid Contract certificate available for PnC
     * </pre>
     *
     * <code>ENUM_PNC_CONTRACT_CERTIFICATE_VALID = 2;</code>
     */
    public static final int ENUM_PNC_CONTRACT_CERTIFICATE_VALID_VALUE = 2;
    /**
     * <pre>
     * Contract Certificate expired
     * </pre>
     *
     * <code>ENUM_PNC_CONTRACT_CERTIFICATE_EXPIRED = 3;</code>
     */
    public static final int ENUM_PNC_CONTRACT_CERTIFICATE_EXPIRED_VALUE = 3;
    /**
     * <pre>
     * Contract Certificate is expiring soon
     * </pre>
     *
     * <code>ENUM_PNC_CONTRACT_CERTIFICATE_EXPIRING_SOON = 4;</code>
     */
    public static final int ENUM_PNC_CONTRACT_CERTIFICATE_EXPIRING_SOON_VALUE = 4;
    /**
     * <pre>
     * Processing the last request
     * </pre>
     *
     * <code>ENUM_PNC_CONTRACT_REQUEST_PROCESSING = 5;</code>
     */
    public static final int ENUM_PNC_CONTRACT_REQUEST_PROCESSING_VALUE = 5;
    /**
     * <pre>
     * Contract Certificate validation failed
     * </pre>
     *
     * <code>ENUM_PNC_CONTRACT_CERTIFICATE_INVALID = 6;</code>
     */
    public static final int ENUM_PNC_CONTRACT_CERTIFICATE_INVALID_VALUE = 6;
    /**
     * <pre>
     * Contract Certificate corrupted during certificate installation process
     * </pre>
     *
     * <code>ENUM_PNC_CERTIFICATE_CORRUPTED_WHILE_INSTALL = 7;</code>
     */
    public static final int ENUM_PNC_CERTIFICATE_CORRUPTED_WHILE_INSTALL_VALUE = 7;
    /**
     * <pre>
     * Contract Certificate corrupted during certificate deletion process
     * </pre>
     *
     * <code>ENUM_PNC_CERTIFICATE_CORRUPTED_WHILE_DELETE = 8;</code>
     */
    public static final int ENUM_PNC_CERTIFICATE_CORRUPTED_WHILE_DELETE_VALUE = 8;
    /**
     * <pre>
     * Failed to process the last request
     * </pre>
     *
     * <code>ENUM_PNC_CONTRACT_REQUEST_PROCESSING_FAILED = 9;</code>
     */
    public static final int ENUM_PNC_CONTRACT_REQUEST_PROCESSING_FAILED_VALUE = 9;
    /**
     * <pre>
     * Successfully processed the last request
     * </pre>
     *
     * <code>ENUM_PNC_CONTRACT_REQUEST_PROCESSED_OK = 10;</code>
     */
    public static final int ENUM_PNC_CONTRACT_REQUEST_PROCESSED_OK_VALUE = 10;


    public final int getNumber() {
      if (this == UNRECOGNIZED) {
        throw new java.lang.IllegalArgumentException(
            "Can't get the number of an unknown enum value.");
      }
      return value;
    }

    /**
     * @param value The numeric wire value of the corresponding enum entry.
     * @return The enum associated with the given numeric wire value.
     * @deprecated Use {@link #forNumber(int)} instead.
     */
    @java.lang.Deprecated
    public static EnumPnCContractStatus valueOf(int value) {
      return forNumber(value);
    }

    /**
     * @param value The numeric wire value of the corresponding enum entry.
     * @return The enum associated with the given numeric wire value.
     */
    public static EnumPnCContractStatus forNumber(int value) {
      switch (value) {
        case 0: return ENUM_PNC_CONTRACT_STATUS_UNSPECIFIED;
        case 1: return ENUM_PNC_CONTRACT_NO_CERTIFICATE_INSTALLED;
        case 2: return ENUM_PNC_CONTRACT_CERTIFICATE_VALID;
        case 3: return ENUM_PNC_CONTRACT_CERTIFICATE_EXPIRED;
        case 4: return ENUM_PNC_CONTRACT_CERTIFICATE_EXPIRING_SOON;
        case 5: return ENUM_PNC_CONTRACT_REQUEST_PROCESSING;
        case 6: return ENUM_PNC_CONTRACT_CERTIFICATE_INVALID;
        case 7: return ENUM_PNC_CERTIFICATE_CORRUPTED_WHILE_INSTALL;
        case 8: return ENUM_PNC_CERTIFICATE_CORRUPTED_WHILE_DELETE;
        case 9: return ENUM_PNC_CONTRACT_REQUEST_PROCESSING_FAILED;
        case 10: return ENUM_PNC_CONTRACT_REQUEST_PROCESSED_OK;
        default: return null;
      }
    }

    public static com.google.protobuf.Internal.EnumLiteMap<EnumPnCContractStatus>
        internalGetValueMap() {
      return internalValueMap;
    }
    private static final com.google.protobuf.Internal.EnumLiteMap<
        EnumPnCContractStatus> internalValueMap =
          new com.google.protobuf.Internal.EnumLiteMap<EnumPnCContractStatus>() {
            public EnumPnCContractStatus findValueByNumber(int number) {
              return EnumPnCContractStatus.forNumber(number);
            }
          };

    public final com.google.protobuf.Descriptors.EnumValueDescriptor
        getValueDescriptor() {
      if (this == UNRECOGNIZED) {
        throw new java.lang.IllegalStateException(
            "Can't get the descriptor of an unrecognized enum value.");
      }
      return getDescriptor().getValues().get(ordinal());
    }
    public final com.google.protobuf.Descriptors.EnumDescriptor
        getDescriptorForType() {
      return getDescriptor();
    }
    public static final com.google.protobuf.Descriptors.EnumDescriptor
        getDescriptor() {
      return charging_common.ChargingCommon.getDescriptor().getEnumTypes().get(19);
    }

    private static final EnumPnCContractStatus[] VALUES = values();

    public static EnumPnCContractStatus valueOf(
        com.google.protobuf.Descriptors.EnumValueDescriptor desc) {
      if (desc.getType() != getDescriptor()) {
        throw new java.lang.IllegalArgumentException(
          "EnumValueDescriptor is not for this type.");
      }
      if (desc.getIndex() == -1) {
        return UNRECOGNIZED;
      }
      return VALUES[desc.getIndex()];
    }

    private final int value;

    private EnumPnCContractStatus(int value) {
      this.value = value;
    }

    // @@protoc_insertion_point(enum_scope:charging_common.EnumPnCContractStatus)
  }

  /**
   * <pre>
   * This is used to indicate the state of Vehicle 2 load discharge state
   * HVPO stands for high voltage power outlet
   * </pre>
   *
   * Protobuf enum {@code charging_common.EnumHVPOStatus}
   */
  public enum EnumHVPOStatus
      implements com.google.protobuf.ProtocolMessageEnum {
    /**
     * <code>ENUM_HVPO_STATUS_UNSPECIFIED = 0;</code>
     */
    ENUM_HVPO_STATUS_UNSPECIFIED(0),
    /**
     * <pre>
     * This state shall indicate that no discharging is requested
     * </pre>
     *
     * <code>ENUM_HVPO_STATUS_NO_DISCHARGE_REQUEST = 1;</code>
     */
    ENUM_HVPO_STATUS_NO_DISCHARGE_REQUEST(1),
    /**
     * <pre>
     * This state shall indicate that discharge is in progress
     * </pre>
     *
     * <code>ENUM_HVPO_STATUS_DISCHARGE_IN_PROGRESS = 2;</code>
     */
    ENUM_HVPO_STATUS_DISCHARGE_IN_PROGRESS(2),
    /**
     * <pre>
     * This state shall indicate that there is a SOC limit on the discharge, once the state of charge is reached
     * once the state of charge is reached discharge will stop
     * </pre>
     *
     * <code>ENUM_HVPO_STATUS_DISCHARGE_SOC_LIMIT_REACHED = 3;</code>
     */
    ENUM_HVPO_STATUS_DISCHARGE_SOC_LIMIT_REACHED(3),
    /**
     * <pre>
     * This state shall indicate that discharging is not available e.g. due to AC charging or Non native Dc charging
     * </pre>
     *
     * <code>ENUM_HVPO_STATUS_DISCHARGE_UNAVAILABLE = 4;</code>
     */
    ENUM_HVPO_STATUS_DISCHARGE_UNAVAILABLE(4),
    /**
     * <pre>
     * This state shall indicate that there is in an error in the HVPO discharge system
     * </pre>
     *
     * <code>ENUM_HVPO_STATUS_DISCHARGE_ERROR = 5;</code>
     */
    ENUM_HVPO_STATUS_DISCHARGE_ERROR(5),
    UNRECOGNIZED(-1),
    ;

    static {
      com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
        com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
        /* major= */ 4,
        /* minor= */ 28,
        /* patch= */ 2,
        /* suffix= */ "",
        EnumHVPOStatus.class.getName());
    }
    /**
     * <code>ENUM_HVPO_STATUS_UNSPECIFIED = 0;</code>
     */
    public static final int ENUM_HVPO_STATUS_UNSPECIFIED_VALUE = 0;
    /**
     * <pre>
     * This state shall indicate that no discharging is requested
     * </pre>
     *
     * <code>ENUM_HVPO_STATUS_NO_DISCHARGE_REQUEST = 1;</code>
     */
    public static final int ENUM_HVPO_STATUS_NO_DISCHARGE_REQUEST_VALUE = 1;
    /**
     * <pre>
     * This state shall indicate that discharge is in progress
     * </pre>
     *
     * <code>ENUM_HVPO_STATUS_DISCHARGE_IN_PROGRESS = 2;</code>
     */
    public static final int ENUM_HVPO_STATUS_DISCHARGE_IN_PROGRESS_VALUE = 2;
    /**
     * <pre>
     * This state shall indicate that there is a SOC limit on the discharge, once the state of charge is reached
     * once the state of charge is reached discharge will stop
     * </pre>
     *
     * <code>ENUM_HVPO_STATUS_DISCHARGE_SOC_LIMIT_REACHED = 3;</code>
     */
    public static final int ENUM_HVPO_STATUS_DISCHARGE_SOC_LIMIT_REACHED_VALUE = 3;
    /**
     * <pre>
     * This state shall indicate that discharging is not available e.g. due to AC charging or Non native Dc charging
     * </pre>
     *
     * <code>ENUM_HVPO_STATUS_DISCHARGE_UNAVAILABLE = 4;</code>
     */
    public static final int ENUM_HVPO_STATUS_DISCHARGE_UNAVAILABLE_VALUE = 4;
    /**
     * <pre>
     * This state shall indicate that there is in an error in the HVPO discharge system
     * </pre>
     *
     * <code>ENUM_HVPO_STATUS_DISCHARGE_ERROR = 5;</code>
     */
    public static final int ENUM_HVPO_STATUS_DISCHARGE_ERROR_VALUE = 5;


    public final int getNumber() {
      if (this == UNRECOGNIZED) {
        throw new java.lang.IllegalArgumentException(
            "Can't get the number of an unknown enum value.");
      }
      return value;
    }

    /**
     * @param value The numeric wire value of the corresponding enum entry.
     * @return The enum associated with the given numeric wire value.
     * @deprecated Use {@link #forNumber(int)} instead.
     */
    @java.lang.Deprecated
    public static EnumHVPOStatus valueOf(int value) {
      return forNumber(value);
    }

    /**
     * @param value The numeric wire value of the corresponding enum entry.
     * @return The enum associated with the given numeric wire value.
     */
    public static EnumHVPOStatus forNumber(int value) {
      switch (value) {
        case 0: return ENUM_HVPO_STATUS_UNSPECIFIED;
        case 1: return ENUM_HVPO_STATUS_NO_DISCHARGE_REQUEST;
        case 2: return ENUM_HVPO_STATUS_DISCHARGE_IN_PROGRESS;
        case 3: return ENUM_HVPO_STATUS_DISCHARGE_SOC_LIMIT_REACHED;
        case 4: return ENUM_HVPO_STATUS_DISCHARGE_UNAVAILABLE;
        case 5: return ENUM_HVPO_STATUS_DISCHARGE_ERROR;
        default: return null;
      }
    }

    public static com.google.protobuf.Internal.EnumLiteMap<EnumHVPOStatus>
        internalGetValueMap() {
      return internalValueMap;
    }
    private static final com.google.protobuf.Internal.EnumLiteMap<
        EnumHVPOStatus> internalValueMap =
          new com.google.protobuf.Internal.EnumLiteMap<EnumHVPOStatus>() {
            public EnumHVPOStatus findValueByNumber(int number) {
              return EnumHVPOStatus.forNumber(number);
            }
          };

    public final com.google.protobuf.Descriptors.EnumValueDescriptor
        getValueDescriptor() {
      if (this == UNRECOGNIZED) {
        throw new java.lang.IllegalStateException(
            "Can't get the descriptor of an unrecognized enum value.");
      }
      return getDescriptor().getValues().get(ordinal());
    }
    public final com.google.protobuf.Descriptors.EnumDescriptor
        getDescriptorForType() {
      return getDescriptor();
    }
    public static final com.google.protobuf.Descriptors.EnumDescriptor
        getDescriptor() {
      return charging_common.ChargingCommon.getDescriptor().getEnumTypes().get(20);
    }

    private static final EnumHVPOStatus[] VALUES = values();

    public static EnumHVPOStatus valueOf(
        com.google.protobuf.Descriptors.EnumValueDescriptor desc) {
      if (desc.getType() != getDescriptor()) {
        throw new java.lang.IllegalArgumentException(
          "EnumValueDescriptor is not for this type.");
      }
      if (desc.getIndex() == -1) {
        return UNRECOGNIZED;
      }
      return VALUES[desc.getIndex()];
    }

    private final int value;

    private EnumHVPOStatus(int value) {
      this.value = value;
    }

    // @@protoc_insertion_point(enum_scope:charging_common.EnumHVPOStatus)
  }

  /**
   * <pre>
   * This is used to indicate the setting of Auto unlock of the charging cable (AC charging)
   * </pre>
   *
   * Protobuf enum {@code charging_common.EnumChargeCableAutoUnlock}
   */
  public enum EnumChargeCableAutoUnlock
      implements com.google.protobuf.ProtocolMessageEnum {
    /**
     * <code>ENUM_CHARGE_CABLE_AUTO_UNLOCK_UNSPECIFIED = 0;</code>
     */
    ENUM_CHARGE_CABLE_AUTO_UNLOCK_UNSPECIFIED(0),
    /**
     * <code>ENUM_CHARGE_CABLE_AUTO_UNLOCK_ON = 1;</code>
     */
    ENUM_CHARGE_CABLE_AUTO_UNLOCK_ON(1),
    /**
     * <code>ENUM_CHARGE_CABLE_AUTO_UNLOCK_OFF = 2;</code>
     */
    ENUM_CHARGE_CABLE_AUTO_UNLOCK_OFF(2),
    UNRECOGNIZED(-1),
    ;

    static {
      com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
        com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
        /* major= */ 4,
        /* minor= */ 28,
        /* patch= */ 2,
        /* suffix= */ "",
        EnumChargeCableAutoUnlock.class.getName());
    }
    /**
     * <code>ENUM_CHARGE_CABLE_AUTO_UNLOCK_UNSPECIFIED = 0;</code>
     */
    public static final int ENUM_CHARGE_CABLE_AUTO_UNLOCK_UNSPECIFIED_VALUE = 0;
    /**
     * <code>ENUM_CHARGE_CABLE_AUTO_UNLOCK_ON = 1;</code>
     */
    public static final int ENUM_CHARGE_CABLE_AUTO_UNLOCK_ON_VALUE = 1;
    /**
     * <code>ENUM_CHARGE_CABLE_AUTO_UNLOCK_OFF = 2;</code>
     */
    public static final int ENUM_CHARGE_CABLE_AUTO_UNLOCK_OFF_VALUE = 2;


    public final int getNumber() {
      if (this == UNRECOGNIZED) {
        throw new java.lang.IllegalArgumentException(
            "Can't get the number of an unknown enum value.");
      }
      return value;
    }

    /**
     * @param value The numeric wire value of the corresponding enum entry.
     * @return The enum associated with the given numeric wire value.
     * @deprecated Use {@link #forNumber(int)} instead.
     */
    @java.lang.Deprecated
    public static EnumChargeCableAutoUnlock valueOf(int value) {
      return forNumber(value);
    }

    /**
     * @param value The numeric wire value of the corresponding enum entry.
     * @return The enum associated with the given numeric wire value.
     */
    public static EnumChargeCableAutoUnlock forNumber(int value) {
      switch (value) {
        case 0: return ENUM_CHARGE_CABLE_AUTO_UNLOCK_UNSPECIFIED;
        case 1: return ENUM_CHARGE_CABLE_AUTO_UNLOCK_ON;
        case 2: return ENUM_CHARGE_CABLE_AUTO_UNLOCK_OFF;
        default: return null;
      }
    }

    public static com.google.protobuf.Internal.EnumLiteMap<EnumChargeCableAutoUnlock>
        internalGetValueMap() {
      return internalValueMap;
    }
    private static final com.google.protobuf.Internal.EnumLiteMap<
        EnumChargeCableAutoUnlock> internalValueMap =
          new com.google.protobuf.Internal.EnumLiteMap<EnumChargeCableAutoUnlock>() {
            public EnumChargeCableAutoUnlock findValueByNumber(int number) {
              return EnumChargeCableAutoUnlock.forNumber(number);
            }
          };

    public final com.google.protobuf.Descriptors.EnumValueDescriptor
        getValueDescriptor() {
      if (this == UNRECOGNIZED) {
        throw new java.lang.IllegalStateException(
            "Can't get the descriptor of an unrecognized enum value.");
      }
      return getDescriptor().getValues().get(ordinal());
    }
    public final com.google.protobuf.Descriptors.EnumDescriptor
        getDescriptorForType() {
      return getDescriptor();
    }
    public static final com.google.protobuf.Descriptors.EnumDescriptor
        getDescriptor() {
      return charging_common.ChargingCommon.getDescriptor().getEnumTypes().get(21);
    }

    private static final EnumChargeCableAutoUnlock[] VALUES = values();

    public static EnumChargeCableAutoUnlock valueOf(
        com.google.protobuf.Descriptors.EnumValueDescriptor desc) {
      if (desc.getType() != getDescriptor()) {
        throw new java.lang.IllegalArgumentException(
          "EnumValueDescriptor is not for this type.");
      }
      if (desc.getIndex() == -1) {
        return UNRECOGNIZED;
      }
      return VALUES[desc.getIndex()];
    }

    private final int value;

    private EnumChargeCableAutoUnlock(int value) {
      this.value = value;
    }

    // @@protoc_insertion_point(enum_scope:charging_common.EnumChargeCableAutoUnlock)
  }

  /**
   * <pre>
   * This is used to indicate the setting of Charging cable - Approach unlocking
   * </pre>
   *
   * Protobuf enum {@code charging_common.EnumChargeCableApproachUnlock}
   */
  public enum EnumChargeCableApproachUnlock
      implements com.google.protobuf.ProtocolMessageEnum {
    /**
     * <code>ENUM_CHARGE_CABLE_APPROACH_UNSPECIFIED = 0;</code>
     */
    ENUM_CHARGE_CABLE_APPROACH_UNSPECIFIED(0),
    /**
     * <code>ENUM_CHARGE_CABLE_APPROACH_UNLOCK_ON = 1;</code>
     */
    ENUM_CHARGE_CABLE_APPROACH_UNLOCK_ON(1),
    /**
     * <code>ENUM_CHARGE_CABLE_APPROACH_UNLOCK_OFF = 2;</code>
     */
    ENUM_CHARGE_CABLE_APPROACH_UNLOCK_OFF(2),
    UNRECOGNIZED(-1),
    ;

    static {
      com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
        com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
        /* major= */ 4,
        /* minor= */ 28,
        /* patch= */ 2,
        /* suffix= */ "",
        EnumChargeCableApproachUnlock.class.getName());
    }
    /**
     * <code>ENUM_CHARGE_CABLE_APPROACH_UNSPECIFIED = 0;</code>
     */
    public static final int ENUM_CHARGE_CABLE_APPROACH_UNSPECIFIED_VALUE = 0;
    /**
     * <code>ENUM_CHARGE_CABLE_APPROACH_UNLOCK_ON = 1;</code>
     */
    public static final int ENUM_CHARGE_CABLE_APPROACH_UNLOCK_ON_VALUE = 1;
    /**
     * <code>ENUM_CHARGE_CABLE_APPROACH_UNLOCK_OFF = 2;</code>
     */
    public static final int ENUM_CHARGE_CABLE_APPROACH_UNLOCK_OFF_VALUE = 2;


    public final int getNumber() {
      if (this == UNRECOGNIZED) {
        throw new java.lang.IllegalArgumentException(
            "Can't get the number of an unknown enum value.");
      }
      return value;
    }

    /**
     * @param value The numeric wire value of the corresponding enum entry.
     * @return The enum associated with the given numeric wire value.
     * @deprecated Use {@link #forNumber(int)} instead.
     */
    @java.lang.Deprecated
    public static EnumChargeCableApproachUnlock valueOf(int value) {
      return forNumber(value);
    }

    /**
     * @param value The numeric wire value of the corresponding enum entry.
     * @return The enum associated with the given numeric wire value.
     */
    public static EnumChargeCableApproachUnlock forNumber(int value) {
      switch (value) {
        case 0: return ENUM_CHARGE_CABLE_APPROACH_UNSPECIFIED;
        case 1: return ENUM_CHARGE_CABLE_APPROACH_UNLOCK_ON;
        case 2: return ENUM_CHARGE_CABLE_APPROACH_UNLOCK_OFF;
        default: return null;
      }
    }

    public static com.google.protobuf.Internal.EnumLiteMap<EnumChargeCableApproachUnlock>
        internalGetValueMap() {
      return internalValueMap;
    }
    private static final com.google.protobuf.Internal.EnumLiteMap<
        EnumChargeCableApproachUnlock> internalValueMap =
          new com.google.protobuf.Internal.EnumLiteMap<EnumChargeCableApproachUnlock>() {
            public EnumChargeCableApproachUnlock findValueByNumber(int number) {
              return EnumChargeCableApproachUnlock.forNumber(number);
            }
          };

    public final com.google.protobuf.Descriptors.EnumValueDescriptor
        getValueDescriptor() {
      if (this == UNRECOGNIZED) {
        throw new java.lang.IllegalStateException(
            "Can't get the descriptor of an unrecognized enum value.");
      }
      return getDescriptor().getValues().get(ordinal());
    }
    public final com.google.protobuf.Descriptors.EnumDescriptor
        getDescriptorForType() {
      return getDescriptor();
    }
    public static final com.google.protobuf.Descriptors.EnumDescriptor
        getDescriptor() {
      return charging_common.ChargingCommon.getDescriptor().getEnumTypes().get(22);
    }

    private static final EnumChargeCableApproachUnlock[] VALUES = values();

    public static EnumChargeCableApproachUnlock valueOf(
        com.google.protobuf.Descriptors.EnumValueDescriptor desc) {
      if (desc.getType() != getDescriptor()) {
        throw new java.lang.IllegalArgumentException(
          "EnumValueDescriptor is not for this type.");
      }
      if (desc.getIndex() == -1) {
        return UNRECOGNIZED;
      }
      return VALUES[desc.getIndex()];
    }

    private final int value;

    private EnumChargeCableApproachUnlock(int value) {
      this.value = value;
    }

    // @@protoc_insertion_point(enum_scope:charging_common.EnumChargeCableApproachUnlock)
  }

  /**
   * <pre>
   * This is used to indicate the setting of Charging status - permanent charging light
   * </pre>
   *
   * Protobuf enum {@code charging_common.EnumChargeLightPermanent}
   */
  public enum EnumChargeLightPermanent
      implements com.google.protobuf.ProtocolMessageEnum {
    /**
     * <code>ENUM_CHARGE_LIGHT_PERMANENT_UNSPECIFIED = 0;</code>
     */
    ENUM_CHARGE_LIGHT_PERMANENT_UNSPECIFIED(0),
    /**
     * <code>ENUM_CHARGE_LIGHT_PERMANENT_ON = 1;</code>
     */
    ENUM_CHARGE_LIGHT_PERMANENT_ON(1),
    /**
     * <code>ENUM_CHARGE_LIGHT_PERMANENT_OFF = 2;</code>
     */
    ENUM_CHARGE_LIGHT_PERMANENT_OFF(2),
    UNRECOGNIZED(-1),
    ;

    static {
      com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
        com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
        /* major= */ 4,
        /* minor= */ 28,
        /* patch= */ 2,
        /* suffix= */ "",
        EnumChargeLightPermanent.class.getName());
    }
    /**
     * <code>ENUM_CHARGE_LIGHT_PERMANENT_UNSPECIFIED = 0;</code>
     */
    public static final int ENUM_CHARGE_LIGHT_PERMANENT_UNSPECIFIED_VALUE = 0;
    /**
     * <code>ENUM_CHARGE_LIGHT_PERMANENT_ON = 1;</code>
     */
    public static final int ENUM_CHARGE_LIGHT_PERMANENT_ON_VALUE = 1;
    /**
     * <code>ENUM_CHARGE_LIGHT_PERMANENT_OFF = 2;</code>
     */
    public static final int ENUM_CHARGE_LIGHT_PERMANENT_OFF_VALUE = 2;


    public final int getNumber() {
      if (this == UNRECOGNIZED) {
        throw new java.lang.IllegalArgumentException(
            "Can't get the number of an unknown enum value.");
      }
      return value;
    }

    /**
     * @param value The numeric wire value of the corresponding enum entry.
     * @return The enum associated with the given numeric wire value.
     * @deprecated Use {@link #forNumber(int)} instead.
     */
    @java.lang.Deprecated
    public static EnumChargeLightPermanent valueOf(int value) {
      return forNumber(value);
    }

    /**
     * @param value The numeric wire value of the corresponding enum entry.
     * @return The enum associated with the given numeric wire value.
     */
    public static EnumChargeLightPermanent forNumber(int value) {
      switch (value) {
        case 0: return ENUM_CHARGE_LIGHT_PERMANENT_UNSPECIFIED;
        case 1: return ENUM_CHARGE_LIGHT_PERMANENT_ON;
        case 2: return ENUM_CHARGE_LIGHT_PERMANENT_OFF;
        default: return null;
      }
    }

    public static com.google.protobuf.Internal.EnumLiteMap<EnumChargeLightPermanent>
        internalGetValueMap() {
      return internalValueMap;
    }
    private static final com.google.protobuf.Internal.EnumLiteMap<
        EnumChargeLightPermanent> internalValueMap =
          new com.google.protobuf.Internal.EnumLiteMap<EnumChargeLightPermanent>() {
            public EnumChargeLightPermanent findValueByNumber(int number) {
              return EnumChargeLightPermanent.forNumber(number);
            }
          };

    public final com.google.protobuf.Descriptors.EnumValueDescriptor
        getValueDescriptor() {
      if (this == UNRECOGNIZED) {
        throw new java.lang.IllegalStateException(
            "Can't get the descriptor of an unrecognized enum value.");
      }
      return getDescriptor().getValues().get(ordinal());
    }
    public final com.google.protobuf.Descriptors.EnumDescriptor
        getDescriptorForType() {
      return getDescriptor();
    }
    public static final com.google.protobuf.Descriptors.EnumDescriptor
        getDescriptor() {
      return charging_common.ChargingCommon.getDescriptor().getEnumTypes().get(23);
    }

    private static final EnumChargeLightPermanent[] VALUES = values();

    public static EnumChargeLightPermanent valueOf(
        com.google.protobuf.Descriptors.EnumValueDescriptor desc) {
      if (desc.getType() != getDescriptor()) {
        throw new java.lang.IllegalArgumentException(
          "EnumValueDescriptor is not for this type.");
      }
      if (desc.getIndex() == -1) {
        return UNRECOGNIZED;
      }
      return VALUES[desc.getIndex()];
    }

    private final int value;

    private EnumChargeLightPermanent(int value) {
      this.value = value;
    }

    // @@protoc_insertion_point(enum_scope:charging_common.EnumChargeLightPermanent)
  }

  /**
   * <pre>
   * This is used to indicate the setting of Auto close of the charging door
   * </pre>
   *
   * Protobuf enum {@code charging_common.EnumChargeDoorAutoClose}
   */
  public enum EnumChargeDoorAutoClose
      implements com.google.protobuf.ProtocolMessageEnum {
    /**
     * <code>ENUM_CHARGE_DOOR_AUTO_CLOSE_UNSPECIFIED = 0;</code>
     */
    ENUM_CHARGE_DOOR_AUTO_CLOSE_UNSPECIFIED(0),
    /**
     * <code>ENUM_CHARGE_DOOR_AUTO_CLOSE_ON = 1;</code>
     */
    ENUM_CHARGE_DOOR_AUTO_CLOSE_ON(1),
    /**
     * <code>ENUM_CHARGE_DOOR_AUTO_CLOSE_OFF = 2;</code>
     */
    ENUM_CHARGE_DOOR_AUTO_CLOSE_OFF(2),
    UNRECOGNIZED(-1),
    ;

    static {
      com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
        com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
        /* major= */ 4,
        /* minor= */ 28,
        /* patch= */ 2,
        /* suffix= */ "",
        EnumChargeDoorAutoClose.class.getName());
    }
    /**
     * <code>ENUM_CHARGE_DOOR_AUTO_CLOSE_UNSPECIFIED = 0;</code>
     */
    public static final int ENUM_CHARGE_DOOR_AUTO_CLOSE_UNSPECIFIED_VALUE = 0;
    /**
     * <code>ENUM_CHARGE_DOOR_AUTO_CLOSE_ON = 1;</code>
     */
    public static final int ENUM_CHARGE_DOOR_AUTO_CLOSE_ON_VALUE = 1;
    /**
     * <code>ENUM_CHARGE_DOOR_AUTO_CLOSE_OFF = 2;</code>
     */
    public static final int ENUM_CHARGE_DOOR_AUTO_CLOSE_OFF_VALUE = 2;


    public final int getNumber() {
      if (this == UNRECOGNIZED) {
        throw new java.lang.IllegalArgumentException(
            "Can't get the number of an unknown enum value.");
      }
      return value;
    }

    /**
     * @param value The numeric wire value of the corresponding enum entry.
     * @return The enum associated with the given numeric wire value.
     * @deprecated Use {@link #forNumber(int)} instead.
     */
    @java.lang.Deprecated
    public static EnumChargeDoorAutoClose valueOf(int value) {
      return forNumber(value);
    }

    /**
     * @param value The numeric wire value of the corresponding enum entry.
     * @return The enum associated with the given numeric wire value.
     */
    public static EnumChargeDoorAutoClose forNumber(int value) {
      switch (value) {
        case 0: return ENUM_CHARGE_DOOR_AUTO_CLOSE_UNSPECIFIED;
        case 1: return ENUM_CHARGE_DOOR_AUTO_CLOSE_ON;
        case 2: return ENUM_CHARGE_DOOR_AUTO_CLOSE_OFF;
        default: return null;
      }
    }

    public static com.google.protobuf.Internal.EnumLiteMap<EnumChargeDoorAutoClose>
        internalGetValueMap() {
      return internalValueMap;
    }
    private static final com.google.protobuf.Internal.EnumLiteMap<
        EnumChargeDoorAutoClose> internalValueMap =
          new com.google.protobuf.Internal.EnumLiteMap<EnumChargeDoorAutoClose>() {
            public EnumChargeDoorAutoClose findValueByNumber(int number) {
              return EnumChargeDoorAutoClose.forNumber(number);
            }
          };

    public final com.google.protobuf.Descriptors.EnumValueDescriptor
        getValueDescriptor() {
      if (this == UNRECOGNIZED) {
        throw new java.lang.IllegalStateException(
            "Can't get the descriptor of an unrecognized enum value.");
      }
      return getDescriptor().getValues().get(ordinal());
    }
    public final com.google.protobuf.Descriptors.EnumDescriptor
        getDescriptorForType() {
      return getDescriptor();
    }
    public static final com.google.protobuf.Descriptors.EnumDescriptor
        getDescriptor() {
      return charging_common.ChargingCommon.getDescriptor().getEnumTypes().get(24);
    }

    private static final EnumChargeDoorAutoClose[] VALUES = values();

    public static EnumChargeDoorAutoClose valueOf(
        com.google.protobuf.Descriptors.EnumValueDescriptor desc) {
      if (desc.getType() != getDescriptor()) {
        throw new java.lang.IllegalArgumentException(
          "EnumValueDescriptor is not for this type.");
      }
      if (desc.getIndex() == -1) {
        return UNRECOGNIZED;
      }
      return VALUES[desc.getIndex()];
    }

    private final int value;

    private EnumChargeDoorAutoClose(int value) {
      this.value = value;
    }

    // @@protoc_insertion_point(enum_scope:charging_common.EnumChargeDoorAutoClose)
  }

  /**
   * <pre>
   * This is used to indicate the battery level
   * </pre>
   *
   * Protobuf enum {@code charging_common.EnumBatteryLevel}
   */
  public enum EnumBatteryLevel
      implements com.google.protobuf.ProtocolMessageEnum {
    /**
     * <code>ENUM_BATTERY_LEVEL_UNSPECIFIED = 0;</code>
     */
    ENUM_BATTERY_LEVEL_UNSPECIFIED(0),
    /**
     * <code>ENUM_BATTERY_LEVEL_LOW = 1;</code>
     */
    ENUM_BATTERY_LEVEL_LOW(1),
    /**
     * <code>ENUM_BATTERY_LEVEL_NORMAL = 2;</code>
     */
    ENUM_BATTERY_LEVEL_NORMAL(2),
    UNRECOGNIZED(-1),
    ;

    static {
      com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
        com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
        /* major= */ 4,
        /* minor= */ 28,
        /* patch= */ 2,
        /* suffix= */ "",
        EnumBatteryLevel.class.getName());
    }
    /**
     * <code>ENUM_BATTERY_LEVEL_UNSPECIFIED = 0;</code>
     */
    public static final int ENUM_BATTERY_LEVEL_UNSPECIFIED_VALUE = 0;
    /**
     * <code>ENUM_BATTERY_LEVEL_LOW = 1;</code>
     */
    public static final int ENUM_BATTERY_LEVEL_LOW_VALUE = 1;
    /**
     * <code>ENUM_BATTERY_LEVEL_NORMAL = 2;</code>
     */
    public static final int ENUM_BATTERY_LEVEL_NORMAL_VALUE = 2;


    public final int getNumber() {
      if (this == UNRECOGNIZED) {
        throw new java.lang.IllegalArgumentException(
            "Can't get the number of an unknown enum value.");
      }
      return value;
    }

    /**
     * @param value The numeric wire value of the corresponding enum entry.
     * @return The enum associated with the given numeric wire value.
     * @deprecated Use {@link #forNumber(int)} instead.
     */
    @java.lang.Deprecated
    public static EnumBatteryLevel valueOf(int value) {
      return forNumber(value);
    }

    /**
     * @param value The numeric wire value of the corresponding enum entry.
     * @return The enum associated with the given numeric wire value.
     */
    public static EnumBatteryLevel forNumber(int value) {
      switch (value) {
        case 0: return ENUM_BATTERY_LEVEL_UNSPECIFIED;
        case 1: return ENUM_BATTERY_LEVEL_LOW;
        case 2: return ENUM_BATTERY_LEVEL_NORMAL;
        default: return null;
      }
    }

    public static com.google.protobuf.Internal.EnumLiteMap<EnumBatteryLevel>
        internalGetValueMap() {
      return internalValueMap;
    }
    private static final com.google.protobuf.Internal.EnumLiteMap<
        EnumBatteryLevel> internalValueMap =
          new com.google.protobuf.Internal.EnumLiteMap<EnumBatteryLevel>() {
            public EnumBatteryLevel findValueByNumber(int number) {
              return EnumBatteryLevel.forNumber(number);
            }
          };

    public final com.google.protobuf.Descriptors.EnumValueDescriptor
        getValueDescriptor() {
      if (this == UNRECOGNIZED) {
        throw new java.lang.IllegalStateException(
            "Can't get the descriptor of an unrecognized enum value.");
      }
      return getDescriptor().getValues().get(ordinal());
    }
    public final com.google.protobuf.Descriptors.EnumDescriptor
        getDescriptorForType() {
      return getDescriptor();
    }
    public static final com.google.protobuf.Descriptors.EnumDescriptor
        getDescriptor() {
      return charging_common.ChargingCommon.getDescriptor().getEnumTypes().get(25);
    }

    private static final EnumBatteryLevel[] VALUES = values();

    public static EnumBatteryLevel valueOf(
        com.google.protobuf.Descriptors.EnumValueDescriptor desc) {
      if (desc.getType() != getDescriptor()) {
        throw new java.lang.IllegalArgumentException(
          "EnumValueDescriptor is not for this type.");
      }
      if (desc.getIndex() == -1) {
        return UNRECOGNIZED;
      }
      return VALUES[desc.getIndex()];
    }

    private final int value;

    private EnumBatteryLevel(int value) {
      this.value = value;
    }

    // @@protoc_insertion_point(enum_scope:charging_common.EnumBatteryLevel)
  }

  /**
   * <pre>
   * This is used to indicate the different errors that can occur in the plug and charge system
   * </pre>
   *
   * Protobuf enum {@code charging_common.EnumPnCErrorCode}
   */
  public enum EnumPnCErrorCode
      implements com.google.protobuf.ProtocolMessageEnum {
    /**
     * <code>ENUM_PNC_ERROR_CODE_UNSPECIFIED = 0;</code>
     */
    ENUM_PNC_ERROR_CODE_UNSPECIFIED(0),
    /**
     * <code>ENUM_PNC_ERROR_CODE_NO_ERROR = 1;</code>
     */
    ENUM_PNC_ERROR_CODE_NO_ERROR(1),
    /**
     * <code>ENUM_PNC_ERROR_CODE_FAILED_NO_CERTIFICATE_AVAILABLE = 2;</code>
     */
    ENUM_PNC_ERROR_CODE_FAILED_NO_CERTIFICATE_AVAILABLE(2),
    /**
     * <code>ENUM_PNC_ERROR_CODE_FAILED_CONTRACT_CANCELED = 3;</code>
     */
    ENUM_PNC_ERROR_CODE_FAILED_CONTRACT_CANCELED(3),
    /**
     * <code>ENUM_PNC_ERROR_CODE_FAILED_CERTIFICATE_REVOKED = 4;</code>
     */
    ENUM_PNC_ERROR_CODE_FAILED_CERTIFICATE_REVOKED(4),
    /**
     * <code>ENUM_PNC_ERROR_CODE_FAILED_CERTIFICATE_EXPIRED = 5;</code>
     */
    ENUM_PNC_ERROR_CODE_FAILED_CERTIFICATE_EXPIRED(5),
    /**
     * <code>ENUM_PNC_ERROR_CODE_FAILED_CERT_CHAIN_ERROR = 6;</code>
     */
    ENUM_PNC_ERROR_CODE_FAILED_CERT_CHAIN_ERROR(6),
    /**
     * <code>ENUM_PNC_ERROR_CODE_FAILED_PAYMENT_SELECTION_INVALID = 7;</code>
     */
    ENUM_PNC_ERROR_CODE_FAILED_PAYMENT_SELECTION_INVALID(7),
    /**
     * <code>ENUM_PNC_ERROR_CODE_FAILED_SIGNATURE_ERROR = 8;</code>
     */
    ENUM_PNC_ERROR_CODE_FAILED_SIGNATURE_ERROR(8),
    /**
     * <code>ENUM_PNC_ERROR_CODE_FAILED_CHALLENGE_INVALID = 9;</code>
     */
    ENUM_PNC_ERROR_CODE_FAILED_CHALLENGE_INVALID(9),
    /**
     * <code>ENUM_PNC_ERROR_CODE_FAILED_TARIFF_SELECTION_INVALID = 10;</code>
     */
    ENUM_PNC_ERROR_CODE_FAILED_TARIFF_SELECTION_INVALID(10),
    /**
     * <code>ENUM_PNC_ERROR_CODE_FAILED_CHARGING_PROFILE_INVALID = 11;</code>
     */
    ENUM_PNC_ERROR_CODE_FAILED_CHARGING_PROFILE_INVALID(11),
    /**
     * <code>ENUM_PNC_ERROR_CODE_FAILED_UNKNOWN_SESSION = 12;</code>
     */
    ENUM_PNC_ERROR_CODE_FAILED_UNKNOWN_SESSION(12),
    /**
     * <code>ENUM_PNC_ERROR_CODE_FAILED_SERVICE_SELECTION_INVALID = 13;</code>
     */
    ENUM_PNC_ERROR_CODE_FAILED_SERVICE_SELECTION_INVALID(13),
    /**
     * <code>ENUM_PNC_ERROR_CODE_FAILED_SERVICE_ID_INVALID = 14;</code>
     */
    ENUM_PNC_ERROR_CODE_FAILED_SERVICE_ID_INVALID(14),
    /**
     * <code>ENUM_PNC_ERROR_CODE_FAILED_NO_CHARGE_SERVICE_SELECTED = 15;</code>
     */
    ENUM_PNC_ERROR_CODE_FAILED_NO_CHARGE_SERVICE_SELECTED(15),
    /**
     * <code>ENUM_PNC_ERROR_CODE_FAILED_POWER_DELIVERY_NOT_APPLIED = 16;</code>
     */
    ENUM_PNC_ERROR_CODE_FAILED_POWER_DELIVERY_NOT_APPLIED(16),
    /**
     * <code>ENUM_PNC_ERROR_CODE_FAILED_CONTACT_ERROR = 17;</code>
     */
    ENUM_PNC_ERROR_CODE_FAILED_CONTACT_ERROR(17),
    /**
     * <code>ENUM_PNC_ERROR_CODE_SEQUENCE_ERROR = 18;</code>
     */
    ENUM_PNC_ERROR_CODE_SEQUENCE_ERROR(18),
    /**
     * <code>ENUM_PNC_ERROR_CODE_FAILED_CERTIFICATE_NOT_ALLOWED_AT_THISEVSE = 19;</code>
     */
    ENUM_PNC_ERROR_CODE_FAILED_CERTIFICATE_NOT_ALLOWED_AT_THISEVSE(19),
    /**
     * <code>ENUM_PNC_ERROR_CODE_FAILED_WRONG_ENERGY_TRANSFER_MODE = 20;</code>
     */
    ENUM_PNC_ERROR_CODE_FAILED_WRONG_ENERGY_TRANSFER_MODE(20),
    /**
     * <code>ENUM_PNC_ERROR_CODE_FAILED_WRONG_CHARGE_PARAMETER = 21;</code>
     */
    ENUM_PNC_ERROR_CODE_FAILED_WRONG_CHARGE_PARAMETER(21),
    /**
     * <code>ENUM_PNC_ERROR_CODE_FAILED_METERING_SIGNATURE_NOT_VALID = 22;</code>
     */
    ENUM_PNC_ERROR_CODE_FAILED_METERING_SIGNATURE_NOT_VALID(22),
    /**
     * <code>ENUM_PNC_ERROR_CODE_FAILED_NO_NEGOTIATION = 23;</code>
     */
    ENUM_PNC_ERROR_CODE_FAILED_NO_NEGOTIATION(23),
    /**
     * <code>ENUM_PNC_ERROR_CODE_EVSE_CERTIFICATION_EXPIRED = 24;</code>
     */
    ENUM_PNC_ERROR_CODE_EVSE_CERTIFICATION_EXPIRED(24),
    /**
     * <code>ENUM_PNC_ERROR_CODE_EVSE_MALFUNCTION = 25;</code>
     */
    ENUM_PNC_ERROR_CODE_EVSE_MALFUNCTION(25),
    /**
     * <code>ENUM_PNC_ERROR_CODE_EVSE_SHUTDOWN = 26;</code>
     */
    ENUM_PNC_ERROR_CODE_EVSE_SHUTDOWN(26),
    /**
     * <code>ENUM_PNC_ERROR_CODE_EVSE_UTILITY_INTERUPT_EVENT = 27;</code>
     */
    ENUM_PNC_ERROR_CODE_EVSE_UTILITY_INTERUPT_EVENT(27),
    /**
     * <code>ENUM_PNC_ERROR_CODE_EVSE_EMERGENCY_SHUTDOWN = 28;</code>
     */
    ENUM_PNC_ERROR_CODE_EVSE_EMERGENCY_SHUTDOWN(28),
    /**
     * <code>ENUM_PNC_ERROR_CODE_EVSE_ISOLATION_STATUS_WARNING = 29;</code>
     */
    ENUM_PNC_ERROR_CODE_EVSE_ISOLATION_STATUS_WARNING(29),
    /**
     * <code>ENUM_PNC_ERROR_CODE_EVSE_ISOLATION_STATUS_FAULT = 30;</code>
     */
    ENUM_PNC_ERROR_CODE_EVSE_ISOLATION_STATUS_FAULT(30),
    /**
     * <code>ENUM_PNC_ERROR_CODE_RCD_TRUE = 31;</code>
     */
    ENUM_PNC_ERROR_CODE_RCD_TRUE(31),
    /**
     * <code>ENUM_PNC_ERROR_CODE_SUPPORTED_APP_PROTOCOL_REQ_TIMOUT = 32;</code>
     */
    ENUM_PNC_ERROR_CODE_SUPPORTED_APP_PROTOCOL_REQ_TIMOUT(32),
    /**
     * <code>ENUM_PNC_ERROR_CODE_SESSION_SETUP_REQ_TIMEOUT = 33;</code>
     */
    ENUM_PNC_ERROR_CODE_SESSION_SETUP_REQ_TIMEOUT(33),
    /**
     * <code>ENUM_PNC_ERROR_CODE_SERVICE_DISCOVERY_REQ_TIMEOUT = 34;</code>
     */
    ENUM_PNC_ERROR_CODE_SERVICE_DISCOVERY_REQ_TIMEOUT(34),
    /**
     * <code>ENUM_PNC_ERROR_CODE_SERVICE_DETAIL_REQ_TIMEOUT = 35;</code>
     */
    ENUM_PNC_ERROR_CODE_SERVICE_DETAIL_REQ_TIMEOUT(35),
    /**
     * <code>ENUM_PNC_ERROR_CODE_SERVICE_PAYMENT_SELECTION_REQ_TIMEOUT = 36;</code>
     */
    ENUM_PNC_ERROR_CODE_SERVICE_PAYMENT_SELECTION_REQ_TIMEOUT(36),
    /**
     * <code>ENUM_PNC_ERROR_CODE_PAYMENT_DETAIL_REQ_TIMEOUT = 37;</code>
     */
    ENUM_PNC_ERROR_CODE_PAYMENT_DETAIL_REQ_TIMEOUT(37),
    /**
     * <code>ENUM_PNC_ERROR_CODE_ATHORIZATION_REQ_TIMEOUT = 38;</code>
     */
    ENUM_PNC_ERROR_CODE_ATHORIZATION_REQ_TIMEOUT(38),
    /**
     * <code>ENUM_PNC_ERROR_CODE_CHARGE_PARAM_DISCOVERY_REQ_TIMEOUT = 39;</code>
     */
    ENUM_PNC_ERROR_CODE_CHARGE_PARAM_DISCOVERY_REQ_TIMEOUT(39),
    /**
     * <code>ENUM_PNC_ERROR_CODE_POWER_DELIVERY_REQ_TIMEOUT = 40;</code>
     */
    ENUM_PNC_ERROR_CODE_POWER_DELIVERY_REQ_TIMEOUT(40),
    /**
     * <code>ENUM_PNC_ERROR_CODE_CHARGING_STATUS_REQ_TIMEOUT = 41;</code>
     */
    ENUM_PNC_ERROR_CODE_CHARGING_STATUS_REQ_TIMEOUT(41),
    /**
     * <code>ENUM_PNC_ERROR_CODE_METERING_RECEIPT_REQ_TIMEOUT = 42;</code>
     */
    ENUM_PNC_ERROR_CODE_METERING_RECEIPT_REQ_TIMEOUT(42),
    /**
     * <code>ENUM_PNC_ERROR_CODE_CABLE_CHECK_REQ_TIMEOUT = 43;</code>
     */
    ENUM_PNC_ERROR_CODE_CABLE_CHECK_REQ_TIMEOUT(43),
    /**
     * <code>ENUM_PNC_ERROR_CODE_WELDING_DETECTION_REQ_TIMEOUT = 44;</code>
     */
    ENUM_PNC_ERROR_CODE_WELDING_DETECTION_REQ_TIMEOUT(44),
    /**
     * <code>ENUM_PNC_ERROR_CODE_PRECHARGE_REQ_TIMEOUT = 45;</code>
     */
    ENUM_PNC_ERROR_CODE_PRECHARGE_REQ_TIMEOUT(45),
    /**
     * <code>ENUM_PNC_ERROR_CODE_CURRENT_DEMAND_REQ_TIMEOUT = 46;</code>
     */
    ENUM_PNC_ERROR_CODE_CURRENT_DEMAND_REQ_TIMEOUT(46),
    /**
     * <code>ENUM_PNC_ERROR_CODE_SESSION_STOP_REQ_TIMEOUT = 47;</code>
     */
    ENUM_PNC_ERROR_CODE_SESSION_STOP_REQ_TIMEOUT(47),
    /**
     * <code>ENUM_PNC_ERROR_CODE_SUPPORTED_APP_PROTOCOL_RES_FAILED_TIMOUT = 48;</code>
     */
    ENUM_PNC_ERROR_CODE_SUPPORTED_APP_PROTOCOL_RES_FAILED_TIMOUT(48),
    /**
     * <code>ENUM_PNC_ERROR_CODE_SESSION_SETUP_RES_FAILED__TIMEOUT = 49;</code>
     */
    ENUM_PNC_ERROR_CODE_SESSION_SETUP_RES_FAILED__TIMEOUT(49),
    /**
     * <code>ENUM_PNC_ERROR_CODE_SERVICE_DISCOVERY_RES_FAILED_TIMEOUT = 50;</code>
     */
    ENUM_PNC_ERROR_CODE_SERVICE_DISCOVERY_RES_FAILED_TIMEOUT(50),
    /**
     * <code>ENUM_PNC_ERROR_CODE_SERVICE_DETAIL_RES_FAILED_TIMEOUT = 51;</code>
     */
    ENUM_PNC_ERROR_CODE_SERVICE_DETAIL_RES_FAILED_TIMEOUT(51),
    /**
     * <code>ENUM_PNC_ERROR_CODE_SERVICE_PAYMENT_SELECTION_RES_FAILED_TIMEOUT = 52;</code>
     */
    ENUM_PNC_ERROR_CODE_SERVICE_PAYMENT_SELECTION_RES_FAILED_TIMEOUT(52),
    /**
     * <code>ENUM_PNC_ERROR_CODE_PAYMENT_DETAILS_RES_FAILED_TIMEOUT = 53;</code>
     */
    ENUM_PNC_ERROR_CODE_PAYMENT_DETAILS_RES_FAILED_TIMEOUT(53),
    /**
     * <code>ENUM_PNC_ERROR_CODE_ATHORIZATION_RES_FAILED_TIMEOUT = 54;</code>
     */
    ENUM_PNC_ERROR_CODE_ATHORIZATION_RES_FAILED_TIMEOUT(54),
    /**
     * <code>ENUM_PNC_ERROR_CODE_CHARGE_PARAM_DISCOVERY_RES_FAILED_TIMEOUT = 55;</code>
     */
    ENUM_PNC_ERROR_CODE_CHARGE_PARAM_DISCOVERY_RES_FAILED_TIMEOUT(55),
    /**
     * <code>ENUM_PNC_ERROR_CODE_POWER_DELIVERY_RES_FAILED_TIMEOUT = 56;</code>
     */
    ENUM_PNC_ERROR_CODE_POWER_DELIVERY_RES_FAILED_TIMEOUT(56),
    /**
     * <code>ENUM_PNC_ERROR_CODE_CHARGING_STATUS_RES_FAILED_TIMEOUT = 57;</code>
     */
    ENUM_PNC_ERROR_CODE_CHARGING_STATUS_RES_FAILED_TIMEOUT(57),
    /**
     * <code>ENUM_PNC_ERROR_CODE_METERING_RECEIPT_RES_FAILED_TIMEOUT = 58;</code>
     */
    ENUM_PNC_ERROR_CODE_METERING_RECEIPT_RES_FAILED_TIMEOUT(58),
    /**
     * <code>ENUM_PNC_ERROR_CODE_CABLE_CHECK_RES_FAILED_TIMEOUT = 59;</code>
     */
    ENUM_PNC_ERROR_CODE_CABLE_CHECK_RES_FAILED_TIMEOUT(59),
    /**
     * <code>ENUM_PNC_ERROR_CODE_WELDING_DETECTION_RES_FAILED_TIMEOUT = 60;</code>
     */
    ENUM_PNC_ERROR_CODE_WELDING_DETECTION_RES_FAILED_TIMEOUT(60),
    /**
     * <code>ENUM_PNC_ERROR_CODE_PRECHARGE_RES_FAILED_TIMEOUT = 61;</code>
     */
    ENUM_PNC_ERROR_CODE_PRECHARGE_RES_FAILED_TIMEOUT(61),
    /**
     * <code>ENUM_PNC_ERROR_CODE_CURRENT_DEMAND_RES_FAILED_TIMEOUT = 62;</code>
     */
    ENUM_PNC_ERROR_CODE_CURRENT_DEMAND_RES_FAILED_TIMEOUT(62),
    /**
     * <code>ENUM_PNC_ERROR_CODE_SESSION_STOP_RES_FAILED_TIMEOUT = 63;</code>
     */
    ENUM_PNC_ERROR_CODE_SESSION_STOP_RES_FAILED_TIMEOUT(63),
    /**
     * <code>ENUM_PNC_ERROR_CODE_INPUT_SIGNAL_ERR_CCF_CHARGE_PNC = 64;</code>
     */
    ENUM_PNC_ERROR_CODE_INPUT_SIGNAL_ERR_CCF_CHARGE_PNC(64),
    /**
     * <code>ENUM_PNC_ERROR_CODE_INPUT_SIGNAL_ERR_PNC_TOGGLE_USER_REQ = 65;</code>
     */
    ENUM_PNC_ERROR_CODE_INPUT_SIGNAL_ERR_PNC_TOGGLE_USER_REQ(65),
    /**
     * <code>ENUM_PNC_ERROR_CODE_INPUT_SIGNAL_ERR_PNC_CHINA_VIN_SHARE = 66;</code>
     */
    ENUM_PNC_ERROR_CODE_INPUT_SIGNAL_ERR_PNC_CHINA_VIN_SHARE(66),
    /**
     * <code>ENUM_PNC_ERROR_CODE_INPUT_SIGNAL_ERR_PNC_CONTRACT_CERT_REQ = 67;</code>
     */
    ENUM_PNC_ERROR_CODE_INPUT_SIGNAL_ERR_PNC_CONTRACT_CERT_REQ(67),
    /**
     * <code>ENUM_PNC_ERROR_CODE_INPUT_SIGNAL_ERR_UTC_TIME_DATE = 68;</code>
     */
    ENUM_PNC_ERROR_CODE_INPUT_SIGNAL_ERR_UTC_TIME_DATE(68),
    /**
     * <code>ENUM_PNC_ERROR_CODE_INPUT_SIGNAL_ERR_MULTIPLE_SIGNALS = 69;</code>
     */
    ENUM_PNC_ERROR_CODE_INPUT_SIGNAL_ERR_MULTIPLE_SIGNALS(69),
    /**
     * <code>ENUM_PNC_ERROR_CODE_V2G_ROOT_CERT_EXPIRING_SOON = 70;</code>
     */
    ENUM_PNC_ERROR_CODE_V2G_ROOT_CERT_EXPIRING_SOON(70),
    /**
     * <code>ENUM_PNC_ERROR_CODE_V2G_ROOT_CERT_CORRUPTED = 71;</code>
     */
    ENUM_PNC_ERROR_CODE_V2G_ROOT_CERT_CORRUPTED(71),
    /**
     * <code>ENUM_PNC_ERROR_CODE_V2G_ROOT_CERT_EXPIRED = 72;</code>
     */
    ENUM_PNC_ERROR_CODE_V2G_ROOT_CERT_EXPIRED(72),
    /**
     * <code>ENUM_PNC_ERROR_CODE_V2G_ROOT_CERT_NOT_INSTALLED = 73;</code>
     */
    ENUM_PNC_ERROR_CODE_V2G_ROOT_CERT_NOT_INSTALLED(73),
    /**
     * <code>ENUM_PNC_ERROR_CODE_V2G_ROOT_CERT_MISSING = 74;</code>
     */
    ENUM_PNC_ERROR_CODE_V2G_ROOT_CERT_MISSING(74),
    /**
     * <code>ENUM_PNC_ERROR_CODE_OEM_ROOT_CERT_EXPIRING_SOON = 75;</code>
     */
    ENUM_PNC_ERROR_CODE_OEM_ROOT_CERT_EXPIRING_SOON(75),
    /**
     * <code>ENUM_PNC_ERROR_CODE_OEM_ROOT_CERT_CORRUPTED = 76;</code>
     */
    ENUM_PNC_ERROR_CODE_OEM_ROOT_CERT_CORRUPTED(76),
    /**
     * <code>ENUM_PNC_ERROR_CODE_OEM_ROOT_CERT_EXPIRED = 77;</code>
     */
    ENUM_PNC_ERROR_CODE_OEM_ROOT_CERT_EXPIRED(77),
    /**
     * <code>ENUM_PNC_ERROR_CODE_OEM_ROOT_CERT_NOT_INSTALLED = 78;</code>
     */
    ENUM_PNC_ERROR_CODE_OEM_ROOT_CERT_NOT_INSTALLED(78),
    /**
     * <code>ENUM_PNC_ERROR_CODE_OEM_PROVISION_CERT_SUB_CA1_EXPIRING_SOON = 79;</code>
     */
    ENUM_PNC_ERROR_CODE_OEM_PROVISION_CERT_SUB_CA1_EXPIRING_SOON(79),
    /**
     * <code>ENUM_PNC_ERROR_CODE_OEM_PROVISION_CERT_SUB_CA1_CORRUPTED = 80;</code>
     */
    ENUM_PNC_ERROR_CODE_OEM_PROVISION_CERT_SUB_CA1_CORRUPTED(80),
    /**
     * <code>ENUM_PNC_ERROR_CODE_OEM_PROVISION_CERT_SUB_CA1_EXPIRED = 81;</code>
     */
    ENUM_PNC_ERROR_CODE_OEM_PROVISION_CERT_SUB_CA1_EXPIRED(81),
    /**
     * <code>ENUM_PNC_ERROR_CODE_OEM_PROVISION_CERT_SUB_CA1_NOT_INSTALLED = 82;</code>
     */
    ENUM_PNC_ERROR_CODE_OEM_PROVISION_CERT_SUB_CA1_NOT_INSTALLED(82),
    /**
     * <code>ENUM_PNC_ERROR_CODE_OEM_PROVISION_CERT_SUB_CA2_EXPIRING_SOON = 83;</code>
     */
    ENUM_PNC_ERROR_CODE_OEM_PROVISION_CERT_SUB_CA2_EXPIRING_SOON(83),
    /**
     * <code>ENUM_PNC_ERROR_CODE_OEM_PROVISION_CERT_SUB_CA2_CORRUPTED = 84;</code>
     */
    ENUM_PNC_ERROR_CODE_OEM_PROVISION_CERT_SUB_CA2_CORRUPTED(84),
    /**
     * <code>ENUM_PNC_ERROR_CODE_OEM_PROVISION_CERT_SUB_CA2_EXPIRED = 85;</code>
     */
    ENUM_PNC_ERROR_CODE_OEM_PROVISION_CERT_SUB_CA2_EXPIRED(85),
    /**
     * <code>ENUM_PNC_ERROR_CODE_OEM_PROVISION_CERT_SUB_CA2_NOT_INSTALLED = 86;</code>
     */
    ENUM_PNC_ERROR_CODE_OEM_PROVISION_CERT_SUB_CA2_NOT_INSTALLED(86),
    /**
     * <code>ENUM_PNC_ERROR_CODE_OEM_PROVISION_CERT_LEAF_EXPIRING_SOON = 87;</code>
     */
    ENUM_PNC_ERROR_CODE_OEM_PROVISION_CERT_LEAF_EXPIRING_SOON(87),
    /**
     * <code>ENUM_PNC_ERROR_CODE_OEM_PROVISION_CERT_LEAF_CORRUPTED = 88;</code>
     */
    ENUM_PNC_ERROR_CODE_OEM_PROVISION_CERT_LEAF_CORRUPTED(88),
    /**
     * <code>ENUM_PNC_ERROR_CODE_OEM_PROVISION_CERT_LEAF_EXPIRED = 89;</code>
     */
    ENUM_PNC_ERROR_CODE_OEM_PROVISION_CERT_LEAF_EXPIRED(89),
    /**
     * <code>ENUM_PNC_ERROR_CODE_OEM_PROVISION_CERT_LEAF_NOT_INSTALLED = 90;</code>
     */
    ENUM_PNC_ERROR_CODE_OEM_PROVISION_CERT_LEAF_NOT_INSTALLED(90),
    /**
     * <code>ENUM_PNC_ERROR_CODE_OEM_PROVISION_CERT_PRIVATE_KEY_NOT_INSTALLED = 91;</code>
     */
    ENUM_PNC_ERROR_CODE_OEM_PROVISION_CERT_PRIVATE_KEY_NOT_INSTALLED(91),
    /**
     * <code>ENUM_PNC_ERROR_CODE_OEM_PROVISION_CERT_PRIVATE_KEY_CORRUPTED = 92;</code>
     */
    ENUM_PNC_ERROR_CODE_OEM_PROVISION_CERT_PRIVATE_KEY_CORRUPTED(92),
    UNRECOGNIZED(-1),
    ;

    static {
      com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
        com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
        /* major= */ 4,
        /* minor= */ 28,
        /* patch= */ 2,
        /* suffix= */ "",
        EnumPnCErrorCode.class.getName());
    }
    /**
     * <code>ENUM_PNC_ERROR_CODE_UNSPECIFIED = 0;</code>
     */
    public static final int ENUM_PNC_ERROR_CODE_UNSPECIFIED_VALUE = 0;
    /**
     * <code>ENUM_PNC_ERROR_CODE_NO_ERROR = 1;</code>
     */
    public static final int ENUM_PNC_ERROR_CODE_NO_ERROR_VALUE = 1;
    /**
     * <code>ENUM_PNC_ERROR_CODE_FAILED_NO_CERTIFICATE_AVAILABLE = 2;</code>
     */
    public static final int ENUM_PNC_ERROR_CODE_FAILED_NO_CERTIFICATE_AVAILABLE_VALUE = 2;
    /**
     * <code>ENUM_PNC_ERROR_CODE_FAILED_CONTRACT_CANCELED = 3;</code>
     */
    public static final int ENUM_PNC_ERROR_CODE_FAILED_CONTRACT_CANCELED_VALUE = 3;
    /**
     * <code>ENUM_PNC_ERROR_CODE_FAILED_CERTIFICATE_REVOKED = 4;</code>
     */
    public static final int ENUM_PNC_ERROR_CODE_FAILED_CERTIFICATE_REVOKED_VALUE = 4;
    /**
     * <code>ENUM_PNC_ERROR_CODE_FAILED_CERTIFICATE_EXPIRED = 5;</code>
     */
    public static final int ENUM_PNC_ERROR_CODE_FAILED_CERTIFICATE_EXPIRED_VALUE = 5;
    /**
     * <code>ENUM_PNC_ERROR_CODE_FAILED_CERT_CHAIN_ERROR = 6;</code>
     */
    public static final int ENUM_PNC_ERROR_CODE_FAILED_CERT_CHAIN_ERROR_VALUE = 6;
    /**
     * <code>ENUM_PNC_ERROR_CODE_FAILED_PAYMENT_SELECTION_INVALID = 7;</code>
     */
    public static final int ENUM_PNC_ERROR_CODE_FAILED_PAYMENT_SELECTION_INVALID_VALUE = 7;
    /**
     * <code>ENUM_PNC_ERROR_CODE_FAILED_SIGNATURE_ERROR = 8;</code>
     */
    public static final int ENUM_PNC_ERROR_CODE_FAILED_SIGNATURE_ERROR_VALUE = 8;
    /**
     * <code>ENUM_PNC_ERROR_CODE_FAILED_CHALLENGE_INVALID = 9;</code>
     */
    public static final int ENUM_PNC_ERROR_CODE_FAILED_CHALLENGE_INVALID_VALUE = 9;
    /**
     * <code>ENUM_PNC_ERROR_CODE_FAILED_TARIFF_SELECTION_INVALID = 10;</code>
     */
    public static final int ENUM_PNC_ERROR_CODE_FAILED_TARIFF_SELECTION_INVALID_VALUE = 10;
    /**
     * <code>ENUM_PNC_ERROR_CODE_FAILED_CHARGING_PROFILE_INVALID = 11;</code>
     */
    public static final int ENUM_PNC_ERROR_CODE_FAILED_CHARGING_PROFILE_INVALID_VALUE = 11;
    /**
     * <code>ENUM_PNC_ERROR_CODE_FAILED_UNKNOWN_SESSION = 12;</code>
     */
    public static final int ENUM_PNC_ERROR_CODE_FAILED_UNKNOWN_SESSION_VALUE = 12;
    /**
     * <code>ENUM_PNC_ERROR_CODE_FAILED_SERVICE_SELECTION_INVALID = 13;</code>
     */
    public static final int ENUM_PNC_ERROR_CODE_FAILED_SERVICE_SELECTION_INVALID_VALUE = 13;
    /**
     * <code>ENUM_PNC_ERROR_CODE_FAILED_SERVICE_ID_INVALID = 14;</code>
     */
    public static final int ENUM_PNC_ERROR_CODE_FAILED_SERVICE_ID_INVALID_VALUE = 14;
    /**
     * <code>ENUM_PNC_ERROR_CODE_FAILED_NO_CHARGE_SERVICE_SELECTED = 15;</code>
     */
    public static final int ENUM_PNC_ERROR_CODE_FAILED_NO_CHARGE_SERVICE_SELECTED_VALUE = 15;
    /**
     * <code>ENUM_PNC_ERROR_CODE_FAILED_POWER_DELIVERY_NOT_APPLIED = 16;</code>
     */
    public static final int ENUM_PNC_ERROR_CODE_FAILED_POWER_DELIVERY_NOT_APPLIED_VALUE = 16;
    /**
     * <code>ENUM_PNC_ERROR_CODE_FAILED_CONTACT_ERROR = 17;</code>
     */
    public static final int ENUM_PNC_ERROR_CODE_FAILED_CONTACT_ERROR_VALUE = 17;
    /**
     * <code>ENUM_PNC_ERROR_CODE_SEQUENCE_ERROR = 18;</code>
     */
    public static final int ENUM_PNC_ERROR_CODE_SEQUENCE_ERROR_VALUE = 18;
    /**
     * <code>ENUM_PNC_ERROR_CODE_FAILED_CERTIFICATE_NOT_ALLOWED_AT_THISEVSE = 19;</code>
     */
    public static final int ENUM_PNC_ERROR_CODE_FAILED_CERTIFICATE_NOT_ALLOWED_AT_THISEVSE_VALUE = 19;
    /**
     * <code>ENUM_PNC_ERROR_CODE_FAILED_WRONG_ENERGY_TRANSFER_MODE = 20;</code>
     */
    public static final int ENUM_PNC_ERROR_CODE_FAILED_WRONG_ENERGY_TRANSFER_MODE_VALUE = 20;
    /**
     * <code>ENUM_PNC_ERROR_CODE_FAILED_WRONG_CHARGE_PARAMETER = 21;</code>
     */
    public static final int ENUM_PNC_ERROR_CODE_FAILED_WRONG_CHARGE_PARAMETER_VALUE = 21;
    /**
     * <code>ENUM_PNC_ERROR_CODE_FAILED_METERING_SIGNATURE_NOT_VALID = 22;</code>
     */
    public static final int ENUM_PNC_ERROR_CODE_FAILED_METERING_SIGNATURE_NOT_VALID_VALUE = 22;
    /**
     * <code>ENUM_PNC_ERROR_CODE_FAILED_NO_NEGOTIATION = 23;</code>
     */
    public static final int ENUM_PNC_ERROR_CODE_FAILED_NO_NEGOTIATION_VALUE = 23;
    /**
     * <code>ENUM_PNC_ERROR_CODE_EVSE_CERTIFICATION_EXPIRED = 24;</code>
     */
    public static final int ENUM_PNC_ERROR_CODE_EVSE_CERTIFICATION_EXPIRED_VALUE = 24;
    /**
     * <code>ENUM_PNC_ERROR_CODE_EVSE_MALFUNCTION = 25;</code>
     */
    public static final int ENUM_PNC_ERROR_CODE_EVSE_MALFUNCTION_VALUE = 25;
    /**
     * <code>ENUM_PNC_ERROR_CODE_EVSE_SHUTDOWN = 26;</code>
     */
    public static final int ENUM_PNC_ERROR_CODE_EVSE_SHUTDOWN_VALUE = 26;
    /**
     * <code>ENUM_PNC_ERROR_CODE_EVSE_UTILITY_INTERUPT_EVENT = 27;</code>
     */
    public static final int ENUM_PNC_ERROR_CODE_EVSE_UTILITY_INTERUPT_EVENT_VALUE = 27;
    /**
     * <code>ENUM_PNC_ERROR_CODE_EVSE_EMERGENCY_SHUTDOWN = 28;</code>
     */
    public static final int ENUM_PNC_ERROR_CODE_EVSE_EMERGENCY_SHUTDOWN_VALUE = 28;
    /**
     * <code>ENUM_PNC_ERROR_CODE_EVSE_ISOLATION_STATUS_WARNING = 29;</code>
     */
    public static final int ENUM_PNC_ERROR_CODE_EVSE_ISOLATION_STATUS_WARNING_VALUE = 29;
    /**
     * <code>ENUM_PNC_ERROR_CODE_EVSE_ISOLATION_STATUS_FAULT = 30;</code>
     */
    public static final int ENUM_PNC_ERROR_CODE_EVSE_ISOLATION_STATUS_FAULT_VALUE = 30;
    /**
     * <code>ENUM_PNC_ERROR_CODE_RCD_TRUE = 31;</code>
     */
    public static final int ENUM_PNC_ERROR_CODE_RCD_TRUE_VALUE = 31;
    /**
     * <code>ENUM_PNC_ERROR_CODE_SUPPORTED_APP_PROTOCOL_REQ_TIMOUT = 32;</code>
     */
    public static final int ENUM_PNC_ERROR_CODE_SUPPORTED_APP_PROTOCOL_REQ_TIMOUT_VALUE = 32;
    /**
     * <code>ENUM_PNC_ERROR_CODE_SESSION_SETUP_REQ_TIMEOUT = 33;</code>
     */
    public static final int ENUM_PNC_ERROR_CODE_SESSION_SETUP_REQ_TIMEOUT_VALUE = 33;
    /**
     * <code>ENUM_PNC_ERROR_CODE_SERVICE_DISCOVERY_REQ_TIMEOUT = 34;</code>
     */
    public static final int ENUM_PNC_ERROR_CODE_SERVICE_DISCOVERY_REQ_TIMEOUT_VALUE = 34;
    /**
     * <code>ENUM_PNC_ERROR_CODE_SERVICE_DETAIL_REQ_TIMEOUT = 35;</code>
     */
    public static final int ENUM_PNC_ERROR_CODE_SERVICE_DETAIL_REQ_TIMEOUT_VALUE = 35;
    /**
     * <code>ENUM_PNC_ERROR_CODE_SERVICE_PAYMENT_SELECTION_REQ_TIMEOUT = 36;</code>
     */
    public static final int ENUM_PNC_ERROR_CODE_SERVICE_PAYMENT_SELECTION_REQ_TIMEOUT_VALUE = 36;
    /**
     * <code>ENUM_PNC_ERROR_CODE_PAYMENT_DETAIL_REQ_TIMEOUT = 37;</code>
     */
    public static final int ENUM_PNC_ERROR_CODE_PAYMENT_DETAIL_REQ_TIMEOUT_VALUE = 37;
    /**
     * <code>ENUM_PNC_ERROR_CODE_ATHORIZATION_REQ_TIMEOUT = 38;</code>
     */
    public static final int ENUM_PNC_ERROR_CODE_ATHORIZATION_REQ_TIMEOUT_VALUE = 38;
    /**
     * <code>ENUM_PNC_ERROR_CODE_CHARGE_PARAM_DISCOVERY_REQ_TIMEOUT = 39;</code>
     */
    public static final int ENUM_PNC_ERROR_CODE_CHARGE_PARAM_DISCOVERY_REQ_TIMEOUT_VALUE = 39;
    /**
     * <code>ENUM_PNC_ERROR_CODE_POWER_DELIVERY_REQ_TIMEOUT = 40;</code>
     */
    public static final int ENUM_PNC_ERROR_CODE_POWER_DELIVERY_REQ_TIMEOUT_VALUE = 40;
    /**
     * <code>ENUM_PNC_ERROR_CODE_CHARGING_STATUS_REQ_TIMEOUT = 41;</code>
     */
    public static final int ENUM_PNC_ERROR_CODE_CHARGING_STATUS_REQ_TIMEOUT_VALUE = 41;
    /**
     * <code>ENUM_PNC_ERROR_CODE_METERING_RECEIPT_REQ_TIMEOUT = 42;</code>
     */
    public static final int ENUM_PNC_ERROR_CODE_METERING_RECEIPT_REQ_TIMEOUT_VALUE = 42;
    /**
     * <code>ENUM_PNC_ERROR_CODE_CABLE_CHECK_REQ_TIMEOUT = 43;</code>
     */
    public static final int ENUM_PNC_ERROR_CODE_CABLE_CHECK_REQ_TIMEOUT_VALUE = 43;
    /**
     * <code>ENUM_PNC_ERROR_CODE_WELDING_DETECTION_REQ_TIMEOUT = 44;</code>
     */
    public static final int ENUM_PNC_ERROR_CODE_WELDING_DETECTION_REQ_TIMEOUT_VALUE = 44;
    /**
     * <code>ENUM_PNC_ERROR_CODE_PRECHARGE_REQ_TIMEOUT = 45;</code>
     */
    public static final int ENUM_PNC_ERROR_CODE_PRECHARGE_REQ_TIMEOUT_VALUE = 45;
    /**
     * <code>ENUM_PNC_ERROR_CODE_CURRENT_DEMAND_REQ_TIMEOUT = 46;</code>
     */
    public static final int ENUM_PNC_ERROR_CODE_CURRENT_DEMAND_REQ_TIMEOUT_VALUE = 46;
    /**
     * <code>ENUM_PNC_ERROR_CODE_SESSION_STOP_REQ_TIMEOUT = 47;</code>
     */
    public static final int ENUM_PNC_ERROR_CODE_SESSION_STOP_REQ_TIMEOUT_VALUE = 47;
    /**
     * <code>ENUM_PNC_ERROR_CODE_SUPPORTED_APP_PROTOCOL_RES_FAILED_TIMOUT = 48;</code>
     */
    public static final int ENUM_PNC_ERROR_CODE_SUPPORTED_APP_PROTOCOL_RES_FAILED_TIMOUT_VALUE = 48;
    /**
     * <code>ENUM_PNC_ERROR_CODE_SESSION_SETUP_RES_FAILED__TIMEOUT = 49;</code>
     */
    public static final int ENUM_PNC_ERROR_CODE_SESSION_SETUP_RES_FAILED__TIMEOUT_VALUE = 49;
    /**
     * <code>ENUM_PNC_ERROR_CODE_SERVICE_DISCOVERY_RES_FAILED_TIMEOUT = 50;</code>
     */
    public static final int ENUM_PNC_ERROR_CODE_SERVICE_DISCOVERY_RES_FAILED_TIMEOUT_VALUE = 50;
    /**
     * <code>ENUM_PNC_ERROR_CODE_SERVICE_DETAIL_RES_FAILED_TIMEOUT = 51;</code>
     */
    public static final int ENUM_PNC_ERROR_CODE_SERVICE_DETAIL_RES_FAILED_TIMEOUT_VALUE = 51;
    /**
     * <code>ENUM_PNC_ERROR_CODE_SERVICE_PAYMENT_SELECTION_RES_FAILED_TIMEOUT = 52;</code>
     */
    public static final int ENUM_PNC_ERROR_CODE_SERVICE_PAYMENT_SELECTION_RES_FAILED_TIMEOUT_VALUE = 52;
    /**
     * <code>ENUM_PNC_ERROR_CODE_PAYMENT_DETAILS_RES_FAILED_TIMEOUT = 53;</code>
     */
    public static final int ENUM_PNC_ERROR_CODE_PAYMENT_DETAILS_RES_FAILED_TIMEOUT_VALUE = 53;
    /**
     * <code>ENUM_PNC_ERROR_CODE_ATHORIZATION_RES_FAILED_TIMEOUT = 54;</code>
     */
    public static final int ENUM_PNC_ERROR_CODE_ATHORIZATION_RES_FAILED_TIMEOUT_VALUE = 54;
    /**
     * <code>ENUM_PNC_ERROR_CODE_CHARGE_PARAM_DISCOVERY_RES_FAILED_TIMEOUT = 55;</code>
     */
    public static final int ENUM_PNC_ERROR_CODE_CHARGE_PARAM_DISCOVERY_RES_FAILED_TIMEOUT_VALUE = 55;
    /**
     * <code>ENUM_PNC_ERROR_CODE_POWER_DELIVERY_RES_FAILED_TIMEOUT = 56;</code>
     */
    public static final int ENUM_PNC_ERROR_CODE_POWER_DELIVERY_RES_FAILED_TIMEOUT_VALUE = 56;
    /**
     * <code>ENUM_PNC_ERROR_CODE_CHARGING_STATUS_RES_FAILED_TIMEOUT = 57;</code>
     */
    public static final int ENUM_PNC_ERROR_CODE_CHARGING_STATUS_RES_FAILED_TIMEOUT_VALUE = 57;
    /**
     * <code>ENUM_PNC_ERROR_CODE_METERING_RECEIPT_RES_FAILED_TIMEOUT = 58;</code>
     */
    public static final int ENUM_PNC_ERROR_CODE_METERING_RECEIPT_RES_FAILED_TIMEOUT_VALUE = 58;
    /**
     * <code>ENUM_PNC_ERROR_CODE_CABLE_CHECK_RES_FAILED_TIMEOUT = 59;</code>
     */
    public static final int ENUM_PNC_ERROR_CODE_CABLE_CHECK_RES_FAILED_TIMEOUT_VALUE = 59;
    /**
     * <code>ENUM_PNC_ERROR_CODE_WELDING_DETECTION_RES_FAILED_TIMEOUT = 60;</code>
     */
    public static final int ENUM_PNC_ERROR_CODE_WELDING_DETECTION_RES_FAILED_TIMEOUT_VALUE = 60;
    /**
     * <code>ENUM_PNC_ERROR_CODE_PRECHARGE_RES_FAILED_TIMEOUT = 61;</code>
     */
    public static final int ENUM_PNC_ERROR_CODE_PRECHARGE_RES_FAILED_TIMEOUT_VALUE = 61;
    /**
     * <code>ENUM_PNC_ERROR_CODE_CURRENT_DEMAND_RES_FAILED_TIMEOUT = 62;</code>
     */
    public static final int ENUM_PNC_ERROR_CODE_CURRENT_DEMAND_RES_FAILED_TIMEOUT_VALUE = 62;
    /**
     * <code>ENUM_PNC_ERROR_CODE_SESSION_STOP_RES_FAILED_TIMEOUT = 63;</code>
     */
    public static final int ENUM_PNC_ERROR_CODE_SESSION_STOP_RES_FAILED_TIMEOUT_VALUE = 63;
    /**
     * <code>ENUM_PNC_ERROR_CODE_INPUT_SIGNAL_ERR_CCF_CHARGE_PNC = 64;</code>
     */
    public static final int ENUM_PNC_ERROR_CODE_INPUT_SIGNAL_ERR_CCF_CHARGE_PNC_VALUE = 64;
    /**
     * <code>ENUM_PNC_ERROR_CODE_INPUT_SIGNAL_ERR_PNC_TOGGLE_USER_REQ = 65;</code>
     */
    public static final int ENUM_PNC_ERROR_CODE_INPUT_SIGNAL_ERR_PNC_TOGGLE_USER_REQ_VALUE = 65;
    /**
     * <code>ENUM_PNC_ERROR_CODE_INPUT_SIGNAL_ERR_PNC_CHINA_VIN_SHARE = 66;</code>
     */
    public static final int ENUM_PNC_ERROR_CODE_INPUT_SIGNAL_ERR_PNC_CHINA_VIN_SHARE_VALUE = 66;
    /**
     * <code>ENUM_PNC_ERROR_CODE_INPUT_SIGNAL_ERR_PNC_CONTRACT_CERT_REQ = 67;</code>
     */
    public static final int ENUM_PNC_ERROR_CODE_INPUT_SIGNAL_ERR_PNC_CONTRACT_CERT_REQ_VALUE = 67;
    /**
     * <code>ENUM_PNC_ERROR_CODE_INPUT_SIGNAL_ERR_UTC_TIME_DATE = 68;</code>
     */
    public static final int ENUM_PNC_ERROR_CODE_INPUT_SIGNAL_ERR_UTC_TIME_DATE_VALUE = 68;
    /**
     * <code>ENUM_PNC_ERROR_CODE_INPUT_SIGNAL_ERR_MULTIPLE_SIGNALS = 69;</code>
     */
    public static final int ENUM_PNC_ERROR_CODE_INPUT_SIGNAL_ERR_MULTIPLE_SIGNALS_VALUE = 69;
    /**
     * <code>ENUM_PNC_ERROR_CODE_V2G_ROOT_CERT_EXPIRING_SOON = 70;</code>
     */
    public static final int ENUM_PNC_ERROR_CODE_V2G_ROOT_CERT_EXPIRING_SOON_VALUE = 70;
    /**
     * <code>ENUM_PNC_ERROR_CODE_V2G_ROOT_CERT_CORRUPTED = 71;</code>
     */
    public static final int ENUM_PNC_ERROR_CODE_V2G_ROOT_CERT_CORRUPTED_VALUE = 71;
    /**
     * <code>ENUM_PNC_ERROR_CODE_V2G_ROOT_CERT_EXPIRED = 72;</code>
     */
    public static final int ENUM_PNC_ERROR_CODE_V2G_ROOT_CERT_EXPIRED_VALUE = 72;
    /**
     * <code>ENUM_PNC_ERROR_CODE_V2G_ROOT_CERT_NOT_INSTALLED = 73;</code>
     */
    public static final int ENUM_PNC_ERROR_CODE_V2G_ROOT_CERT_NOT_INSTALLED_VALUE = 73;
    /**
     * <code>ENUM_PNC_ERROR_CODE_V2G_ROOT_CERT_MISSING = 74;</code>
     */
    public static final int ENUM_PNC_ERROR_CODE_V2G_ROOT_CERT_MISSING_VALUE = 74;
    /**
     * <code>ENUM_PNC_ERROR_CODE_OEM_ROOT_CERT_EXPIRING_SOON = 75;</code>
     */
    public static final int ENUM_PNC_ERROR_CODE_OEM_ROOT_CERT_EXPIRING_SOON_VALUE = 75;
    /**
     * <code>ENUM_PNC_ERROR_CODE_OEM_ROOT_CERT_CORRUPTED = 76;</code>
     */
    public static final int ENUM_PNC_ERROR_CODE_OEM_ROOT_CERT_CORRUPTED_VALUE = 76;
    /**
     * <code>ENUM_PNC_ERROR_CODE_OEM_ROOT_CERT_EXPIRED = 77;</code>
     */
    public static final int ENUM_PNC_ERROR_CODE_OEM_ROOT_CERT_EXPIRED_VALUE = 77;
    /**
     * <code>ENUM_PNC_ERROR_CODE_OEM_ROOT_CERT_NOT_INSTALLED = 78;</code>
     */
    public static final int ENUM_PNC_ERROR_CODE_OEM_ROOT_CERT_NOT_INSTALLED_VALUE = 78;
    /**
     * <code>ENUM_PNC_ERROR_CODE_OEM_PROVISION_CERT_SUB_CA1_EXPIRING_SOON = 79;</code>
     */
    public static final int ENUM_PNC_ERROR_CODE_OEM_PROVISION_CERT_SUB_CA1_EXPIRING_SOON_VALUE = 79;
    /**
     * <code>ENUM_PNC_ERROR_CODE_OEM_PROVISION_CERT_SUB_CA1_CORRUPTED = 80;</code>
     */
    public static final int ENUM_PNC_ERROR_CODE_OEM_PROVISION_CERT_SUB_CA1_CORRUPTED_VALUE = 80;
    /**
     * <code>ENUM_PNC_ERROR_CODE_OEM_PROVISION_CERT_SUB_CA1_EXPIRED = 81;</code>
     */
    public static final int ENUM_PNC_ERROR_CODE_OEM_PROVISION_CERT_SUB_CA1_EXPIRED_VALUE = 81;
    /**
     * <code>ENUM_PNC_ERROR_CODE_OEM_PROVISION_CERT_SUB_CA1_NOT_INSTALLED = 82;</code>
     */
    public static final int ENUM_PNC_ERROR_CODE_OEM_PROVISION_CERT_SUB_CA1_NOT_INSTALLED_VALUE = 82;
    /**
     * <code>ENUM_PNC_ERROR_CODE_OEM_PROVISION_CERT_SUB_CA2_EXPIRING_SOON = 83;</code>
     */
    public static final int ENUM_PNC_ERROR_CODE_OEM_PROVISION_CERT_SUB_CA2_EXPIRING_SOON_VALUE = 83;
    /**
     * <code>ENUM_PNC_ERROR_CODE_OEM_PROVISION_CERT_SUB_CA2_CORRUPTED = 84;</code>
     */
    public static final int ENUM_PNC_ERROR_CODE_OEM_PROVISION_CERT_SUB_CA2_CORRUPTED_VALUE = 84;
    /**
     * <code>ENUM_PNC_ERROR_CODE_OEM_PROVISION_CERT_SUB_CA2_EXPIRED = 85;</code>
     */
    public static final int ENUM_PNC_ERROR_CODE_OEM_PROVISION_CERT_SUB_CA2_EXPIRED_VALUE = 85;
    /**
     * <code>ENUM_PNC_ERROR_CODE_OEM_PROVISION_CERT_SUB_CA2_NOT_INSTALLED = 86;</code>
     */
    public static final int ENUM_PNC_ERROR_CODE_OEM_PROVISION_CERT_SUB_CA2_NOT_INSTALLED_VALUE = 86;
    /**
     * <code>ENUM_PNC_ERROR_CODE_OEM_PROVISION_CERT_LEAF_EXPIRING_SOON = 87;</code>
     */
    public static final int ENUM_PNC_ERROR_CODE_OEM_PROVISION_CERT_LEAF_EXPIRING_SOON_VALUE = 87;
    /**
     * <code>ENUM_PNC_ERROR_CODE_OEM_PROVISION_CERT_LEAF_CORRUPTED = 88;</code>
     */
    public static final int ENUM_PNC_ERROR_CODE_OEM_PROVISION_CERT_LEAF_CORRUPTED_VALUE = 88;
    /**
     * <code>ENUM_PNC_ERROR_CODE_OEM_PROVISION_CERT_LEAF_EXPIRED = 89;</code>
     */
    public static final int ENUM_PNC_ERROR_CODE_OEM_PROVISION_CERT_LEAF_EXPIRED_VALUE = 89;
    /**
     * <code>ENUM_PNC_ERROR_CODE_OEM_PROVISION_CERT_LEAF_NOT_INSTALLED = 90;</code>
     */
    public static final int ENUM_PNC_ERROR_CODE_OEM_PROVISION_CERT_LEAF_NOT_INSTALLED_VALUE = 90;
    /**
     * <code>ENUM_PNC_ERROR_CODE_OEM_PROVISION_CERT_PRIVATE_KEY_NOT_INSTALLED = 91;</code>
     */
    public static final int ENUM_PNC_ERROR_CODE_OEM_PROVISION_CERT_PRIVATE_KEY_NOT_INSTALLED_VALUE = 91;
    /**
     * <code>ENUM_PNC_ERROR_CODE_OEM_PROVISION_CERT_PRIVATE_KEY_CORRUPTED = 92;</code>
     */
    public static final int ENUM_PNC_ERROR_CODE_OEM_PROVISION_CERT_PRIVATE_KEY_CORRUPTED_VALUE = 92;


    public final int getNumber() {
      if (this == UNRECOGNIZED) {
        throw new java.lang.IllegalArgumentException(
            "Can't get the number of an unknown enum value.");
      }
      return value;
    }

    /**
     * @param value The numeric wire value of the corresponding enum entry.
     * @return The enum associated with the given numeric wire value.
     * @deprecated Use {@link #forNumber(int)} instead.
     */
    @java.lang.Deprecated
    public static EnumPnCErrorCode valueOf(int value) {
      return forNumber(value);
    }

    /**
     * @param value The numeric wire value of the corresponding enum entry.
     * @return The enum associated with the given numeric wire value.
     */
    public static EnumPnCErrorCode forNumber(int value) {
      switch (value) {
        case 0: return ENUM_PNC_ERROR_CODE_UNSPECIFIED;
        case 1: return ENUM_PNC_ERROR_CODE_NO_ERROR;
        case 2: return ENUM_PNC_ERROR_CODE_FAILED_NO_CERTIFICATE_AVAILABLE;
        case 3: return ENUM_PNC_ERROR_CODE_FAILED_CONTRACT_CANCELED;
        case 4: return ENUM_PNC_ERROR_CODE_FAILED_CERTIFICATE_REVOKED;
        case 5: return ENUM_PNC_ERROR_CODE_FAILED_CERTIFICATE_EXPIRED;
        case 6: return ENUM_PNC_ERROR_CODE_FAILED_CERT_CHAIN_ERROR;
        case 7: return ENUM_PNC_ERROR_CODE_FAILED_PAYMENT_SELECTION_INVALID;
        case 8: return ENUM_PNC_ERROR_CODE_FAILED_SIGNATURE_ERROR;
        case 9: return ENUM_PNC_ERROR_CODE_FAILED_CHALLENGE_INVALID;
        case 10: return ENUM_PNC_ERROR_CODE_FAILED_TARIFF_SELECTION_INVALID;
        case 11: return ENUM_PNC_ERROR_CODE_FAILED_CHARGING_PROFILE_INVALID;
        case 12: return ENUM_PNC_ERROR_CODE_FAILED_UNKNOWN_SESSION;
        case 13: return ENUM_PNC_ERROR_CODE_FAILED_SERVICE_SELECTION_INVALID;
        case 14: return ENUM_PNC_ERROR_CODE_FAILED_SERVICE_ID_INVALID;
        case 15: return ENUM_PNC_ERROR_CODE_FAILED_NO_CHARGE_SERVICE_SELECTED;
        case 16: return ENUM_PNC_ERROR_CODE_FAILED_POWER_DELIVERY_NOT_APPLIED;
        case 17: return ENUM_PNC_ERROR_CODE_FAILED_CONTACT_ERROR;
        case 18: return ENUM_PNC_ERROR_CODE_SEQUENCE_ERROR;
        case 19: return ENUM_PNC_ERROR_CODE_FAILED_CERTIFICATE_NOT_ALLOWED_AT_THISEVSE;
        case 20: return ENUM_PNC_ERROR_CODE_FAILED_WRONG_ENERGY_TRANSFER_MODE;
        case 21: return ENUM_PNC_ERROR_CODE_FAILED_WRONG_CHARGE_PARAMETER;
        case 22: return ENUM_PNC_ERROR_CODE_FAILED_METERING_SIGNATURE_NOT_VALID;
        case 23: return ENUM_PNC_ERROR_CODE_FAILED_NO_NEGOTIATION;
        case 24: return ENUM_PNC_ERROR_CODE_EVSE_CERTIFICATION_EXPIRED;
        case 25: return ENUM_PNC_ERROR_CODE_EVSE_MALFUNCTION;
        case 26: return ENUM_PNC_ERROR_CODE_EVSE_SHUTDOWN;
        case 27: return ENUM_PNC_ERROR_CODE_EVSE_UTILITY_INTERUPT_EVENT;
        case 28: return ENUM_PNC_ERROR_CODE_EVSE_EMERGENCY_SHUTDOWN;
        case 29: return ENUM_PNC_ERROR_CODE_EVSE_ISOLATION_STATUS_WARNING;
        case 30: return ENUM_PNC_ERROR_CODE_EVSE_ISOLATION_STATUS_FAULT;
        case 31: return ENUM_PNC_ERROR_CODE_RCD_TRUE;
        case 32: return ENUM_PNC_ERROR_CODE_SUPPORTED_APP_PROTOCOL_REQ_TIMOUT;
        case 33: return ENUM_PNC_ERROR_CODE_SESSION_SETUP_REQ_TIMEOUT;
        case 34: return ENUM_PNC_ERROR_CODE_SERVICE_DISCOVERY_REQ_TIMEOUT;
        case 35: return ENUM_PNC_ERROR_CODE_SERVICE_DETAIL_REQ_TIMEOUT;
        case 36: return ENUM_PNC_ERROR_CODE_SERVICE_PAYMENT_SELECTION_REQ_TIMEOUT;
        case 37: return ENUM_PNC_ERROR_CODE_PAYMENT_DETAIL_REQ_TIMEOUT;
        case 38: return ENUM_PNC_ERROR_CODE_ATHORIZATION_REQ_TIMEOUT;
        case 39: return ENUM_PNC_ERROR_CODE_CHARGE_PARAM_DISCOVERY_REQ_TIMEOUT;
        case 40: return ENUM_PNC_ERROR_CODE_POWER_DELIVERY_REQ_TIMEOUT;
        case 41: return ENUM_PNC_ERROR_CODE_CHARGING_STATUS_REQ_TIMEOUT;
        case 42: return ENUM_PNC_ERROR_CODE_METERING_RECEIPT_REQ_TIMEOUT;
        case 43: return ENUM_PNC_ERROR_CODE_CABLE_CHECK_REQ_TIMEOUT;
        case 44: return ENUM_PNC_ERROR_CODE_WELDING_DETECTION_REQ_TIMEOUT;
        case 45: return ENUM_PNC_ERROR_CODE_PRECHARGE_REQ_TIMEOUT;
        case 46: return ENUM_PNC_ERROR_CODE_CURRENT_DEMAND_REQ_TIMEOUT;
        case 47: return ENUM_PNC_ERROR_CODE_SESSION_STOP_REQ_TIMEOUT;
        case 48: return ENUM_PNC_ERROR_CODE_SUPPORTED_APP_PROTOCOL_RES_FAILED_TIMOUT;
        case 49: return ENUM_PNC_ERROR_CODE_SESSION_SETUP_RES_FAILED__TIMEOUT;
        case 50: return ENUM_PNC_ERROR_CODE_SERVICE_DISCOVERY_RES_FAILED_TIMEOUT;
        case 51: return ENUM_PNC_ERROR_CODE_SERVICE_DETAIL_RES_FAILED_TIMEOUT;
        case 52: return ENUM_PNC_ERROR_CODE_SERVICE_PAYMENT_SELECTION_RES_FAILED_TIMEOUT;
        case 53: return ENUM_PNC_ERROR_CODE_PAYMENT_DETAILS_RES_FAILED_TIMEOUT;
        case 54: return ENUM_PNC_ERROR_CODE_ATHORIZATION_RES_FAILED_TIMEOUT;
        case 55: return ENUM_PNC_ERROR_CODE_CHARGE_PARAM_DISCOVERY_RES_FAILED_TIMEOUT;
        case 56: return ENUM_PNC_ERROR_CODE_POWER_DELIVERY_RES_FAILED_TIMEOUT;
        case 57: return ENUM_PNC_ERROR_CODE_CHARGING_STATUS_RES_FAILED_TIMEOUT;
        case 58: return ENUM_PNC_ERROR_CODE_METERING_RECEIPT_RES_FAILED_TIMEOUT;
        case 59: return ENUM_PNC_ERROR_CODE_CABLE_CHECK_RES_FAILED_TIMEOUT;
        case 60: return ENUM_PNC_ERROR_CODE_WELDING_DETECTION_RES_FAILED_TIMEOUT;
        case 61: return ENUM_PNC_ERROR_CODE_PRECHARGE_RES_FAILED_TIMEOUT;
        case 62: return ENUM_PNC_ERROR_CODE_CURRENT_DEMAND_RES_FAILED_TIMEOUT;
        case 63: return ENUM_PNC_ERROR_CODE_SESSION_STOP_RES_FAILED_TIMEOUT;
        case 64: return ENUM_PNC_ERROR_CODE_INPUT_SIGNAL_ERR_CCF_CHARGE_PNC;
        case 65: return ENUM_PNC_ERROR_CODE_INPUT_SIGNAL_ERR_PNC_TOGGLE_USER_REQ;
        case 66: return ENUM_PNC_ERROR_CODE_INPUT_SIGNAL_ERR_PNC_CHINA_VIN_SHARE;
        case 67: return ENUM_PNC_ERROR_CODE_INPUT_SIGNAL_ERR_PNC_CONTRACT_CERT_REQ;
        case 68: return ENUM_PNC_ERROR_CODE_INPUT_SIGNAL_ERR_UTC_TIME_DATE;
        case 69: return ENUM_PNC_ERROR_CODE_INPUT_SIGNAL_ERR_MULTIPLE_SIGNALS;
        case 70: return ENUM_PNC_ERROR_CODE_V2G_ROOT_CERT_EXPIRING_SOON;
        case 71: return ENUM_PNC_ERROR_CODE_V2G_ROOT_CERT_CORRUPTED;
        case 72: return ENUM_PNC_ERROR_CODE_V2G_ROOT_CERT_EXPIRED;
        case 73: return ENUM_PNC_ERROR_CODE_V2G_ROOT_CERT_NOT_INSTALLED;
        case 74: return ENUM_PNC_ERROR_CODE_V2G_ROOT_CERT_MISSING;
        case 75: return ENUM_PNC_ERROR_CODE_OEM_ROOT_CERT_EXPIRING_SOON;
        case 76: return ENUM_PNC_ERROR_CODE_OEM_ROOT_CERT_CORRUPTED;
        case 77: return ENUM_PNC_ERROR_CODE_OEM_ROOT_CERT_EXPIRED;
        case 78: return ENUM_PNC_ERROR_CODE_OEM_ROOT_CERT_NOT_INSTALLED;
        case 79: return ENUM_PNC_ERROR_CODE_OEM_PROVISION_CERT_SUB_CA1_EXPIRING_SOON;
        case 80: return ENUM_PNC_ERROR_CODE_OEM_PROVISION_CERT_SUB_CA1_CORRUPTED;
        case 81: return ENUM_PNC_ERROR_CODE_OEM_PROVISION_CERT_SUB_CA1_EXPIRED;
        case 82: return ENUM_PNC_ERROR_CODE_OEM_PROVISION_CERT_SUB_CA1_NOT_INSTALLED;
        case 83: return ENUM_PNC_ERROR_CODE_OEM_PROVISION_CERT_SUB_CA2_EXPIRING_SOON;
        case 84: return ENUM_PNC_ERROR_CODE_OEM_PROVISION_CERT_SUB_CA2_CORRUPTED;
        case 85: return ENUM_PNC_ERROR_CODE_OEM_PROVISION_CERT_SUB_CA2_EXPIRED;
        case 86: return ENUM_PNC_ERROR_CODE_OEM_PROVISION_CERT_SUB_CA2_NOT_INSTALLED;
        case 87: return ENUM_PNC_ERROR_CODE_OEM_PROVISION_CERT_LEAF_EXPIRING_SOON;
        case 88: return ENUM_PNC_ERROR_CODE_OEM_PROVISION_CERT_LEAF_CORRUPTED;
        case 89: return ENUM_PNC_ERROR_CODE_OEM_PROVISION_CERT_LEAF_EXPIRED;
        case 90: return ENUM_PNC_ERROR_CODE_OEM_PROVISION_CERT_LEAF_NOT_INSTALLED;
        case 91: return ENUM_PNC_ERROR_CODE_OEM_PROVISION_CERT_PRIVATE_KEY_NOT_INSTALLED;
        case 92: return ENUM_PNC_ERROR_CODE_OEM_PROVISION_CERT_PRIVATE_KEY_CORRUPTED;
        default: return null;
      }
    }

    public static com.google.protobuf.Internal.EnumLiteMap<EnumPnCErrorCode>
        internalGetValueMap() {
      return internalValueMap;
    }
    private static final com.google.protobuf.Internal.EnumLiteMap<
        EnumPnCErrorCode> internalValueMap =
          new com.google.protobuf.Internal.EnumLiteMap<EnumPnCErrorCode>() {
            public EnumPnCErrorCode findValueByNumber(int number) {
              return EnumPnCErrorCode.forNumber(number);
            }
          };

    public final com.google.protobuf.Descriptors.EnumValueDescriptor
        getValueDescriptor() {
      if (this == UNRECOGNIZED) {
        throw new java.lang.IllegalStateException(
            "Can't get the descriptor of an unrecognized enum value.");
      }
      return getDescriptor().getValues().get(ordinal());
    }
    public final com.google.protobuf.Descriptors.EnumDescriptor
        getDescriptorForType() {
      return getDescriptor();
    }
    public static final com.google.protobuf.Descriptors.EnumDescriptor
        getDescriptor() {
      return charging_common.ChargingCommon.getDescriptor().getEnumTypes().get(26);
    }

    private static final EnumPnCErrorCode[] VALUES = values();

    public static EnumPnCErrorCode valueOf(
        com.google.protobuf.Descriptors.EnumValueDescriptor desc) {
      if (desc.getType() != getDescriptor()) {
        throw new java.lang.IllegalArgumentException(
          "EnumValueDescriptor is not for this type.");
      }
      if (desc.getIndex() == -1) {
        return UNRECOGNIZED;
      }
      return VALUES[desc.getIndex()];
    }

    private final int value;

    private EnumPnCErrorCode(int value) {
      this.value = value;
    }

    // @@protoc_insertion_point(enum_scope:charging_common.EnumPnCErrorCode)
  }

  public interface PredictedChargeDataOrBuilder extends
      // @@protoc_insertion_point(interface_extends:charging_common.PredictedChargeData)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     * soc percentage at the indicated level in percentage (0 to 100)
     * </pre>
     *
     * <code>uint32 battery_level = 1;</code>
     * @return The batteryLevel.
     */
    int getBatteryLevel();

    /**
     * <pre>
     * predicted range at the indicated level in kms (0 to 1000)
     * </pre>
     *
     * <code>uint32 predicted_range = 2;</code>
     * @return The predictedRange.
     */
    int getPredictedRange();

    /**
     * <pre>
     * predicted time to reach the predicted range at the indicated level in minutes (0 to 3600)
     * </pre>
     *
     * <code>uint32 predicted_time = 3;</code>
     * @return The predictedTime.
     */
    int getPredictedTime();
  }
  /**
   * <pre>
   * ***************************************************************************
   * </pre>
   *
   * Protobuf type {@code charging_common.PredictedChargeData}
   */
  public static final class PredictedChargeData extends
      com.google.protobuf.GeneratedMessage implements
      // @@protoc_insertion_point(message_implements:charging_common.PredictedChargeData)
      PredictedChargeDataOrBuilder {
  private static final long serialVersionUID = 0L;
    static {
      com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
        com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
        /* major= */ 4,
        /* minor= */ 28,
        /* patch= */ 2,
        /* suffix= */ "",
        PredictedChargeData.class.getName());
    }
    // Use PredictedChargeData.newBuilder() to construct.
    private PredictedChargeData(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
    }
    private PredictedChargeData() {
    }

    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return charging_common.ChargingCommon.internal_static_charging_common_PredictedChargeData_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return charging_common.ChargingCommon.internal_static_charging_common_PredictedChargeData_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              charging_common.ChargingCommon.PredictedChargeData.class, charging_common.ChargingCommon.PredictedChargeData.Builder.class);
    }

    public static final int BATTERY_LEVEL_FIELD_NUMBER = 1;
    private int batteryLevel_ = 0;
    /**
     * <pre>
     * soc percentage at the indicated level in percentage (0 to 100)
     * </pre>
     *
     * <code>uint32 battery_level = 1;</code>
     * @return The batteryLevel.
     */
    @java.lang.Override
    public int getBatteryLevel() {
      return batteryLevel_;
    }

    public static final int PREDICTED_RANGE_FIELD_NUMBER = 2;
    private int predictedRange_ = 0;
    /**
     * <pre>
     * predicted range at the indicated level in kms (0 to 1000)
     * </pre>
     *
     * <code>uint32 predicted_range = 2;</code>
     * @return The predictedRange.
     */
    @java.lang.Override
    public int getPredictedRange() {
      return predictedRange_;
    }

    public static final int PREDICTED_TIME_FIELD_NUMBER = 3;
    private int predictedTime_ = 0;
    /**
     * <pre>
     * predicted time to reach the predicted range at the indicated level in minutes (0 to 3600)
     * </pre>
     *
     * <code>uint32 predicted_time = 3;</code>
     * @return The predictedTime.
     */
    @java.lang.Override
    public int getPredictedTime() {
      return predictedTime_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (batteryLevel_ != 0) {
        output.writeUInt32(1, batteryLevel_);
      }
      if (predictedRange_ != 0) {
        output.writeUInt32(2, predictedRange_);
      }
      if (predictedTime_ != 0) {
        output.writeUInt32(3, predictedTime_);
      }
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (batteryLevel_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(1, batteryLevel_);
      }
      if (predictedRange_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(2, predictedRange_);
      }
      if (predictedTime_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(3, predictedTime_);
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof charging_common.ChargingCommon.PredictedChargeData)) {
        return super.equals(obj);
      }
      charging_common.ChargingCommon.PredictedChargeData other = (charging_common.ChargingCommon.PredictedChargeData) obj;

      if (getBatteryLevel()
          != other.getBatteryLevel()) return false;
      if (getPredictedRange()
          != other.getPredictedRange()) return false;
      if (getPredictedTime()
          != other.getPredictedTime()) return false;
      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + BATTERY_LEVEL_FIELD_NUMBER;
      hash = (53 * hash) + getBatteryLevel();
      hash = (37 * hash) + PREDICTED_RANGE_FIELD_NUMBER;
      hash = (53 * hash) + getPredictedRange();
      hash = (37 * hash) + PREDICTED_TIME_FIELD_NUMBER;
      hash = (53 * hash) + getPredictedTime();
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static charging_common.ChargingCommon.PredictedChargeData parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static charging_common.ChargingCommon.PredictedChargeData parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static charging_common.ChargingCommon.PredictedChargeData parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static charging_common.ChargingCommon.PredictedChargeData parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static charging_common.ChargingCommon.PredictedChargeData parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static charging_common.ChargingCommon.PredictedChargeData parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static charging_common.ChargingCommon.PredictedChargeData parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input);
    }
    public static charging_common.ChargingCommon.PredictedChargeData parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public static charging_common.ChargingCommon.PredictedChargeData parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseDelimitedWithIOException(PARSER, input);
    }

    public static charging_common.ChargingCommon.PredictedChargeData parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static charging_common.ChargingCommon.PredictedChargeData parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input);
    }
    public static charging_common.ChargingCommon.PredictedChargeData parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(charging_common.ChargingCommon.PredictedChargeData prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     * ***************************************************************************
     * </pre>
     *
     * Protobuf type {@code charging_common.PredictedChargeData}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:charging_common.PredictedChargeData)
        charging_common.ChargingCommon.PredictedChargeDataOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return charging_common.ChargingCommon.internal_static_charging_common_PredictedChargeData_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return charging_common.ChargingCommon.internal_static_charging_common_PredictedChargeData_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                charging_common.ChargingCommon.PredictedChargeData.class, charging_common.ChargingCommon.PredictedChargeData.Builder.class);
      }

      // Construct using charging_common.ChargingCommon.PredictedChargeData.newBuilder()
      private Builder() {

      }

      private Builder(
          com.google.protobuf.GeneratedMessage.BuilderParent parent) {
        super(parent);

      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        bitField0_ = 0;
        batteryLevel_ = 0;
        predictedRange_ = 0;
        predictedTime_ = 0;
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return charging_common.ChargingCommon.internal_static_charging_common_PredictedChargeData_descriptor;
      }

      @java.lang.Override
      public charging_common.ChargingCommon.PredictedChargeData getDefaultInstanceForType() {
        return charging_common.ChargingCommon.PredictedChargeData.getDefaultInstance();
      }

      @java.lang.Override
      public charging_common.ChargingCommon.PredictedChargeData build() {
        charging_common.ChargingCommon.PredictedChargeData result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public charging_common.ChargingCommon.PredictedChargeData buildPartial() {
        charging_common.ChargingCommon.PredictedChargeData result = new charging_common.ChargingCommon.PredictedChargeData(this);
        if (bitField0_ != 0) { buildPartial0(result); }
        onBuilt();
        return result;
      }

      private void buildPartial0(charging_common.ChargingCommon.PredictedChargeData result) {
        int from_bitField0_ = bitField0_;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.batteryLevel_ = batteryLevel_;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          result.predictedRange_ = predictedRange_;
        }
        if (((from_bitField0_ & 0x00000004) != 0)) {
          result.predictedTime_ = predictedTime_;
        }
      }

      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof charging_common.ChargingCommon.PredictedChargeData) {
          return mergeFrom((charging_common.ChargingCommon.PredictedChargeData)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(charging_common.ChargingCommon.PredictedChargeData other) {
        if (other == charging_common.ChargingCommon.PredictedChargeData.getDefaultInstance()) return this;
        if (other.getBatteryLevel() != 0) {
          setBatteryLevel(other.getBatteryLevel());
        }
        if (other.getPredictedRange() != 0) {
          setPredictedRange(other.getPredictedRange());
        }
        if (other.getPredictedTime() != 0) {
          setPredictedTime(other.getPredictedTime());
        }
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 8: {
                batteryLevel_ = input.readUInt32();
                bitField0_ |= 0x00000001;
                break;
              } // case 8
              case 16: {
                predictedRange_ = input.readUInt32();
                bitField0_ |= 0x00000002;
                break;
              } // case 16
              case 24: {
                predictedTime_ = input.readUInt32();
                bitField0_ |= 0x00000004;
                break;
              } // case 24
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }
      private int bitField0_;

      private int batteryLevel_ ;
      /**
       * <pre>
       * soc percentage at the indicated level in percentage (0 to 100)
       * </pre>
       *
       * <code>uint32 battery_level = 1;</code>
       * @return The batteryLevel.
       */
      @java.lang.Override
      public int getBatteryLevel() {
        return batteryLevel_;
      }
      /**
       * <pre>
       * soc percentage at the indicated level in percentage (0 to 100)
       * </pre>
       *
       * <code>uint32 battery_level = 1;</code>
       * @param value The batteryLevel to set.
       * @return This builder for chaining.
       */
      public Builder setBatteryLevel(int value) {

        batteryLevel_ = value;
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * soc percentage at the indicated level in percentage (0 to 100)
       * </pre>
       *
       * <code>uint32 battery_level = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearBatteryLevel() {
        bitField0_ = (bitField0_ & ~0x00000001);
        batteryLevel_ = 0;
        onChanged();
        return this;
      }

      private int predictedRange_ ;
      /**
       * <pre>
       * predicted range at the indicated level in kms (0 to 1000)
       * </pre>
       *
       * <code>uint32 predicted_range = 2;</code>
       * @return The predictedRange.
       */
      @java.lang.Override
      public int getPredictedRange() {
        return predictedRange_;
      }
      /**
       * <pre>
       * predicted range at the indicated level in kms (0 to 1000)
       * </pre>
       *
       * <code>uint32 predicted_range = 2;</code>
       * @param value The predictedRange to set.
       * @return This builder for chaining.
       */
      public Builder setPredictedRange(int value) {

        predictedRange_ = value;
        bitField0_ |= 0x00000002;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * predicted range at the indicated level in kms (0 to 1000)
       * </pre>
       *
       * <code>uint32 predicted_range = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearPredictedRange() {
        bitField0_ = (bitField0_ & ~0x00000002);
        predictedRange_ = 0;
        onChanged();
        return this;
      }

      private int predictedTime_ ;
      /**
       * <pre>
       * predicted time to reach the predicted range at the indicated level in minutes (0 to 3600)
       * </pre>
       *
       * <code>uint32 predicted_time = 3;</code>
       * @return The predictedTime.
       */
      @java.lang.Override
      public int getPredictedTime() {
        return predictedTime_;
      }
      /**
       * <pre>
       * predicted time to reach the predicted range at the indicated level in minutes (0 to 3600)
       * </pre>
       *
       * <code>uint32 predicted_time = 3;</code>
       * @param value The predictedTime to set.
       * @return This builder for chaining.
       */
      public Builder setPredictedTime(int value) {

        predictedTime_ = value;
        bitField0_ |= 0x00000004;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * predicted time to reach the predicted range at the indicated level in minutes (0 to 3600)
       * </pre>
       *
       * <code>uint32 predicted_time = 3;</code>
       * @return This builder for chaining.
       */
      public Builder clearPredictedTime() {
        bitField0_ = (bitField0_ & ~0x00000004);
        predictedTime_ = 0;
        onChanged();
        return this;
      }

      // @@protoc_insertion_point(builder_scope:charging_common.PredictedChargeData)
    }

    // @@protoc_insertion_point(class_scope:charging_common.PredictedChargeData)
    private static final charging_common.ChargingCommon.PredictedChargeData DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new charging_common.ChargingCommon.PredictedChargeData();
    }

    public static charging_common.ChargingCommon.PredictedChargeData getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<PredictedChargeData>
        PARSER = new com.google.protobuf.AbstractParser<PredictedChargeData>() {
      @java.lang.Override
      public PredictedChargeData parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<PredictedChargeData> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<PredictedChargeData> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public charging_common.ChargingCommon.PredictedChargeData getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface ChargingCurveDataOrBuilder extends
      // @@protoc_insertion_point(interface_extends:charging_common.ChargingCurveData)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     * state of charge as per the curve data
     * state of charge shall be sent in percentage (0 to 100%)
     * </pre>
     *
     * <code>optional uint32 state_of_charge = 1;</code>
     * @return Whether the stateOfCharge field is set.
     */
    boolean hasStateOfCharge();
    /**
     * <pre>
     * state of charge as per the curve data
     * state of charge shall be sent in percentage (0 to 100%)
     * </pre>
     *
     * <code>optional uint32 state_of_charge = 1;</code>
     * @return The stateOfCharge.
     */
    int getStateOfCharge();

    /**
     * <pre>
     * max power shall be sent in watts
     * maximum power  as per the curve data
     * </pre>
     *
     * <code>optional uint32 max_power = 2;</code>
     * @return Whether the maxPower field is set.
     */
    boolean hasMaxPower();
    /**
     * <pre>
     * max power shall be sent in watts
     * maximum power  as per the curve data
     * </pre>
     *
     * <code>optional uint32 max_power = 2;</code>
     * @return The maxPower.
     */
    int getMaxPower();
  }
  /**
   * Protobuf type {@code charging_common.ChargingCurveData}
   */
  public static final class ChargingCurveData extends
      com.google.protobuf.GeneratedMessage implements
      // @@protoc_insertion_point(message_implements:charging_common.ChargingCurveData)
      ChargingCurveDataOrBuilder {
  private static final long serialVersionUID = 0L;
    static {
      com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
        com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
        /* major= */ 4,
        /* minor= */ 28,
        /* patch= */ 2,
        /* suffix= */ "",
        ChargingCurveData.class.getName());
    }
    // Use ChargingCurveData.newBuilder() to construct.
    private ChargingCurveData(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
    }
    private ChargingCurveData() {
    }

    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return charging_common.ChargingCommon.internal_static_charging_common_ChargingCurveData_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return charging_common.ChargingCommon.internal_static_charging_common_ChargingCurveData_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              charging_common.ChargingCommon.ChargingCurveData.class, charging_common.ChargingCommon.ChargingCurveData.Builder.class);
    }

    private int bitField0_;
    public static final int STATE_OF_CHARGE_FIELD_NUMBER = 1;
    private int stateOfCharge_ = 0;
    /**
     * <pre>
     * state of charge as per the curve data
     * state of charge shall be sent in percentage (0 to 100%)
     * </pre>
     *
     * <code>optional uint32 state_of_charge = 1;</code>
     * @return Whether the stateOfCharge field is set.
     */
    @java.lang.Override
    public boolean hasStateOfCharge() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <pre>
     * state of charge as per the curve data
     * state of charge shall be sent in percentage (0 to 100%)
     * </pre>
     *
     * <code>optional uint32 state_of_charge = 1;</code>
     * @return The stateOfCharge.
     */
    @java.lang.Override
    public int getStateOfCharge() {
      return stateOfCharge_;
    }

    public static final int MAX_POWER_FIELD_NUMBER = 2;
    private int maxPower_ = 0;
    /**
     * <pre>
     * max power shall be sent in watts
     * maximum power  as per the curve data
     * </pre>
     *
     * <code>optional uint32 max_power = 2;</code>
     * @return Whether the maxPower field is set.
     */
    @java.lang.Override
    public boolean hasMaxPower() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <pre>
     * max power shall be sent in watts
     * maximum power  as per the curve data
     * </pre>
     *
     * <code>optional uint32 max_power = 2;</code>
     * @return The maxPower.
     */
    @java.lang.Override
    public int getMaxPower() {
      return maxPower_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeUInt32(1, stateOfCharge_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        output.writeUInt32(2, maxPower_);
      }
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(1, stateOfCharge_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(2, maxPower_);
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof charging_common.ChargingCommon.ChargingCurveData)) {
        return super.equals(obj);
      }
      charging_common.ChargingCommon.ChargingCurveData other = (charging_common.ChargingCommon.ChargingCurveData) obj;

      if (hasStateOfCharge() != other.hasStateOfCharge()) return false;
      if (hasStateOfCharge()) {
        if (getStateOfCharge()
            != other.getStateOfCharge()) return false;
      }
      if (hasMaxPower() != other.hasMaxPower()) return false;
      if (hasMaxPower()) {
        if (getMaxPower()
            != other.getMaxPower()) return false;
      }
      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasStateOfCharge()) {
        hash = (37 * hash) + STATE_OF_CHARGE_FIELD_NUMBER;
        hash = (53 * hash) + getStateOfCharge();
      }
      if (hasMaxPower()) {
        hash = (37 * hash) + MAX_POWER_FIELD_NUMBER;
        hash = (53 * hash) + getMaxPower();
      }
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static charging_common.ChargingCommon.ChargingCurveData parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static charging_common.ChargingCommon.ChargingCurveData parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static charging_common.ChargingCommon.ChargingCurveData parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static charging_common.ChargingCommon.ChargingCurveData parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static charging_common.ChargingCommon.ChargingCurveData parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static charging_common.ChargingCommon.ChargingCurveData parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static charging_common.ChargingCommon.ChargingCurveData parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input);
    }
    public static charging_common.ChargingCommon.ChargingCurveData parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public static charging_common.ChargingCommon.ChargingCurveData parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseDelimitedWithIOException(PARSER, input);
    }

    public static charging_common.ChargingCommon.ChargingCurveData parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static charging_common.ChargingCommon.ChargingCurveData parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input);
    }
    public static charging_common.ChargingCommon.ChargingCurveData parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(charging_common.ChargingCommon.ChargingCurveData prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code charging_common.ChargingCurveData}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:charging_common.ChargingCurveData)
        charging_common.ChargingCommon.ChargingCurveDataOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return charging_common.ChargingCommon.internal_static_charging_common_ChargingCurveData_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return charging_common.ChargingCommon.internal_static_charging_common_ChargingCurveData_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                charging_common.ChargingCommon.ChargingCurveData.class, charging_common.ChargingCommon.ChargingCurveData.Builder.class);
      }

      // Construct using charging_common.ChargingCommon.ChargingCurveData.newBuilder()
      private Builder() {

      }

      private Builder(
          com.google.protobuf.GeneratedMessage.BuilderParent parent) {
        super(parent);

      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        bitField0_ = 0;
        stateOfCharge_ = 0;
        maxPower_ = 0;
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return charging_common.ChargingCommon.internal_static_charging_common_ChargingCurveData_descriptor;
      }

      @java.lang.Override
      public charging_common.ChargingCommon.ChargingCurveData getDefaultInstanceForType() {
        return charging_common.ChargingCommon.ChargingCurveData.getDefaultInstance();
      }

      @java.lang.Override
      public charging_common.ChargingCommon.ChargingCurveData build() {
        charging_common.ChargingCommon.ChargingCurveData result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public charging_common.ChargingCommon.ChargingCurveData buildPartial() {
        charging_common.ChargingCommon.ChargingCurveData result = new charging_common.ChargingCommon.ChargingCurveData(this);
        if (bitField0_ != 0) { buildPartial0(result); }
        onBuilt();
        return result;
      }

      private void buildPartial0(charging_common.ChargingCommon.ChargingCurveData result) {
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.stateOfCharge_ = stateOfCharge_;
          to_bitField0_ |= 0x00000001;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          result.maxPower_ = maxPower_;
          to_bitField0_ |= 0x00000002;
        }
        result.bitField0_ |= to_bitField0_;
      }

      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof charging_common.ChargingCommon.ChargingCurveData) {
          return mergeFrom((charging_common.ChargingCommon.ChargingCurveData)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(charging_common.ChargingCommon.ChargingCurveData other) {
        if (other == charging_common.ChargingCommon.ChargingCurveData.getDefaultInstance()) return this;
        if (other.hasStateOfCharge()) {
          setStateOfCharge(other.getStateOfCharge());
        }
        if (other.hasMaxPower()) {
          setMaxPower(other.getMaxPower());
        }
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 8: {
                stateOfCharge_ = input.readUInt32();
                bitField0_ |= 0x00000001;
                break;
              } // case 8
              case 16: {
                maxPower_ = input.readUInt32();
                bitField0_ |= 0x00000002;
                break;
              } // case 16
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }
      private int bitField0_;

      private int stateOfCharge_ ;
      /**
       * <pre>
       * state of charge as per the curve data
       * state of charge shall be sent in percentage (0 to 100%)
       * </pre>
       *
       * <code>optional uint32 state_of_charge = 1;</code>
       * @return Whether the stateOfCharge field is set.
       */
      @java.lang.Override
      public boolean hasStateOfCharge() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <pre>
       * state of charge as per the curve data
       * state of charge shall be sent in percentage (0 to 100%)
       * </pre>
       *
       * <code>optional uint32 state_of_charge = 1;</code>
       * @return The stateOfCharge.
       */
      @java.lang.Override
      public int getStateOfCharge() {
        return stateOfCharge_;
      }
      /**
       * <pre>
       * state of charge as per the curve data
       * state of charge shall be sent in percentage (0 to 100%)
       * </pre>
       *
       * <code>optional uint32 state_of_charge = 1;</code>
       * @param value The stateOfCharge to set.
       * @return This builder for chaining.
       */
      public Builder setStateOfCharge(int value) {

        stateOfCharge_ = value;
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * state of charge as per the curve data
       * state of charge shall be sent in percentage (0 to 100%)
       * </pre>
       *
       * <code>optional uint32 state_of_charge = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearStateOfCharge() {
        bitField0_ = (bitField0_ & ~0x00000001);
        stateOfCharge_ = 0;
        onChanged();
        return this;
      }

      private int maxPower_ ;
      /**
       * <pre>
       * max power shall be sent in watts
       * maximum power  as per the curve data
       * </pre>
       *
       * <code>optional uint32 max_power = 2;</code>
       * @return Whether the maxPower field is set.
       */
      @java.lang.Override
      public boolean hasMaxPower() {
        return ((bitField0_ & 0x00000002) != 0);
      }
      /**
       * <pre>
       * max power shall be sent in watts
       * maximum power  as per the curve data
       * </pre>
       *
       * <code>optional uint32 max_power = 2;</code>
       * @return The maxPower.
       */
      @java.lang.Override
      public int getMaxPower() {
        return maxPower_;
      }
      /**
       * <pre>
       * max power shall be sent in watts
       * maximum power  as per the curve data
       * </pre>
       *
       * <code>optional uint32 max_power = 2;</code>
       * @param value The maxPower to set.
       * @return This builder for chaining.
       */
      public Builder setMaxPower(int value) {

        maxPower_ = value;
        bitField0_ |= 0x00000002;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * max power shall be sent in watts
       * maximum power  as per the curve data
       * </pre>
       *
       * <code>optional uint32 max_power = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearMaxPower() {
        bitField0_ = (bitField0_ & ~0x00000002);
        maxPower_ = 0;
        onChanged();
        return this;
      }

      // @@protoc_insertion_point(builder_scope:charging_common.ChargingCurveData)
    }

    // @@protoc_insertion_point(class_scope:charging_common.ChargingCurveData)
    private static final charging_common.ChargingCommon.ChargingCurveData DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new charging_common.ChargingCommon.ChargingCurveData();
    }

    public static charging_common.ChargingCommon.ChargingCurveData getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<ChargingCurveData>
        PARSER = new com.google.protobuf.AbstractParser<ChargingCurveData>() {
      @java.lang.Override
      public ChargingCurveData parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<ChargingCurveData> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<ChargingCurveData> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public charging_common.ChargingCommon.ChargingCurveData getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_charging_common_PredictedChargeData_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_charging_common_PredictedChargeData_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_charging_common_ChargingCurveData_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_charging_common_ChargingCurveData_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n\025charging_common.proto\022\017charging_common" +
      "\"]\n\023PredictedChargeData\022\025\n\rbattery_level" +
      "\030\001 \001(\r\022\027\n\017predicted_range\030\002 \001(\r\022\026\n\016predi" +
      "cted_time\030\003 \001(\r\"k\n\021ChargingCurveData\022\034\n\017" +
      "state_of_charge\030\001 \001(\rH\000\210\001\001\022\026\n\tmax_power\030" +
      "\002 \001(\rH\001\210\001\001B\022\n\020_state_of_chargeB\014\n\n_max_p" +
      "ower*\236\003\n\nEnumStatus\022\033\n\027ENUM_STATUS_UNSPE" +
      "CIFIED\020\000\022\022\n\016ENUM_STATUS_OK\020\001\022\035\n\031ENUM_STA" +
      "TUS_DATA_DEGRADED\020\002\022\037\n\033ENUM_STATUS_DATA_" +
      "UNRELIABLE\020\003\022 \n\034ENUM_STATUS_DATA_UNAVAIL" +
      "ABLE\020\004\022+\n\'ENUM_STATUS_ERROR_INVALID_SERV" +
      "ICE_STATE\020\005\022+\n\'ENUM_STATUS_ERROR_INVALID" +
      "_VEHICLE_STATE\020\006\022/\n+ENUM_STATUS_ERROR_IN" +
      "VALID_CAR_CONFIGURATION\020\007\022)\n%ENUM_STATUS" +
      "_ERROR_MISSING_INPUT_FIELD\020\010\022)\n%ENUM_STA" +
      "TUS_ERROR_INVALID_INPUT_FIELD\020\t\022\026\n\022ENUM_" +
      "STATUS_NOT_OK\020\n\"\004\010\013\020d*\306\001\n\032EnumChargeCont" +
      "rolOperation\022-\n)ENUM_CHARGE_CONTROL_OPER" +
      "ATION_UNSPECIFIED\020\000\022\'\n#ENUM_CHARGE_CONTR" +
      "OL_OPERATION_START\020\001\022&\n\"ENUM_CHARGE_CONT" +
      "ROL_OPERATION_STOP\020\002\022(\n$ENUM_CHARGE_CONT" +
      "ROL_OPERATION_REVERT\020\003*v\n\021EnumChargeCont" +
      "ext\022#\n\037ENUM_CHARGE_CONTEXT_UNSPECIFIED\020\000" +
      "\022\034\n\030ENUM_CHARGE_CONTEXT_USER\020\001\022\036\n\032ENUM_C" +
      "HARGE_CONTEXT_SYSTEM\020\002*\363\004\n\017EnumChargeSta" +
      "te\022!\n\035ENUM_CHARGE_STATE_UNSPECIFIED\020\000\022\035\n" +
      "\031ENUM_CHARGE_STATE_DEFAULT\020\001\022\"\n\036ENUM_CHA" +
      "RGE_STATE_INITIALIZING\020\002\022\'\n#ENUM_CHARGE_" +
      "STATE_WAITING_TO_CHARGE\020\003\0220\n,ENUM_CHARGE" +
      "_STATE_WAITING_FOR_CHARGE_STATION\020\004\022(\n$E" +
      "NUM_CHARGE_STATE_CHARGE_IN_PROGRESS\020\005\022$\n" +
      " ENUM_CHARGE_STATE_CHARGE_STOPPED\020\006\022%\n!E" +
      "NUM_CHARGE_STATE_CHARGE_COMPLETE\020\007\022\"\n\036EN" +
      "UM_CHARGE_STATE_CHARGE_ERROR\020\010\022*\n&ENUM_C" +
      "HARGE_STATE_WAITING_TO_DISCHARGE\020\t\022+\n\'EN" +
      "UM_CHARGE_STATE_DISCHARGE_IN_PROGRESS\020\n\022" +
      "(\n$ENUM_CHARGE_STATE_DISCHARGE_COMPLETE\020" +
      "\013\022%\n!ENUM_CHARGE_STATE_DISCHARGE_ERROR\020\014" +
      "\022/\n+ENUM_CHARGE_STATE_FORCED_CHARGE_IN_P" +
      "ROGRESS\020\r\022)\n%ENUM_CHARGE_STATE_WAITING_F" +
      "OR_PAYMENT\020\016*\264\004\n\023EnumChargeErrorMode\022&\n\"" +
      "ENUM_CHARGE_ERROR_MODE_UNSPECIFIED\020\000\022#\n\037" +
      "ENUM_CHARGE_ERROR_MODE_NO_ERROR\020\001\022\'\n#ENU" +
      "M_CHARGE_ERROR_MODE_SYSTEM_ERROR\020\002\022/\n+EN" +
      "UM_CHARGE_ERROR_MODE_CHARGE_STATION_ERRO" +
      "R\020\003\022*\n&ENUM_CHARGE_ERROR_MODE_DISCHARGE_" +
      "ERROR\020\004\022,\n(ENUM_CHARGE_ERROR_MODE_PLUG_L" +
      "OCK_FAILURE\020\005\022.\n*ENUM_CHARGE_ERROR_MODE_" +
      "PLUG_UNLOCK_FAILURE\020\006\0220\n,ENUM_CHARGE_ERR" +
      "OR_MODE_MANUAL_PAYMENT_FAILED\020\007\0222\n.ENUM_" +
      "CHARGE_ERROR_MODE_AUTOMATE_PAYMENT_FAILE" +
      "D\020\010\022/\n+ENUM_CHARGE_ERROR_MODE_BATTERY_TE" +
      "MP_WARNING\020\t\022\'\n#ENUM_CHARGE_ERROR_MODE_G" +
      "ENERAL_INFO\020\n\022,\n(ENUM_CHARGE_ERROR_MODE_" +
      "CHARGE_DOOR_ERROR\020\013*\302\001\n\026EnumChargingInle" +
      "tState\022)\n%ENUM_CHARGING_INLET_STATE_UNSP" +
      "ECIFIED\020\000\022\'\n#ENUM_CHARGING_INLET_STATE_U" +
      "NPLUGGED\020\001\022%\n!ENUM_CHARGING_INLET_STATE_" +
      "PLUGGED\020\002\022-\n)ENUM_CHARGING_INLET_STATE_A" +
      "DAPTOR_PLUGGED\020\003*\355\001\n\016EnumChargeType\022 \n\034E" +
      "NUM_CHARGE_TYPE_UNSPECIFIED\020\000\022\036\n\032ENUM_CH" +
      "ARGE_TYPE_IMMEDIATE\020\001\022#\n\037ENUM_CHARGE_TYP" +
      "E_FIXED_SCHEDULE\020\002\022\"\n\036ENUM_CHARGE_TYPE_S" +
      "CHEDULE_PLUS\020\003\022#\n\037ENUM_CHARGE_TYPE_SMART" +
      "_SCHEDULE\020\004\022+\n\'ENUM_CHARGE_TYPE_BIDIRECT" +
      "IONAL_SCHEDULE\020\005*\331\001\n\022EnumChargingMethod\022" +
      "$\n ENUM_CHARGING_METHOD_UNSPECIFIED\020\000\022%\n" +
      "!ENUM_CHARGING_METHOD_NOT_CHARGING\020\001\022$\n " +
      "ENUM_CHARGING_METHOD_AC_CHARGING\020\002\022$\n EN" +
      "UM_CHARGING_METHOD_DC_CHARGING\020\003\022*\n&ENUM" +
      "_CHARGING_METHOD_WIRELESS_CHARGING\020\004*i\n\016" +
      "EnumChargeDoor\022 \n\034ENUM_CHARGE_DOOR_UNSPE" +
      "CIFIED\020\000\022\031\n\025ENUM_CHARGE_DOOR_LEFT\020\001\022\032\n\026E" +
      "NUM_CHARGE_DOOR_RIGHT\020\002*\342\001\n\027EnumChargeDo" +
      "orOperation\022*\n&ENUM_CHARGE_DOOR_OPERATIO" +
      "N_UNSPECIFIED\020\000\022#\n\037ENUM_CHARGE_DOOR_OPER" +
      "ATION_OPEN\020\001\022$\n ENUM_CHARGE_DOOR_OPERATI" +
      "ON_CLOSE\020\002\022*\n&ENUM_CHARGE_DOOR_OPERATION" +
      "_IN_PROGRESS\020\003\022$\n ENUM_CHARGE_DOOR_OPERA" +
      "TION_ERROR\020\004*\274\001\n\030EnumChargeCableOperatio" +
      "n\022+\n\'ENUM_CHARGE_CABLE_OPERATION_UNSPECI" +
      "FIED\020\000\022&\n\"ENUM_CHARGE_CABLE_OPERATION_UN" +
      "LOCK\020\001\022$\n ENUM_CHARGE_CABLE_OPERATION_LO" +
      "CK\020\002\022%\n!ENUM_CHARGE_CABLE_OPERATION_ERRO" +
      "R\020\003*\212\001\n\024EnumPnCPaymentChoice\022\'\n#ENUM_PNC" +
      "_PAYMENT_CHOICE_UNSPECIFIED\020\000\022$\n ENUM_PN" +
      "C_PAYMENT_CHOICE_DISABLED\020\001\022#\n\037ENUM_PNC_" +
      "PAYMENT_CHOICE_ENABLED\020\002*\212\001\n\024EnumPnCPaym" +
      "entMethod\022\'\n#ENUM_PNC_PAYMENT_METHOD_UNS" +
      "PECIFIED\020\000\022%\n!ENUM_PNC_PAYMENT_METHOD_AU" +
      "TOMATIC\020\001\022\"\n\036ENUM_PNC_PAYMENT_METHOD_MAN" +
      "UAL\020\002*\205\001\n\rEnumPnCStatus\022\037\n\033ENUM_PNC_STAT" +
      "US_UNSPECIFIED\020\000\022\032\n\026ENUM_PNC_STATUS_ACTI" +
      "VE\020\001\022\034\n\030ENUM_PNC_STATUS_INACTIVE\020\002\022\031\n\025EN" +
      "UM_PNC_STATUS_ERROR\020\003*\237\001\n\032EnumPnCFeature" +
      "Availability\022 \n\034ENUM_PNC_FEATURE_UNSPECI" +
      "FIED\020\000\022\"\n\036ENUM_PNC_FEATURE_NOT_SUPPORTED" +
      "\020\001\022\035\n\031ENUM_PNC_FEATURE_DISABLED\020\002\022\034\n\030ENU" +
      "M_PNC_FEATURE_ENABLED\020\003*v\n\017EnumPnCVINSha" +
      "re\022\"\n\036ENUM_PNC_VIN_SHARE_UNSPECIFIED\020\000\022\037" +
      "\n\033ENUM_PNC_VIN_SHARE_DISABLED\020\001\022\036\n\032ENUM_" +
      "PNC_VIN_SHARE_ENABLED\020\002*\342\002\n\037EnumPnCV2GRo" +
      "otCertificateStatus\022-\n)ENUM_PNC_V2G_ROOT" +
      "_CERTIFICATE_UNSPECIFIED\020\000\022+\n\'ENUM_PNC_V" +
      "2G_ROOT_CERTIFICATE_CORRUPTED\020\001\022/\n+ENUM_" +
      "PNC_V2G_ROOT_CERTIFICATE_NOT_INSTALLED\020\002" +
      "\022)\n%ENUM_PNC_V2G_ROOT_CERTIFICATE_EXPIRE" +
      "D\020\003\022)\n%ENUM_PNC_V2G_ROOT_CERTIFICATE_MIS" +
      "SING\020\004\022/\n+ENUM_PNC_V2G_ROOT_CERTIFICATE_" +
      "EXPIRING_SOON\020\005\022+\n\'ENUM_PNC_V2G_ROOT_CER" +
      "TIFICATE_INSTALLED\020\006*\341\t\n\022EnumChrgTrouble" +
      "Sht\022$\n ENUM_CHRG_TROUBLESHT_UNSPECIFIED\020" +
      "\000\022&\n\"ENUM_CHRG_TROUBLESHT_CLEAR_MESSAGE\020" +
      "\001\022)\n%ENUM_CHRG_TROUBLESHT_PWR_SOURCE_FAU" +
      "LT\020\002\022.\n*ENUM_CHRG_TROUBLESHT_UNSUITABLE_" +
      "PWR_SOURCE\020\003\022-\n)ENUM_CHRG_TROUBLESHT_OVE" +
      "RCURRENT_DETECTED\020\004\0220\n,ENUM_CHRG_TROUBLE" +
      "SHT_UNABLE_TO_LOCK_PIN_DUAL\020\005\0222\n.ENUM_CH" +
      "RG_TROUBLESHT_UNABLE_TO_LOCK_PIN_SINGLE\020" +
      "\006\022-\n)ENUM_CHRG_TROUBLESHT_UNABLE_TO_UNLO" +
      "CK_PIN\020\007\022(\n$ENUM_CHRG_TROUBLESHT_FLAP_ST" +
      "UCK_OPEN\020\010\0220\n,ENUM_CHRG_TROUBLESHT_FLAP_" +
      "STUCK_OPEN_DRIVING\020\t\022*\n&ENUM_CHRG_TROUBL" +
      "ESHT_FLAP_STUCK_CLOSED\020\n\022)\n%ENUM_CHRG_TR" +
      "OUBLESHT_BOTH_INLETS_USED\020\013\022\'\n#ENUM_CHRG" +
      "_TROUBLESHT_NO_AC_CHARGING\020\014\022\'\n#ENUM_CHR" +
      "G_TROUBLESHT_NO_DC_CHARGING\020\r\022/\n+ENUM_CH" +
      "RG_TROUBLESHT_ALL_CHARGING_PREVENTED\020\016\022-" +
      "\n)ENUM_CHRG_TROUBLESHT_CHARGING_INLET_IS" +
      "SUE\020\017\022 \n\034ENUM_CHRG_TROUBLESHT_DC_800V\020\020\022" +
      "7\n3ENUM_CHRG_TROUBLESHT_PWR_SOURCE_DECRE" +
      "ASE_CHARG_RATE\020\021\022&\n\"ENUM_CHRG_TROUBLESHT" +
      "_LOW_BATT_TEMP\020\022\022/\n+ENUM_CHRG_TROUBLESHT" +
      "_CRITICAL_LOW_BATT_TEMP\020\023\022\'\n#ENUM_CHRG_T" +
      "ROUBLESHT_HIGH_BATT_TEMP\020\024\022-\n)ENUM_CHRG_" +
      "TROUBLESHT_USER_SELECT_AC_LIMIT\020\025\022)\n%ENU" +
      "M_CHRG_TROUBLESHT_SLOW_AC_CHARGING\020\026\022)\n%" +
      "ENUM_CHRG_TROUBLESHT_NO_CONTRACT_CERT\020\027\022" +
      "1\n-ENUM_CHRG_TROUBLESHT_CONTRACT_CERT_EX" +
      "P_CANCEL\020\030\022(\n$ENUM_CHRG_TROUBLESHT_EVSE_" +
      "CERT_ERROR\020\033\022.\n*ENUM_CHRG_TROUBLESHT_GEN" +
      "ERIC_PNC_V2G_ERROR\020\034\"\004\010\031\020\031\"\004\010\032\020\032*\330\001\n\034Enu" +
      "mPnCContractCertOperation\0220\n,ENUM_PNC_CO" +
      "NTRACT_CERT_OPERATION_UNSPECIFIED\020\000\022,\n(E" +
      "NUM_PNC_CONTRACT_CERT_OPERATION_INSTALL\020" +
      "\001\022+\n\'ENUM_PNC_CONTRACT_CERT_OPERATION_UP" +
      "DATE\020\002\022+\n\'ENUM_PNC_CONTRACT_CERT_OPERATI" +
      "ON_DELETE\020\003*\213\004\n\025EnumPnCContractStatus\022(\n" +
      "$ENUM_PNC_CONTRACT_STATUS_UNSPECIFIED\020\000\022" +
      ".\n*ENUM_PNC_CONTRACT_NO_CERTIFICATE_INST" +
      "ALLED\020\001\022\'\n#ENUM_PNC_CONTRACT_CERTIFICATE" +
      "_VALID\020\002\022)\n%ENUM_PNC_CONTRACT_CERTIFICAT" +
      "E_EXPIRED\020\003\022/\n+ENUM_PNC_CONTRACT_CERTIFI" +
      "CATE_EXPIRING_SOON\020\004\022(\n$ENUM_PNC_CONTRAC" +
      "T_REQUEST_PROCESSING\020\005\022)\n%ENUM_PNC_CONTR" +
      "ACT_CERTIFICATE_INVALID\020\006\0220\n,ENUM_PNC_CE" +
      "RTIFICATE_CORRUPTED_WHILE_INSTALL\020\007\022/\n+E" +
      "NUM_PNC_CERTIFICATE_CORRUPTED_WHILE_DELE" +
      "TE\020\010\022/\n+ENUM_PNC_CONTRACT_REQUEST_PROCES" +
      "SING_FAILED\020\t\022*\n&ENUM_PNC_CONTRACT_REQUE" +
      "ST_PROCESSED_OK\020\n*\215\002\n\016EnumHVPOStatus\022 \n\034" +
      "ENUM_HVPO_STATUS_UNSPECIFIED\020\000\022)\n%ENUM_H" +
      "VPO_STATUS_NO_DISCHARGE_REQUEST\020\001\022*\n&ENU" +
      "M_HVPO_STATUS_DISCHARGE_IN_PROGRESS\020\002\0220\n" +
      ",ENUM_HVPO_STATUS_DISCHARGE_SOC_LIMIT_RE" +
      "ACHED\020\003\022*\n&ENUM_HVPO_STATUS_DISCHARGE_UN" +
      "AVAILABLE\020\004\022$\n ENUM_HVPO_STATUS_DISCHARG" +
      "E_ERROR\020\005*\227\001\n\031EnumChargeCableAutoUnlock\022" +
      "-\n)ENUM_CHARGE_CABLE_AUTO_UNLOCK_UNSPECI" +
      "FIED\020\000\022$\n ENUM_CHARGE_CABLE_AUTO_UNLOCK_" +
      "ON\020\001\022%\n!ENUM_CHARGE_CABLE_AUTO_UNLOCK_OF" +
      "F\020\002*\240\001\n\035EnumChargeCableApproachUnlock\022*\n" +
      "&ENUM_CHARGE_CABLE_APPROACH_UNSPECIFIED\020" +
      "\000\022(\n$ENUM_CHARGE_CABLE_APPROACH_UNLOCK_O" +
      "N\020\001\022)\n%ENUM_CHARGE_CABLE_APPROACH_UNLOCK" +
      "_OFF\020\002*\220\001\n\030EnumChargeLightPermanent\022+\n\'E" +
      "NUM_CHARGE_LIGHT_PERMANENT_UNSPECIFIED\020\000" +
      "\022\"\n\036ENUM_CHARGE_LIGHT_PERMANENT_ON\020\001\022#\n\037" +
      "ENUM_CHARGE_LIGHT_PERMANENT_OFF\020\002*\217\001\n\027En" +
      "umChargeDoorAutoClose\022+\n\'ENUM_CHARGE_DOO" +
      "R_AUTO_CLOSE_UNSPECIFIED\020\000\022\"\n\036ENUM_CHARG" +
      "E_DOOR_AUTO_CLOSE_ON\020\001\022#\n\037ENUM_CHARGE_DO" +
      "OR_AUTO_CLOSE_OFF\020\002*q\n\020EnumBatteryLevel\022" +
      "\"\n\036ENUM_BATTERY_LEVEL_UNSPECIFIED\020\000\022\032\n\026E" +
      "NUM_BATTERY_LEVEL_LOW\020\001\022\035\n\031ENUM_BATTERY_" +
      "LEVEL_NORMAL\020\002*\256(\n\020EnumPnCErrorCode\022#\n\037E" +
      "NUM_PNC_ERROR_CODE_UNSPECIFIED\020\000\022 \n\034ENUM" +
      "_PNC_ERROR_CODE_NO_ERROR\020\001\0227\n3ENUM_PNC_E" +
      "RROR_CODE_FAILED_NO_CERTIFICATE_AVAILABL" +
      "E\020\002\0220\n,ENUM_PNC_ERROR_CODE_FAILED_CONTRA" +
      "CT_CANCELED\020\003\0222\n.ENUM_PNC_ERROR_CODE_FAI" +
      "LED_CERTIFICATE_REVOKED\020\004\0222\n.ENUM_PNC_ER" +
      "ROR_CODE_FAILED_CERTIFICATE_EXPIRED\020\005\022/\n" +
      "+ENUM_PNC_ERROR_CODE_FAILED_CERT_CHAIN_E" +
      "RROR\020\006\0228\n4ENUM_PNC_ERROR_CODE_FAILED_PAY" +
      "MENT_SELECTION_INVALID\020\007\022.\n*ENUM_PNC_ERR" +
      "OR_CODE_FAILED_SIGNATURE_ERROR\020\010\0220\n,ENUM" +
      "_PNC_ERROR_CODE_FAILED_CHALLENGE_INVALID" +
      "\020\t\0227\n3ENUM_PNC_ERROR_CODE_FAILED_TARIFF_" +
      "SELECTION_INVALID\020\n\0227\n3ENUM_PNC_ERROR_CO" +
      "DE_FAILED_CHARGING_PROFILE_INVALID\020\013\022.\n*" +
      "ENUM_PNC_ERROR_CODE_FAILED_UNKNOWN_SESSI" +
      "ON\020\014\0228\n4ENUM_PNC_ERROR_CODE_FAILED_SERVI" +
      "CE_SELECTION_INVALID\020\r\0221\n-ENUM_PNC_ERROR" +
      "_CODE_FAILED_SERVICE_ID_INVALID\020\016\0229\n5ENU" +
      "M_PNC_ERROR_CODE_FAILED_NO_CHARGE_SERVIC" +
      "E_SELECTED\020\017\0229\n5ENUM_PNC_ERROR_CODE_FAIL" +
      "ED_POWER_DELIVERY_NOT_APPLIED\020\020\022,\n(ENUM_" +
      "PNC_ERROR_CODE_FAILED_CONTACT_ERROR\020\021\022&\n" +
      "\"ENUM_PNC_ERROR_CODE_SEQUENCE_ERROR\020\022\022B\n" +
      ">ENUM_PNC_ERROR_CODE_FAILED_CERTIFICATE_" +
      "NOT_ALLOWED_AT_THISEVSE\020\023\0229\n5ENUM_PNC_ER" +
      "ROR_CODE_FAILED_WRONG_ENERGY_TRANSFER_MO" +
      "DE\020\024\0225\n1ENUM_PNC_ERROR_CODE_FAILED_WRONG" +
      "_CHARGE_PARAMETER\020\025\022;\n7ENUM_PNC_ERROR_CO" +
      "DE_FAILED_METERING_SIGNATURE_NOT_VALID\020\026" +
      "\022-\n)ENUM_PNC_ERROR_CODE_FAILED_NO_NEGOTI" +
      "ATION\020\027\0222\n.ENUM_PNC_ERROR_CODE_EVSE_CERT" +
      "IFICATION_EXPIRED\020\030\022(\n$ENUM_PNC_ERROR_CO" +
      "DE_EVSE_MALFUNCTION\020\031\022%\n!ENUM_PNC_ERROR_" +
      "CODE_EVSE_SHUTDOWN\020\032\0223\n/ENUM_PNC_ERROR_C" +
      "ODE_EVSE_UTILITY_INTERUPT_EVENT\020\033\022/\n+ENU" +
      "M_PNC_ERROR_CODE_EVSE_EMERGENCY_SHUTDOWN" +
      "\020\034\0225\n1ENUM_PNC_ERROR_CODE_EVSE_ISOLATION" +
      "_STATUS_WARNING\020\035\0223\n/ENUM_PNC_ERROR_CODE" +
      "_EVSE_ISOLATION_STATUS_FAULT\020\036\022 \n\034ENUM_P" +
      "NC_ERROR_CODE_RCD_TRUE\020\037\0229\n5ENUM_PNC_ERR" +
      "OR_CODE_SUPPORTED_APP_PROTOCOL_REQ_TIMOU" +
      "T\020 \0221\n-ENUM_PNC_ERROR_CODE_SESSION_SETUP" +
      "_REQ_TIMEOUT\020!\0225\n1ENUM_PNC_ERROR_CODE_SE" +
      "RVICE_DISCOVERY_REQ_TIMEOUT\020\"\0222\n.ENUM_PN" +
      "C_ERROR_CODE_SERVICE_DETAIL_REQ_TIMEOUT\020" +
      "#\022=\n9ENUM_PNC_ERROR_CODE_SERVICE_PAYMENT" +
      "_SELECTION_REQ_TIMEOUT\020$\0222\n.ENUM_PNC_ERR" +
      "OR_CODE_PAYMENT_DETAIL_REQ_TIMEOUT\020%\0220\n," +
      "ENUM_PNC_ERROR_CODE_ATHORIZATION_REQ_TIM" +
      "EOUT\020&\022:\n6ENUM_PNC_ERROR_CODE_CHARGE_PAR" +
      "AM_DISCOVERY_REQ_TIMEOUT\020\'\0222\n.ENUM_PNC_E" +
      "RROR_CODE_POWER_DELIVERY_REQ_TIMEOUT\020(\0223" +
      "\n/ENUM_PNC_ERROR_CODE_CHARGING_STATUS_RE" +
      "Q_TIMEOUT\020)\0224\n0ENUM_PNC_ERROR_CODE_METER" +
      "ING_RECEIPT_REQ_TIMEOUT\020*\022/\n+ENUM_PNC_ER" +
      "ROR_CODE_CABLE_CHECK_REQ_TIMEOUT\020+\0225\n1EN" +
      "UM_PNC_ERROR_CODE_WELDING_DETECTION_REQ_" +
      "TIMEOUT\020,\022-\n)ENUM_PNC_ERROR_CODE_PRECHAR" +
      "GE_REQ_TIMEOUT\020-\0222\n.ENUM_PNC_ERROR_CODE_" +
      "CURRENT_DEMAND_REQ_TIMEOUT\020.\0220\n,ENUM_PNC" +
      "_ERROR_CODE_SESSION_STOP_REQ_TIMEOUT\020/\022@" +
      "\n<ENUM_PNC_ERROR_CODE_SUPPORTED_APP_PROT" +
      "OCOL_RES_FAILED_TIMOUT\0200\0229\n5ENUM_PNC_ERR" +
      "OR_CODE_SESSION_SETUP_RES_FAILED__TIMEOU" +
      "T\0201\022<\n8ENUM_PNC_ERROR_CODE_SERVICE_DISCO" +
      "VERY_RES_FAILED_TIMEOUT\0202\0229\n5ENUM_PNC_ER" +
      "ROR_CODE_SERVICE_DETAIL_RES_FAILED_TIMEO" +
      "UT\0203\022D\n@ENUM_PNC_ERROR_CODE_SERVICE_PAYM" +
      "ENT_SELECTION_RES_FAILED_TIMEOUT\0204\022:\n6EN" +
      "UM_PNC_ERROR_CODE_PAYMENT_DETAILS_RES_FA" +
      "ILED_TIMEOUT\0205\0227\n3ENUM_PNC_ERROR_CODE_AT" +
      "HORIZATION_RES_FAILED_TIMEOUT\0206\022A\n=ENUM_" +
      "PNC_ERROR_CODE_CHARGE_PARAM_DISCOVERY_RE" +
      "S_FAILED_TIMEOUT\0207\0229\n5ENUM_PNC_ERROR_COD" +
      "E_POWER_DELIVERY_RES_FAILED_TIMEOUT\0208\022:\n" +
      "6ENUM_PNC_ERROR_CODE_CHARGING_STATUS_RES" +
      "_FAILED_TIMEOUT\0209\022;\n7ENUM_PNC_ERROR_CODE" +
      "_METERING_RECEIPT_RES_FAILED_TIMEOUT\020:\0226" +
      "\n2ENUM_PNC_ERROR_CODE_CABLE_CHECK_RES_FA" +
      "ILED_TIMEOUT\020;\022<\n8ENUM_PNC_ERROR_CODE_WE" +
      "LDING_DETECTION_RES_FAILED_TIMEOUT\020<\0224\n0" +
      "ENUM_PNC_ERROR_CODE_PRECHARGE_RES_FAILED" +
      "_TIMEOUT\020=\0229\n5ENUM_PNC_ERROR_CODE_CURREN" +
      "T_DEMAND_RES_FAILED_TIMEOUT\020>\0227\n3ENUM_PN" +
      "C_ERROR_CODE_SESSION_STOP_RES_FAILED_TIM" +
      "EOUT\020?\0227\n3ENUM_PNC_ERROR_CODE_INPUT_SIGN" +
      "AL_ERR_CCF_CHARGE_PNC\020@\022<\n8ENUM_PNC_ERRO" +
      "R_CODE_INPUT_SIGNAL_ERR_PNC_TOGGLE_USER_" +
      "REQ\020A\022<\n8ENUM_PNC_ERROR_CODE_INPUT_SIGNA" +
      "L_ERR_PNC_CHINA_VIN_SHARE\020B\022>\n:ENUM_PNC_" +
      "ERROR_CODE_INPUT_SIGNAL_ERR_PNC_CONTRACT" +
      "_CERT_REQ\020C\0226\n2ENUM_PNC_ERROR_CODE_INPUT" +
      "_SIGNAL_ERR_UTC_TIME_DATE\020D\0229\n5ENUM_PNC_" +
      "ERROR_CODE_INPUT_SIGNAL_ERR_MULTIPLE_SIG" +
      "NALS\020E\0223\n/ENUM_PNC_ERROR_CODE_V2G_ROOT_C" +
      "ERT_EXPIRING_SOON\020F\022/\n+ENUM_PNC_ERROR_CO" +
      "DE_V2G_ROOT_CERT_CORRUPTED\020G\022-\n)ENUM_PNC" +
      "_ERROR_CODE_V2G_ROOT_CERT_EXPIRED\020H\0223\n/E" +
      "NUM_PNC_ERROR_CODE_V2G_ROOT_CERT_NOT_INS" +
      "TALLED\020I\022-\n)ENUM_PNC_ERROR_CODE_V2G_ROOT" +
      "_CERT_MISSING\020J\0223\n/ENUM_PNC_ERROR_CODE_O" +
      "EM_ROOT_CERT_EXPIRING_SOON\020K\022/\n+ENUM_PNC" +
      "_ERROR_CODE_OEM_ROOT_CERT_CORRUPTED\020L\022-\n" +
      ")ENUM_PNC_ERROR_CODE_OEM_ROOT_CERT_EXPIR" +
      "ED\020M\0223\n/ENUM_PNC_ERROR_CODE_OEM_ROOT_CER" +
      "T_NOT_INSTALLED\020N\022@\n<ENUM_PNC_ERROR_CODE" +
      "_OEM_PROVISION_CERT_SUB_CA1_EXPIRING_SOO" +
      "N\020O\022<\n8ENUM_PNC_ERROR_CODE_OEM_PROVISION" +
      "_CERT_SUB_CA1_CORRUPTED\020P\022:\n6ENUM_PNC_ER" +
      "ROR_CODE_OEM_PROVISION_CERT_SUB_CA1_EXPI" +
      "RED\020Q\022@\n<ENUM_PNC_ERROR_CODE_OEM_PROVISI" +
      "ON_CERT_SUB_CA1_NOT_INSTALLED\020R\022@\n<ENUM_" +
      "PNC_ERROR_CODE_OEM_PROVISION_CERT_SUB_CA" +
      "2_EXPIRING_SOON\020S\022<\n8ENUM_PNC_ERROR_CODE" +
      "_OEM_PROVISION_CERT_SUB_CA2_CORRUPTED\020T\022" +
      ":\n6ENUM_PNC_ERROR_CODE_OEM_PROVISION_CER" +
      "T_SUB_CA2_EXPIRED\020U\022@\n<ENUM_PNC_ERROR_CO" +
      "DE_OEM_PROVISION_CERT_SUB_CA2_NOT_INSTAL" +
      "LED\020V\022=\n9ENUM_PNC_ERROR_CODE_OEM_PROVISI" +
      "ON_CERT_LEAF_EXPIRING_SOON\020W\0229\n5ENUM_PNC" +
      "_ERROR_CODE_OEM_PROVISION_CERT_LEAF_CORR" +
      "UPTED\020X\0227\n3ENUM_PNC_ERROR_CODE_OEM_PROVI" +
      "SION_CERT_LEAF_EXPIRED\020Y\022=\n9ENUM_PNC_ERR" +
      "OR_CODE_OEM_PROVISION_CERT_LEAF_NOT_INST" +
      "ALLED\020Z\022D\n@ENUM_PNC_ERROR_CODE_OEM_PROVI" +
      "SION_CERT_PRIVATE_KEY_NOT_INSTALLED\020[\022@\n" +
      "<ENUM_PNC_ERROR_CODE_OEM_PROVISION_CERT_" +
      "PRIVATE_KEY_CORRUPTED\020\\b\006proto3"
    };
    descriptor = com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
        });
    internal_static_charging_common_PredictedChargeData_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_charging_common_PredictedChargeData_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessage.FieldAccessorTable(
        internal_static_charging_common_PredictedChargeData_descriptor,
        new java.lang.String[] { "BatteryLevel", "PredictedRange", "PredictedTime", });
    internal_static_charging_common_ChargingCurveData_descriptor =
      getDescriptor().getMessageTypes().get(1);
    internal_static_charging_common_ChargingCurveData_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessage.FieldAccessorTable(
        internal_static_charging_common_ChargingCurveData_descriptor,
        new java.lang.String[] { "StateOfCharge", "MaxPower", });
    descriptor.resolveAllFeaturesImmutable();
  }

  // @@protoc_insertion_point(outer_class_scope)
}
