// Generated by the protocol buffer compiler.  DO NOT EDIT!
// NO CHECKED-IN PROTOBUF GENCODE
// source: climate_common.proto
// Protobuf Java Version: 4.28.2

package climate_common;

public final class ClimateCommon {
  private ClimateCommon() {}
  static {
    com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
      com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
      /* major= */ 4,
      /* minor= */ 28,
      /* patch= */ 2,
      /* suffix= */ "",
      ClimateCommon.class.getName());
  }
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  /**
   * <pre>
   * Following enumeration specifies seat climate temperature controlled intensity
   * </pre>
   *
   * Protobuf enum {@code climate_common.EnumSeatClimateIntensity}
   */
  public enum EnumSeatClimateIntensity
      implements com.google.protobuf.ProtocolMessageEnum {
    /**
     * <code>ENUM_SEAT_CLIMATE_INTENSITY_UNSPECIFIED = 0;</code>
     */
    ENUM_SEAT_CLIMATE_INTENSITY_UNSPECIFIED(0),
    /**
     * <code>ENUM_SEAT_CLIMATE_INTENSITY_OFF = 1;</code>
     */
    ENUM_SEAT_CLIMATE_INTENSITY_OFF(1),
    /**
     * <code>ENUM_SEAT_CLIMATE_INTENSITY_HEAT_LEVEL_1 = 2;</code>
     */
    ENUM_SEAT_CLIMATE_INTENSITY_HEAT_LEVEL_1(2),
    /**
     * <code>ENUM_SEAT_CLIMATE_INTENSITY_COOL_LEVEL_1 = 3;</code>
     */
    ENUM_SEAT_CLIMATE_INTENSITY_COOL_LEVEL_1(3),
    /**
     * <code>ENUM_SEAT_CLIMATE_INTENSITY_HEAT_LEVEL_2 = 4;</code>
     */
    ENUM_SEAT_CLIMATE_INTENSITY_HEAT_LEVEL_2(4),
    /**
     * <code>ENUM_SEAT_CLIMATE_INTENSITY_COOL_LEVEL_2 = 5;</code>
     */
    ENUM_SEAT_CLIMATE_INTENSITY_COOL_LEVEL_2(5),
    /**
     * <code>ENUM_SEAT_CLIMATE_INTENSITY_HEAT_LEVEL_3 = 6;</code>
     */
    ENUM_SEAT_CLIMATE_INTENSITY_HEAT_LEVEL_3(6),
    /**
     * <code>ENUM_SEAT_CLIMATE_INTENSITY_COOL_LEVEL_3 = 7;</code>
     */
    ENUM_SEAT_CLIMATE_INTENSITY_COOL_LEVEL_3(7),
    /**
     * <code>ENUM_SEAT_CLIMATE_INTENSITY_HEAT_LEVEL_4 = 8;</code>
     */
    ENUM_SEAT_CLIMATE_INTENSITY_HEAT_LEVEL_4(8),
    /**
     * <code>ENUM_SEAT_CLIMATE_INTENSITY_COOL_LEVEL_4 = 9;</code>
     */
    ENUM_SEAT_CLIMATE_INTENSITY_COOL_LEVEL_4(9),
    /**
     * <code>ENUM_SEAT_CLIMATE_INTENSITY_HEAT_LEVEL_5 = 10;</code>
     */
    ENUM_SEAT_CLIMATE_INTENSITY_HEAT_LEVEL_5(10),
    /**
     * <code>ENUM_SEAT_CLIMATE_INTENSITY_COOL_LEVEL_5 = 11;</code>
     */
    ENUM_SEAT_CLIMATE_INTENSITY_COOL_LEVEL_5(11),
    UNRECOGNIZED(-1),
    ;

    static {
      com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
        com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
        /* major= */ 4,
        /* minor= */ 28,
        /* patch= */ 2,
        /* suffix= */ "",
        EnumSeatClimateIntensity.class.getName());
    }
    /**
     * <code>ENUM_SEAT_CLIMATE_INTENSITY_UNSPECIFIED = 0;</code>
     */
    public static final int ENUM_SEAT_CLIMATE_INTENSITY_UNSPECIFIED_VALUE = 0;
    /**
     * <code>ENUM_SEAT_CLIMATE_INTENSITY_OFF = 1;</code>
     */
    public static final int ENUM_SEAT_CLIMATE_INTENSITY_OFF_VALUE = 1;
    /**
     * <code>ENUM_SEAT_CLIMATE_INTENSITY_HEAT_LEVEL_1 = 2;</code>
     */
    public static final int ENUM_SEAT_CLIMATE_INTENSITY_HEAT_LEVEL_1_VALUE = 2;
    /**
     * <code>ENUM_SEAT_CLIMATE_INTENSITY_COOL_LEVEL_1 = 3;</code>
     */
    public static final int ENUM_SEAT_CLIMATE_INTENSITY_COOL_LEVEL_1_VALUE = 3;
    /**
     * <code>ENUM_SEAT_CLIMATE_INTENSITY_HEAT_LEVEL_2 = 4;</code>
     */
    public static final int ENUM_SEAT_CLIMATE_INTENSITY_HEAT_LEVEL_2_VALUE = 4;
    /**
     * <code>ENUM_SEAT_CLIMATE_INTENSITY_COOL_LEVEL_2 = 5;</code>
     */
    public static final int ENUM_SEAT_CLIMATE_INTENSITY_COOL_LEVEL_2_VALUE = 5;
    /**
     * <code>ENUM_SEAT_CLIMATE_INTENSITY_HEAT_LEVEL_3 = 6;</code>
     */
    public static final int ENUM_SEAT_CLIMATE_INTENSITY_HEAT_LEVEL_3_VALUE = 6;
    /**
     * <code>ENUM_SEAT_CLIMATE_INTENSITY_COOL_LEVEL_3 = 7;</code>
     */
    public static final int ENUM_SEAT_CLIMATE_INTENSITY_COOL_LEVEL_3_VALUE = 7;
    /**
     * <code>ENUM_SEAT_CLIMATE_INTENSITY_HEAT_LEVEL_4 = 8;</code>
     */
    public static final int ENUM_SEAT_CLIMATE_INTENSITY_HEAT_LEVEL_4_VALUE = 8;
    /**
     * <code>ENUM_SEAT_CLIMATE_INTENSITY_COOL_LEVEL_4 = 9;</code>
     */
    public static final int ENUM_SEAT_CLIMATE_INTENSITY_COOL_LEVEL_4_VALUE = 9;
    /**
     * <code>ENUM_SEAT_CLIMATE_INTENSITY_HEAT_LEVEL_5 = 10;</code>
     */
    public static final int ENUM_SEAT_CLIMATE_INTENSITY_HEAT_LEVEL_5_VALUE = 10;
    /**
     * <code>ENUM_SEAT_CLIMATE_INTENSITY_COOL_LEVEL_5 = 11;</code>
     */
    public static final int ENUM_SEAT_CLIMATE_INTENSITY_COOL_LEVEL_5_VALUE = 11;


    public final int getNumber() {
      if (this == UNRECOGNIZED) {
        throw new java.lang.IllegalArgumentException(
            "Can't get the number of an unknown enum value.");
      }
      return value;
    }

    /**
     * @param value The numeric wire value of the corresponding enum entry.
     * @return The enum associated with the given numeric wire value.
     * @deprecated Use {@link #forNumber(int)} instead.
     */
    @java.lang.Deprecated
    public static EnumSeatClimateIntensity valueOf(int value) {
      return forNumber(value);
    }

    /**
     * @param value The numeric wire value of the corresponding enum entry.
     * @return The enum associated with the given numeric wire value.
     */
    public static EnumSeatClimateIntensity forNumber(int value) {
      switch (value) {
        case 0: return ENUM_SEAT_CLIMATE_INTENSITY_UNSPECIFIED;
        case 1: return ENUM_SEAT_CLIMATE_INTENSITY_OFF;
        case 2: return ENUM_SEAT_CLIMATE_INTENSITY_HEAT_LEVEL_1;
        case 3: return ENUM_SEAT_CLIMATE_INTENSITY_COOL_LEVEL_1;
        case 4: return ENUM_SEAT_CLIMATE_INTENSITY_HEAT_LEVEL_2;
        case 5: return ENUM_SEAT_CLIMATE_INTENSITY_COOL_LEVEL_2;
        case 6: return ENUM_SEAT_CLIMATE_INTENSITY_HEAT_LEVEL_3;
        case 7: return ENUM_SEAT_CLIMATE_INTENSITY_COOL_LEVEL_3;
        case 8: return ENUM_SEAT_CLIMATE_INTENSITY_HEAT_LEVEL_4;
        case 9: return ENUM_SEAT_CLIMATE_INTENSITY_COOL_LEVEL_4;
        case 10: return ENUM_SEAT_CLIMATE_INTENSITY_HEAT_LEVEL_5;
        case 11: return ENUM_SEAT_CLIMATE_INTENSITY_COOL_LEVEL_5;
        default: return null;
      }
    }

    public static com.google.protobuf.Internal.EnumLiteMap<EnumSeatClimateIntensity>
        internalGetValueMap() {
      return internalValueMap;
    }
    private static final com.google.protobuf.Internal.EnumLiteMap<
        EnumSeatClimateIntensity> internalValueMap =
          new com.google.protobuf.Internal.EnumLiteMap<EnumSeatClimateIntensity>() {
            public EnumSeatClimateIntensity findValueByNumber(int number) {
              return EnumSeatClimateIntensity.forNumber(number);
            }
          };

    public final com.google.protobuf.Descriptors.EnumValueDescriptor
        getValueDescriptor() {
      if (this == UNRECOGNIZED) {
        throw new java.lang.IllegalStateException(
            "Can't get the descriptor of an unrecognized enum value.");
      }
      return getDescriptor().getValues().get(ordinal());
    }
    public final com.google.protobuf.Descriptors.EnumDescriptor
        getDescriptorForType() {
      return getDescriptor();
    }
    public static final com.google.protobuf.Descriptors.EnumDescriptor
        getDescriptor() {
      return climate_common.ClimateCommon.getDescriptor().getEnumTypes().get(0);
    }

    private static final EnumSeatClimateIntensity[] VALUES = values();

    public static EnumSeatClimateIntensity valueOf(
        com.google.protobuf.Descriptors.EnumValueDescriptor desc) {
      if (desc.getType() != getDescriptor()) {
        throw new java.lang.IllegalArgumentException(
          "EnumValueDescriptor is not for this type.");
      }
      if (desc.getIndex() == -1) {
        return UNRECOGNIZED;
      }
      return VALUES[desc.getIndex()];
    }

    private final int value;

    private EnumSeatClimateIntensity(int value) {
      this.value = value;
    }

    // @@protoc_insertion_point(enum_scope:climate_common.EnumSeatClimateIntensity)
  }

  /**
   * <pre>
   * Following enumeration specifies seat area
   * When turning ON or turning OFF the Seat Climate, EnumSeatClimateArea shall not be populated.
   * ENUM_SEAT_CLIMATE_AREA_ALL is used when want to specifiy all seat controls including peripherals,
   * i.e. Cushion, Squab, Foot Rest, Arm Rest, Calf Rest.
   * </pre>
   *
   * Protobuf enum {@code climate_common.EnumSeatClimateArea}
   */
  public enum EnumSeatClimateArea
      implements com.google.protobuf.ProtocolMessageEnum {
    /**
     * <code>ENUM_SEAT_CLIMATE_AREA_UNSPECIFIED = 0;</code>
     */
    ENUM_SEAT_CLIMATE_AREA_UNSPECIFIED(0),
    /**
     * <code>ENUM_SEAT_CLIMATE_AREA_ALL = 1;</code>
     */
    ENUM_SEAT_CLIMATE_AREA_ALL(1),
    /**
     * <code>ENUM_SEAT_CLIMATE_AREA_CUSHION = 2;</code>
     */
    ENUM_SEAT_CLIMATE_AREA_CUSHION(2),
    /**
     * <code>ENUM_SEAT_CLIMATE_AREA_SQUAB = 3;</code>
     */
    ENUM_SEAT_CLIMATE_AREA_SQUAB(3),
    /**
     * <pre>
     * Door Arm rest is associated with left or right seat selection based on EnumSeatSelection values.
     * </pre>
     *
     * <code>ENUM_SEAT_CLIMATE_AREA_DOOR_ARM_REST = 4;</code>
     */
    ENUM_SEAT_CLIMATE_AREA_DOOR_ARM_REST(4),
    /**
     * <pre>
     * ENUM_SEAT_SELECTION_FIRST_ROW_CENTRE or ENUM_SEAT_SELECTION_SECOND_ROW_CENTRE shall be used
     * with ENUM_SEAT_CLIMATE_AREA_CENTER_ARM_REST as applicable.
     * </pre>
     *
     * <code>ENUM_SEAT_CLIMATE_AREA_CENTER_ARM_REST = 5;</code>
     */
    ENUM_SEAT_CLIMATE_AREA_CENTER_ARM_REST(5),
    /**
     * <pre>
     * Calf rest is only applicable to second row.
     * </pre>
     *
     * <code>ENUM_SEAT_CLIMATE_AREA_CALF_REST = 6;</code>
     */
    ENUM_SEAT_CLIMATE_AREA_CALF_REST(6),
    /**
     * <pre>
     * There is only one Foot rest in the vehicle.
     * So while sending ENUM_SEAT_CLIMATE_AREA_FOOT_REST there is no need to populate seat selection.
     * </pre>
     *
     * <code>ENUM_SEAT_CLIMATE_AREA_FOOT_REST = 7;</code>
     */
    ENUM_SEAT_CLIMATE_AREA_FOOT_REST(7),
    UNRECOGNIZED(-1),
    ;

    static {
      com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
        com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
        /* major= */ 4,
        /* minor= */ 28,
        /* patch= */ 2,
        /* suffix= */ "",
        EnumSeatClimateArea.class.getName());
    }
    /**
     * <code>ENUM_SEAT_CLIMATE_AREA_UNSPECIFIED = 0;</code>
     */
    public static final int ENUM_SEAT_CLIMATE_AREA_UNSPECIFIED_VALUE = 0;
    /**
     * <code>ENUM_SEAT_CLIMATE_AREA_ALL = 1;</code>
     */
    public static final int ENUM_SEAT_CLIMATE_AREA_ALL_VALUE = 1;
    /**
     * <code>ENUM_SEAT_CLIMATE_AREA_CUSHION = 2;</code>
     */
    public static final int ENUM_SEAT_CLIMATE_AREA_CUSHION_VALUE = 2;
    /**
     * <code>ENUM_SEAT_CLIMATE_AREA_SQUAB = 3;</code>
     */
    public static final int ENUM_SEAT_CLIMATE_AREA_SQUAB_VALUE = 3;
    /**
     * <pre>
     * Door Arm rest is associated with left or right seat selection based on EnumSeatSelection values.
     * </pre>
     *
     * <code>ENUM_SEAT_CLIMATE_AREA_DOOR_ARM_REST = 4;</code>
     */
    public static final int ENUM_SEAT_CLIMATE_AREA_DOOR_ARM_REST_VALUE = 4;
    /**
     * <pre>
     * ENUM_SEAT_SELECTION_FIRST_ROW_CENTRE or ENUM_SEAT_SELECTION_SECOND_ROW_CENTRE shall be used
     * with ENUM_SEAT_CLIMATE_AREA_CENTER_ARM_REST as applicable.
     * </pre>
     *
     * <code>ENUM_SEAT_CLIMATE_AREA_CENTER_ARM_REST = 5;</code>
     */
    public static final int ENUM_SEAT_CLIMATE_AREA_CENTER_ARM_REST_VALUE = 5;
    /**
     * <pre>
     * Calf rest is only applicable to second row.
     * </pre>
     *
     * <code>ENUM_SEAT_CLIMATE_AREA_CALF_REST = 6;</code>
     */
    public static final int ENUM_SEAT_CLIMATE_AREA_CALF_REST_VALUE = 6;
    /**
     * <pre>
     * There is only one Foot rest in the vehicle.
     * So while sending ENUM_SEAT_CLIMATE_AREA_FOOT_REST there is no need to populate seat selection.
     * </pre>
     *
     * <code>ENUM_SEAT_CLIMATE_AREA_FOOT_REST = 7;</code>
     */
    public static final int ENUM_SEAT_CLIMATE_AREA_FOOT_REST_VALUE = 7;


    public final int getNumber() {
      if (this == UNRECOGNIZED) {
        throw new java.lang.IllegalArgumentException(
            "Can't get the number of an unknown enum value.");
      }
      return value;
    }

    /**
     * @param value The numeric wire value of the corresponding enum entry.
     * @return The enum associated with the given numeric wire value.
     * @deprecated Use {@link #forNumber(int)} instead.
     */
    @java.lang.Deprecated
    public static EnumSeatClimateArea valueOf(int value) {
      return forNumber(value);
    }

    /**
     * @param value The numeric wire value of the corresponding enum entry.
     * @return The enum associated with the given numeric wire value.
     */
    public static EnumSeatClimateArea forNumber(int value) {
      switch (value) {
        case 0: return ENUM_SEAT_CLIMATE_AREA_UNSPECIFIED;
        case 1: return ENUM_SEAT_CLIMATE_AREA_ALL;
        case 2: return ENUM_SEAT_CLIMATE_AREA_CUSHION;
        case 3: return ENUM_SEAT_CLIMATE_AREA_SQUAB;
        case 4: return ENUM_SEAT_CLIMATE_AREA_DOOR_ARM_REST;
        case 5: return ENUM_SEAT_CLIMATE_AREA_CENTER_ARM_REST;
        case 6: return ENUM_SEAT_CLIMATE_AREA_CALF_REST;
        case 7: return ENUM_SEAT_CLIMATE_AREA_FOOT_REST;
        default: return null;
      }
    }

    public static com.google.protobuf.Internal.EnumLiteMap<EnumSeatClimateArea>
        internalGetValueMap() {
      return internalValueMap;
    }
    private static final com.google.protobuf.Internal.EnumLiteMap<
        EnumSeatClimateArea> internalValueMap =
          new com.google.protobuf.Internal.EnumLiteMap<EnumSeatClimateArea>() {
            public EnumSeatClimateArea findValueByNumber(int number) {
              return EnumSeatClimateArea.forNumber(number);
            }
          };

    public final com.google.protobuf.Descriptors.EnumValueDescriptor
        getValueDescriptor() {
      if (this == UNRECOGNIZED) {
        throw new java.lang.IllegalStateException(
            "Can't get the descriptor of an unrecognized enum value.");
      }
      return getDescriptor().getValues().get(ordinal());
    }
    public final com.google.protobuf.Descriptors.EnumDescriptor
        getDescriptorForType() {
      return getDescriptor();
    }
    public static final com.google.protobuf.Descriptors.EnumDescriptor
        getDescriptor() {
      return climate_common.ClimateCommon.getDescriptor().getEnumTypes().get(1);
    }

    private static final EnumSeatClimateArea[] VALUES = values();

    public static EnumSeatClimateArea valueOf(
        com.google.protobuf.Descriptors.EnumValueDescriptor desc) {
      if (desc.getType() != getDescriptor()) {
        throw new java.lang.IllegalArgumentException(
          "EnumValueDescriptor is not for this type.");
      }
      if (desc.getIndex() == -1) {
        return UNRECOGNIZED;
      }
      return VALUES[desc.getIndex()];
    }

    private final int value;

    private EnumSeatClimateArea(int value) {
      this.value = value;
    }

    // @@protoc_insertion_point(enum_scope:climate_common.EnumSeatClimateArea)
  }

  /**
   * <pre>
   * Following enumeration specifies seat area state
   * </pre>
   *
   * Protobuf enum {@code climate_common.EnumSeatClimateState}
   */
  public enum EnumSeatClimateState
      implements com.google.protobuf.ProtocolMessageEnum {
    /**
     * <code>ENUM_SEAT_CLIMATE_STATE_UNSPECIFIED = 0;</code>
     */
    ENUM_SEAT_CLIMATE_STATE_UNSPECIFIED(0),
    /**
     * <code>ENUM_SEAT_CLIMATE_STATE_ON = 1;</code>
     */
    ENUM_SEAT_CLIMATE_STATE_ON(1),
    /**
     * <code>ENUM_SEAT_CLIMATE_STATE_OFF = 2;</code>
     */
    ENUM_SEAT_CLIMATE_STATE_OFF(2),
    /**
     * <code>ENUM_SEAT_CLIMATE_STATE_INHIBIT = 3;</code>
     */
    ENUM_SEAT_CLIMATE_STATE_INHIBIT(3),
    UNRECOGNIZED(-1),
    ;

    static {
      com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
        com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
        /* major= */ 4,
        /* minor= */ 28,
        /* patch= */ 2,
        /* suffix= */ "",
        EnumSeatClimateState.class.getName());
    }
    /**
     * <code>ENUM_SEAT_CLIMATE_STATE_UNSPECIFIED = 0;</code>
     */
    public static final int ENUM_SEAT_CLIMATE_STATE_UNSPECIFIED_VALUE = 0;
    /**
     * <code>ENUM_SEAT_CLIMATE_STATE_ON = 1;</code>
     */
    public static final int ENUM_SEAT_CLIMATE_STATE_ON_VALUE = 1;
    /**
     * <code>ENUM_SEAT_CLIMATE_STATE_OFF = 2;</code>
     */
    public static final int ENUM_SEAT_CLIMATE_STATE_OFF_VALUE = 2;
    /**
     * <code>ENUM_SEAT_CLIMATE_STATE_INHIBIT = 3;</code>
     */
    public static final int ENUM_SEAT_CLIMATE_STATE_INHIBIT_VALUE = 3;


    public final int getNumber() {
      if (this == UNRECOGNIZED) {
        throw new java.lang.IllegalArgumentException(
            "Can't get the number of an unknown enum value.");
      }
      return value;
    }

    /**
     * @param value The numeric wire value of the corresponding enum entry.
     * @return The enum associated with the given numeric wire value.
     * @deprecated Use {@link #forNumber(int)} instead.
     */
    @java.lang.Deprecated
    public static EnumSeatClimateState valueOf(int value) {
      return forNumber(value);
    }

    /**
     * @param value The numeric wire value of the corresponding enum entry.
     * @return The enum associated with the given numeric wire value.
     */
    public static EnumSeatClimateState forNumber(int value) {
      switch (value) {
        case 0: return ENUM_SEAT_CLIMATE_STATE_UNSPECIFIED;
        case 1: return ENUM_SEAT_CLIMATE_STATE_ON;
        case 2: return ENUM_SEAT_CLIMATE_STATE_OFF;
        case 3: return ENUM_SEAT_CLIMATE_STATE_INHIBIT;
        default: return null;
      }
    }

    public static com.google.protobuf.Internal.EnumLiteMap<EnumSeatClimateState>
        internalGetValueMap() {
      return internalValueMap;
    }
    private static final com.google.protobuf.Internal.EnumLiteMap<
        EnumSeatClimateState> internalValueMap =
          new com.google.protobuf.Internal.EnumLiteMap<EnumSeatClimateState>() {
            public EnumSeatClimateState findValueByNumber(int number) {
              return EnumSeatClimateState.forNumber(number);
            }
          };

    public final com.google.protobuf.Descriptors.EnumValueDescriptor
        getValueDescriptor() {
      if (this == UNRECOGNIZED) {
        throw new java.lang.IllegalStateException(
            "Can't get the descriptor of an unrecognized enum value.");
      }
      return getDescriptor().getValues().get(ordinal());
    }
    public final com.google.protobuf.Descriptors.EnumDescriptor
        getDescriptorForType() {
      return getDescriptor();
    }
    public static final com.google.protobuf.Descriptors.EnumDescriptor
        getDescriptor() {
      return climate_common.ClimateCommon.getDescriptor().getEnumTypes().get(2);
    }

    private static final EnumSeatClimateState[] VALUES = values();

    public static EnumSeatClimateState valueOf(
        com.google.protobuf.Descriptors.EnumValueDescriptor desc) {
      if (desc.getType() != getDescriptor()) {
        throw new java.lang.IllegalArgumentException(
          "EnumValueDescriptor is not for this type.");
      }
      if (desc.getIndex() == -1) {
        return UNRECOGNIZED;
      }
      return VALUES[desc.getIndex()];
    }

    private final int value;

    private EnumSeatClimateState(int value) {
      this.value = value;
    }

    // @@protoc_insertion_point(enum_scope:climate_common.EnumSeatClimateState)
  }

  /**
   * <pre>
   * Used to select and retrieve the status of Heated Steering Wheel.
   * </pre>
   *
   * Protobuf enum {@code climate_common.EnumHSWTemperatureLevel}
   */
  public enum EnumHSWTemperatureLevel
      implements com.google.protobuf.ProtocolMessageEnum {
    /**
     * <code>ENUM_HSW_TEMPERATURE_LEVEL_UNSPECIFIED = 0;</code>
     */
    ENUM_HSW_TEMPERATURE_LEVEL_UNSPECIFIED(0),
    /**
     * <code>ENUM_HSW_TEMPERATURE_LEVEL_OFF = 1;</code>
     */
    ENUM_HSW_TEMPERATURE_LEVEL_OFF(1),
    /**
     * <code>ENUM_HSW_TEMPERATURE_LEVEL_1 = 2;</code>
     */
    ENUM_HSW_TEMPERATURE_LEVEL_1(2),
    /**
     * <code>ENUM_HSW_TEMPERATURE_LEVEL_2 = 3;</code>
     */
    ENUM_HSW_TEMPERATURE_LEVEL_2(3),
    UNRECOGNIZED(-1),
    ;

    static {
      com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
        com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
        /* major= */ 4,
        /* minor= */ 28,
        /* patch= */ 2,
        /* suffix= */ "",
        EnumHSWTemperatureLevel.class.getName());
    }
    /**
     * <code>ENUM_HSW_TEMPERATURE_LEVEL_UNSPECIFIED = 0;</code>
     */
    public static final int ENUM_HSW_TEMPERATURE_LEVEL_UNSPECIFIED_VALUE = 0;
    /**
     * <code>ENUM_HSW_TEMPERATURE_LEVEL_OFF = 1;</code>
     */
    public static final int ENUM_HSW_TEMPERATURE_LEVEL_OFF_VALUE = 1;
    /**
     * <code>ENUM_HSW_TEMPERATURE_LEVEL_1 = 2;</code>
     */
    public static final int ENUM_HSW_TEMPERATURE_LEVEL_1_VALUE = 2;
    /**
     * <code>ENUM_HSW_TEMPERATURE_LEVEL_2 = 3;</code>
     */
    public static final int ENUM_HSW_TEMPERATURE_LEVEL_2_VALUE = 3;


    public final int getNumber() {
      if (this == UNRECOGNIZED) {
        throw new java.lang.IllegalArgumentException(
            "Can't get the number of an unknown enum value.");
      }
      return value;
    }

    /**
     * @param value The numeric wire value of the corresponding enum entry.
     * @return The enum associated with the given numeric wire value.
     * @deprecated Use {@link #forNumber(int)} instead.
     */
    @java.lang.Deprecated
    public static EnumHSWTemperatureLevel valueOf(int value) {
      return forNumber(value);
    }

    /**
     * @param value The numeric wire value of the corresponding enum entry.
     * @return The enum associated with the given numeric wire value.
     */
    public static EnumHSWTemperatureLevel forNumber(int value) {
      switch (value) {
        case 0: return ENUM_HSW_TEMPERATURE_LEVEL_UNSPECIFIED;
        case 1: return ENUM_HSW_TEMPERATURE_LEVEL_OFF;
        case 2: return ENUM_HSW_TEMPERATURE_LEVEL_1;
        case 3: return ENUM_HSW_TEMPERATURE_LEVEL_2;
        default: return null;
      }
    }

    public static com.google.protobuf.Internal.EnumLiteMap<EnumHSWTemperatureLevel>
        internalGetValueMap() {
      return internalValueMap;
    }
    private static final com.google.protobuf.Internal.EnumLiteMap<
        EnumHSWTemperatureLevel> internalValueMap =
          new com.google.protobuf.Internal.EnumLiteMap<EnumHSWTemperatureLevel>() {
            public EnumHSWTemperatureLevel findValueByNumber(int number) {
              return EnumHSWTemperatureLevel.forNumber(number);
            }
          };

    public final com.google.protobuf.Descriptors.EnumValueDescriptor
        getValueDescriptor() {
      if (this == UNRECOGNIZED) {
        throw new java.lang.IllegalStateException(
            "Can't get the descriptor of an unrecognized enum value.");
      }
      return getDescriptor().getValues().get(ordinal());
    }
    public final com.google.protobuf.Descriptors.EnumDescriptor
        getDescriptorForType() {
      return getDescriptor();
    }
    public static final com.google.protobuf.Descriptors.EnumDescriptor
        getDescriptor() {
      return climate_common.ClimateCommon.getDescriptor().getEnumTypes().get(3);
    }

    private static final EnumHSWTemperatureLevel[] VALUES = values();

    public static EnumHSWTemperatureLevel valueOf(
        com.google.protobuf.Descriptors.EnumValueDescriptor desc) {
      if (desc.getType() != getDescriptor()) {
        throw new java.lang.IllegalArgumentException(
          "EnumValueDescriptor is not for this type.");
      }
      if (desc.getIndex() == -1) {
        return UNRECOGNIZED;
      }
      return VALUES[desc.getIndex()];
    }

    private final int value;

    private EnumHSWTemperatureLevel(int value) {
      this.value = value;
    }

    // @@protoc_insertion_point(enum_scope:climate_common.EnumHSWTemperatureLevel)
  }

  public interface SeatClimateOperationOrBuilder extends
      // @@protoc_insertion_point(interface_extends:climate_common.SeatClimateOperation)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>.climate_common.EnumSeatClimateArea seat_area = 1;</code>
     * @return The enum numeric value on the wire for seatArea.
     */
    int getSeatAreaValue();
    /**
     * <code>.climate_common.EnumSeatClimateArea seat_area = 1;</code>
     * @return The seatArea.
     */
    climate_common.ClimateCommon.EnumSeatClimateArea getSeatArea();

    /**
     * <code>.climate_common.EnumSeatClimateState seat_state = 2;</code>
     * @return The enum numeric value on the wire for seatState.
     */
    int getSeatStateValue();
    /**
     * <code>.climate_common.EnumSeatClimateState seat_state = 2;</code>
     * @return The seatState.
     */
    climate_common.ClimateCommon.EnumSeatClimateState getSeatState();

    /**
     * <code>.climate_common.EnumSeatClimateIntensity seat_climate_intensity = 3;</code>
     * @return The enum numeric value on the wire for seatClimateIntensity.
     */
    int getSeatClimateIntensityValue();
    /**
     * <code>.climate_common.EnumSeatClimateIntensity seat_climate_intensity = 3;</code>
     * @return The seatClimateIntensity.
     */
    climate_common.ClimateCommon.EnumSeatClimateIntensity getSeatClimateIntensity();
  }
  /**
   * <pre>
   * This is a nested message contains seat area,seat state and seat intensity.
   * </pre>
   *
   * Protobuf type {@code climate_common.SeatClimateOperation}
   */
  public static final class SeatClimateOperation extends
      com.google.protobuf.GeneratedMessage implements
      // @@protoc_insertion_point(message_implements:climate_common.SeatClimateOperation)
      SeatClimateOperationOrBuilder {
  private static final long serialVersionUID = 0L;
    static {
      com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
        com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
        /* major= */ 4,
        /* minor= */ 28,
        /* patch= */ 2,
        /* suffix= */ "",
        SeatClimateOperation.class.getName());
    }
    // Use SeatClimateOperation.newBuilder() to construct.
    private SeatClimateOperation(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
    }
    private SeatClimateOperation() {
      seatArea_ = 0;
      seatState_ = 0;
      seatClimateIntensity_ = 0;
    }

    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return climate_common.ClimateCommon.internal_static_climate_common_SeatClimateOperation_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return climate_common.ClimateCommon.internal_static_climate_common_SeatClimateOperation_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              climate_common.ClimateCommon.SeatClimateOperation.class, climate_common.ClimateCommon.SeatClimateOperation.Builder.class);
    }

    public static final int SEAT_AREA_FIELD_NUMBER = 1;
    private int seatArea_ = 0;
    /**
     * <code>.climate_common.EnumSeatClimateArea seat_area = 1;</code>
     * @return The enum numeric value on the wire for seatArea.
     */
    @java.lang.Override public int getSeatAreaValue() {
      return seatArea_;
    }
    /**
     * <code>.climate_common.EnumSeatClimateArea seat_area = 1;</code>
     * @return The seatArea.
     */
    @java.lang.Override public climate_common.ClimateCommon.EnumSeatClimateArea getSeatArea() {
      climate_common.ClimateCommon.EnumSeatClimateArea result = climate_common.ClimateCommon.EnumSeatClimateArea.forNumber(seatArea_);
      return result == null ? climate_common.ClimateCommon.EnumSeatClimateArea.UNRECOGNIZED : result;
    }

    public static final int SEAT_STATE_FIELD_NUMBER = 2;
    private int seatState_ = 0;
    /**
     * <code>.climate_common.EnumSeatClimateState seat_state = 2;</code>
     * @return The enum numeric value on the wire for seatState.
     */
    @java.lang.Override public int getSeatStateValue() {
      return seatState_;
    }
    /**
     * <code>.climate_common.EnumSeatClimateState seat_state = 2;</code>
     * @return The seatState.
     */
    @java.lang.Override public climate_common.ClimateCommon.EnumSeatClimateState getSeatState() {
      climate_common.ClimateCommon.EnumSeatClimateState result = climate_common.ClimateCommon.EnumSeatClimateState.forNumber(seatState_);
      return result == null ? climate_common.ClimateCommon.EnumSeatClimateState.UNRECOGNIZED : result;
    }

    public static final int SEAT_CLIMATE_INTENSITY_FIELD_NUMBER = 3;
    private int seatClimateIntensity_ = 0;
    /**
     * <code>.climate_common.EnumSeatClimateIntensity seat_climate_intensity = 3;</code>
     * @return The enum numeric value on the wire for seatClimateIntensity.
     */
    @java.lang.Override public int getSeatClimateIntensityValue() {
      return seatClimateIntensity_;
    }
    /**
     * <code>.climate_common.EnumSeatClimateIntensity seat_climate_intensity = 3;</code>
     * @return The seatClimateIntensity.
     */
    @java.lang.Override public climate_common.ClimateCommon.EnumSeatClimateIntensity getSeatClimateIntensity() {
      climate_common.ClimateCommon.EnumSeatClimateIntensity result = climate_common.ClimateCommon.EnumSeatClimateIntensity.forNumber(seatClimateIntensity_);
      return result == null ? climate_common.ClimateCommon.EnumSeatClimateIntensity.UNRECOGNIZED : result;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (seatArea_ != climate_common.ClimateCommon.EnumSeatClimateArea.ENUM_SEAT_CLIMATE_AREA_UNSPECIFIED.getNumber()) {
        output.writeEnum(1, seatArea_);
      }
      if (seatState_ != climate_common.ClimateCommon.EnumSeatClimateState.ENUM_SEAT_CLIMATE_STATE_UNSPECIFIED.getNumber()) {
        output.writeEnum(2, seatState_);
      }
      if (seatClimateIntensity_ != climate_common.ClimateCommon.EnumSeatClimateIntensity.ENUM_SEAT_CLIMATE_INTENSITY_UNSPECIFIED.getNumber()) {
        output.writeEnum(3, seatClimateIntensity_);
      }
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (seatArea_ != climate_common.ClimateCommon.EnumSeatClimateArea.ENUM_SEAT_CLIMATE_AREA_UNSPECIFIED.getNumber()) {
        size += com.google.protobuf.CodedOutputStream
          .computeEnumSize(1, seatArea_);
      }
      if (seatState_ != climate_common.ClimateCommon.EnumSeatClimateState.ENUM_SEAT_CLIMATE_STATE_UNSPECIFIED.getNumber()) {
        size += com.google.protobuf.CodedOutputStream
          .computeEnumSize(2, seatState_);
      }
      if (seatClimateIntensity_ != climate_common.ClimateCommon.EnumSeatClimateIntensity.ENUM_SEAT_CLIMATE_INTENSITY_UNSPECIFIED.getNumber()) {
        size += com.google.protobuf.CodedOutputStream
          .computeEnumSize(3, seatClimateIntensity_);
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof climate_common.ClimateCommon.SeatClimateOperation)) {
        return super.equals(obj);
      }
      climate_common.ClimateCommon.SeatClimateOperation other = (climate_common.ClimateCommon.SeatClimateOperation) obj;

      if (seatArea_ != other.seatArea_) return false;
      if (seatState_ != other.seatState_) return false;
      if (seatClimateIntensity_ != other.seatClimateIntensity_) return false;
      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + SEAT_AREA_FIELD_NUMBER;
      hash = (53 * hash) + seatArea_;
      hash = (37 * hash) + SEAT_STATE_FIELD_NUMBER;
      hash = (53 * hash) + seatState_;
      hash = (37 * hash) + SEAT_CLIMATE_INTENSITY_FIELD_NUMBER;
      hash = (53 * hash) + seatClimateIntensity_;
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static climate_common.ClimateCommon.SeatClimateOperation parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static climate_common.ClimateCommon.SeatClimateOperation parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static climate_common.ClimateCommon.SeatClimateOperation parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static climate_common.ClimateCommon.SeatClimateOperation parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static climate_common.ClimateCommon.SeatClimateOperation parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static climate_common.ClimateCommon.SeatClimateOperation parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static climate_common.ClimateCommon.SeatClimateOperation parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input);
    }
    public static climate_common.ClimateCommon.SeatClimateOperation parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public static climate_common.ClimateCommon.SeatClimateOperation parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseDelimitedWithIOException(PARSER, input);
    }

    public static climate_common.ClimateCommon.SeatClimateOperation parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static climate_common.ClimateCommon.SeatClimateOperation parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input);
    }
    public static climate_common.ClimateCommon.SeatClimateOperation parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(climate_common.ClimateCommon.SeatClimateOperation prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     * This is a nested message contains seat area,seat state and seat intensity.
     * </pre>
     *
     * Protobuf type {@code climate_common.SeatClimateOperation}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:climate_common.SeatClimateOperation)
        climate_common.ClimateCommon.SeatClimateOperationOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return climate_common.ClimateCommon.internal_static_climate_common_SeatClimateOperation_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return climate_common.ClimateCommon.internal_static_climate_common_SeatClimateOperation_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                climate_common.ClimateCommon.SeatClimateOperation.class, climate_common.ClimateCommon.SeatClimateOperation.Builder.class);
      }

      // Construct using climate_common.ClimateCommon.SeatClimateOperation.newBuilder()
      private Builder() {

      }

      private Builder(
          com.google.protobuf.GeneratedMessage.BuilderParent parent) {
        super(parent);

      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        bitField0_ = 0;
        seatArea_ = 0;
        seatState_ = 0;
        seatClimateIntensity_ = 0;
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return climate_common.ClimateCommon.internal_static_climate_common_SeatClimateOperation_descriptor;
      }

      @java.lang.Override
      public climate_common.ClimateCommon.SeatClimateOperation getDefaultInstanceForType() {
        return climate_common.ClimateCommon.SeatClimateOperation.getDefaultInstance();
      }

      @java.lang.Override
      public climate_common.ClimateCommon.SeatClimateOperation build() {
        climate_common.ClimateCommon.SeatClimateOperation result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public climate_common.ClimateCommon.SeatClimateOperation buildPartial() {
        climate_common.ClimateCommon.SeatClimateOperation result = new climate_common.ClimateCommon.SeatClimateOperation(this);
        if (bitField0_ != 0) { buildPartial0(result); }
        onBuilt();
        return result;
      }

      private void buildPartial0(climate_common.ClimateCommon.SeatClimateOperation result) {
        int from_bitField0_ = bitField0_;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.seatArea_ = seatArea_;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          result.seatState_ = seatState_;
        }
        if (((from_bitField0_ & 0x00000004) != 0)) {
          result.seatClimateIntensity_ = seatClimateIntensity_;
        }
      }

      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof climate_common.ClimateCommon.SeatClimateOperation) {
          return mergeFrom((climate_common.ClimateCommon.SeatClimateOperation)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(climate_common.ClimateCommon.SeatClimateOperation other) {
        if (other == climate_common.ClimateCommon.SeatClimateOperation.getDefaultInstance()) return this;
        if (other.seatArea_ != 0) {
          setSeatAreaValue(other.getSeatAreaValue());
        }
        if (other.seatState_ != 0) {
          setSeatStateValue(other.getSeatStateValue());
        }
        if (other.seatClimateIntensity_ != 0) {
          setSeatClimateIntensityValue(other.getSeatClimateIntensityValue());
        }
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 8: {
                seatArea_ = input.readEnum();
                bitField0_ |= 0x00000001;
                break;
              } // case 8
              case 16: {
                seatState_ = input.readEnum();
                bitField0_ |= 0x00000002;
                break;
              } // case 16
              case 24: {
                seatClimateIntensity_ = input.readEnum();
                bitField0_ |= 0x00000004;
                break;
              } // case 24
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }
      private int bitField0_;

      private int seatArea_ = 0;
      /**
       * <code>.climate_common.EnumSeatClimateArea seat_area = 1;</code>
       * @return The enum numeric value on the wire for seatArea.
       */
      @java.lang.Override public int getSeatAreaValue() {
        return seatArea_;
      }
      /**
       * <code>.climate_common.EnumSeatClimateArea seat_area = 1;</code>
       * @param value The enum numeric value on the wire for seatArea to set.
       * @return This builder for chaining.
       */
      public Builder setSeatAreaValue(int value) {
        seatArea_ = value;
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }
      /**
       * <code>.climate_common.EnumSeatClimateArea seat_area = 1;</code>
       * @return The seatArea.
       */
      @java.lang.Override
      public climate_common.ClimateCommon.EnumSeatClimateArea getSeatArea() {
        climate_common.ClimateCommon.EnumSeatClimateArea result = climate_common.ClimateCommon.EnumSeatClimateArea.forNumber(seatArea_);
        return result == null ? climate_common.ClimateCommon.EnumSeatClimateArea.UNRECOGNIZED : result;
      }
      /**
       * <code>.climate_common.EnumSeatClimateArea seat_area = 1;</code>
       * @param value The seatArea to set.
       * @return This builder for chaining.
       */
      public Builder setSeatArea(climate_common.ClimateCommon.EnumSeatClimateArea value) {
        if (value == null) {
          throw new NullPointerException();
        }
        bitField0_ |= 0x00000001;
        seatArea_ = value.getNumber();
        onChanged();
        return this;
      }
      /**
       * <code>.climate_common.EnumSeatClimateArea seat_area = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearSeatArea() {
        bitField0_ = (bitField0_ & ~0x00000001);
        seatArea_ = 0;
        onChanged();
        return this;
      }

      private int seatState_ = 0;
      /**
       * <code>.climate_common.EnumSeatClimateState seat_state = 2;</code>
       * @return The enum numeric value on the wire for seatState.
       */
      @java.lang.Override public int getSeatStateValue() {
        return seatState_;
      }
      /**
       * <code>.climate_common.EnumSeatClimateState seat_state = 2;</code>
       * @param value The enum numeric value on the wire for seatState to set.
       * @return This builder for chaining.
       */
      public Builder setSeatStateValue(int value) {
        seatState_ = value;
        bitField0_ |= 0x00000002;
        onChanged();
        return this;
      }
      /**
       * <code>.climate_common.EnumSeatClimateState seat_state = 2;</code>
       * @return The seatState.
       */
      @java.lang.Override
      public climate_common.ClimateCommon.EnumSeatClimateState getSeatState() {
        climate_common.ClimateCommon.EnumSeatClimateState result = climate_common.ClimateCommon.EnumSeatClimateState.forNumber(seatState_);
        return result == null ? climate_common.ClimateCommon.EnumSeatClimateState.UNRECOGNIZED : result;
      }
      /**
       * <code>.climate_common.EnumSeatClimateState seat_state = 2;</code>
       * @param value The seatState to set.
       * @return This builder for chaining.
       */
      public Builder setSeatState(climate_common.ClimateCommon.EnumSeatClimateState value) {
        if (value == null) {
          throw new NullPointerException();
        }
        bitField0_ |= 0x00000002;
        seatState_ = value.getNumber();
        onChanged();
        return this;
      }
      /**
       * <code>.climate_common.EnumSeatClimateState seat_state = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearSeatState() {
        bitField0_ = (bitField0_ & ~0x00000002);
        seatState_ = 0;
        onChanged();
        return this;
      }

      private int seatClimateIntensity_ = 0;
      /**
       * <code>.climate_common.EnumSeatClimateIntensity seat_climate_intensity = 3;</code>
       * @return The enum numeric value on the wire for seatClimateIntensity.
       */
      @java.lang.Override public int getSeatClimateIntensityValue() {
        return seatClimateIntensity_;
      }
      /**
       * <code>.climate_common.EnumSeatClimateIntensity seat_climate_intensity = 3;</code>
       * @param value The enum numeric value on the wire for seatClimateIntensity to set.
       * @return This builder for chaining.
       */
      public Builder setSeatClimateIntensityValue(int value) {
        seatClimateIntensity_ = value;
        bitField0_ |= 0x00000004;
        onChanged();
        return this;
      }
      /**
       * <code>.climate_common.EnumSeatClimateIntensity seat_climate_intensity = 3;</code>
       * @return The seatClimateIntensity.
       */
      @java.lang.Override
      public climate_common.ClimateCommon.EnumSeatClimateIntensity getSeatClimateIntensity() {
        climate_common.ClimateCommon.EnumSeatClimateIntensity result = climate_common.ClimateCommon.EnumSeatClimateIntensity.forNumber(seatClimateIntensity_);
        return result == null ? climate_common.ClimateCommon.EnumSeatClimateIntensity.UNRECOGNIZED : result;
      }
      /**
       * <code>.climate_common.EnumSeatClimateIntensity seat_climate_intensity = 3;</code>
       * @param value The seatClimateIntensity to set.
       * @return This builder for chaining.
       */
      public Builder setSeatClimateIntensity(climate_common.ClimateCommon.EnumSeatClimateIntensity value) {
        if (value == null) {
          throw new NullPointerException();
        }
        bitField0_ |= 0x00000004;
        seatClimateIntensity_ = value.getNumber();
        onChanged();
        return this;
      }
      /**
       * <code>.climate_common.EnumSeatClimateIntensity seat_climate_intensity = 3;</code>
       * @return This builder for chaining.
       */
      public Builder clearSeatClimateIntensity() {
        bitField0_ = (bitField0_ & ~0x00000004);
        seatClimateIntensity_ = 0;
        onChanged();
        return this;
      }

      // @@protoc_insertion_point(builder_scope:climate_common.SeatClimateOperation)
    }

    // @@protoc_insertion_point(class_scope:climate_common.SeatClimateOperation)
    private static final climate_common.ClimateCommon.SeatClimateOperation DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new climate_common.ClimateCommon.SeatClimateOperation();
    }

    public static climate_common.ClimateCommon.SeatClimateOperation getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<SeatClimateOperation>
        PARSER = new com.google.protobuf.AbstractParser<SeatClimateOperation>() {
      @java.lang.Override
      public SeatClimateOperation parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<SeatClimateOperation> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<SeatClimateOperation> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public climate_common.ClimateCommon.SeatClimateOperation getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface SeatClimateZoneStateOrBuilder extends
      // @@protoc_insertion_point(interface_extends:climate_common.SeatClimateZoneState)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>.seats_common.EnumSeatSelection seat_selection = 1;</code>
     * @return The enum numeric value on the wire for seatSelection.
     */
    int getSeatSelectionValue();
    /**
     * <code>.seats_common.EnumSeatSelection seat_selection = 1;</code>
     * @return The seatSelection.
     */
    seats_common.SeatsCommon.EnumSeatSelection getSeatSelection();

    /**
     * <code>repeated .climate_common.SeatClimateOperation seat_operation = 2;</code>
     */
    java.util.List<climate_common.ClimateCommon.SeatClimateOperation> 
        getSeatOperationList();
    /**
     * <code>repeated .climate_common.SeatClimateOperation seat_operation = 2;</code>
     */
    climate_common.ClimateCommon.SeatClimateOperation getSeatOperation(int index);
    /**
     * <code>repeated .climate_common.SeatClimateOperation seat_operation = 2;</code>
     */
    int getSeatOperationCount();
    /**
     * <code>repeated .climate_common.SeatClimateOperation seat_operation = 2;</code>
     */
    java.util.List<? extends climate_common.ClimateCommon.SeatClimateOperationOrBuilder> 
        getSeatOperationOrBuilderList();
    /**
     * <code>repeated .climate_common.SeatClimateOperation seat_operation = 2;</code>
     */
    climate_common.ClimateCommon.SeatClimateOperationOrBuilder getSeatOperationOrBuilder(
        int index);

    /**
     * <code>optional .climate_common.EnumSeatClimateIntensity seat_climate_intensity = 3;</code>
     * @return Whether the seatClimateIntensity field is set.
     */
    boolean hasSeatClimateIntensity();
    /**
     * <code>optional .climate_common.EnumSeatClimateIntensity seat_climate_intensity = 3;</code>
     * @return The enum numeric value on the wire for seatClimateIntensity.
     */
    int getSeatClimateIntensityValue();
    /**
     * <code>optional .climate_common.EnumSeatClimateIntensity seat_climate_intensity = 3;</code>
     * @return The seatClimateIntensity.
     */
    climate_common.ClimateCommon.EnumSeatClimateIntensity getSeatClimateIntensity();
  }
  /**
   * <pre>
   * This is a nested message allows the application to specify seat state for particular seat selection
   * </pre>
   *
   * Protobuf type {@code climate_common.SeatClimateZoneState}
   */
  public static final class SeatClimateZoneState extends
      com.google.protobuf.GeneratedMessage implements
      // @@protoc_insertion_point(message_implements:climate_common.SeatClimateZoneState)
      SeatClimateZoneStateOrBuilder {
  private static final long serialVersionUID = 0L;
    static {
      com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
        com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
        /* major= */ 4,
        /* minor= */ 28,
        /* patch= */ 2,
        /* suffix= */ "",
        SeatClimateZoneState.class.getName());
    }
    // Use SeatClimateZoneState.newBuilder() to construct.
    private SeatClimateZoneState(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
    }
    private SeatClimateZoneState() {
      seatSelection_ = 0;
      seatOperation_ = java.util.Collections.emptyList();
      seatClimateIntensity_ = 0;
    }

    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return climate_common.ClimateCommon.internal_static_climate_common_SeatClimateZoneState_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return climate_common.ClimateCommon.internal_static_climate_common_SeatClimateZoneState_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              climate_common.ClimateCommon.SeatClimateZoneState.class, climate_common.ClimateCommon.SeatClimateZoneState.Builder.class);
    }

    private int bitField0_;
    public static final int SEAT_SELECTION_FIELD_NUMBER = 1;
    private int seatSelection_ = 0;
    /**
     * <code>.seats_common.EnumSeatSelection seat_selection = 1;</code>
     * @return The enum numeric value on the wire for seatSelection.
     */
    @java.lang.Override public int getSeatSelectionValue() {
      return seatSelection_;
    }
    /**
     * <code>.seats_common.EnumSeatSelection seat_selection = 1;</code>
     * @return The seatSelection.
     */
    @java.lang.Override public seats_common.SeatsCommon.EnumSeatSelection getSeatSelection() {
      seats_common.SeatsCommon.EnumSeatSelection result = seats_common.SeatsCommon.EnumSeatSelection.forNumber(seatSelection_);
      return result == null ? seats_common.SeatsCommon.EnumSeatSelection.UNRECOGNIZED : result;
    }

    public static final int SEAT_OPERATION_FIELD_NUMBER = 2;
    @SuppressWarnings("serial")
    private java.util.List<climate_common.ClimateCommon.SeatClimateOperation> seatOperation_;
    /**
     * <code>repeated .climate_common.SeatClimateOperation seat_operation = 2;</code>
     */
    @java.lang.Override
    public java.util.List<climate_common.ClimateCommon.SeatClimateOperation> getSeatOperationList() {
      return seatOperation_;
    }
    /**
     * <code>repeated .climate_common.SeatClimateOperation seat_operation = 2;</code>
     */
    @java.lang.Override
    public java.util.List<? extends climate_common.ClimateCommon.SeatClimateOperationOrBuilder> 
        getSeatOperationOrBuilderList() {
      return seatOperation_;
    }
    /**
     * <code>repeated .climate_common.SeatClimateOperation seat_operation = 2;</code>
     */
    @java.lang.Override
    public int getSeatOperationCount() {
      return seatOperation_.size();
    }
    /**
     * <code>repeated .climate_common.SeatClimateOperation seat_operation = 2;</code>
     */
    @java.lang.Override
    public climate_common.ClimateCommon.SeatClimateOperation getSeatOperation(int index) {
      return seatOperation_.get(index);
    }
    /**
     * <code>repeated .climate_common.SeatClimateOperation seat_operation = 2;</code>
     */
    @java.lang.Override
    public climate_common.ClimateCommon.SeatClimateOperationOrBuilder getSeatOperationOrBuilder(
        int index) {
      return seatOperation_.get(index);
    }

    public static final int SEAT_CLIMATE_INTENSITY_FIELD_NUMBER = 3;
    private int seatClimateIntensity_ = 0;
    /**
     * <code>optional .climate_common.EnumSeatClimateIntensity seat_climate_intensity = 3;</code>
     * @return Whether the seatClimateIntensity field is set.
     */
    @java.lang.Override public boolean hasSeatClimateIntensity() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>optional .climate_common.EnumSeatClimateIntensity seat_climate_intensity = 3;</code>
     * @return The enum numeric value on the wire for seatClimateIntensity.
     */
    @java.lang.Override public int getSeatClimateIntensityValue() {
      return seatClimateIntensity_;
    }
    /**
     * <code>optional .climate_common.EnumSeatClimateIntensity seat_climate_intensity = 3;</code>
     * @return The seatClimateIntensity.
     */
    @java.lang.Override public climate_common.ClimateCommon.EnumSeatClimateIntensity getSeatClimateIntensity() {
      climate_common.ClimateCommon.EnumSeatClimateIntensity result = climate_common.ClimateCommon.EnumSeatClimateIntensity.forNumber(seatClimateIntensity_);
      return result == null ? climate_common.ClimateCommon.EnumSeatClimateIntensity.UNRECOGNIZED : result;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (seatSelection_ != seats_common.SeatsCommon.EnumSeatSelection.ENUM_SEAT_SELECTION_UNSPECIFIED.getNumber()) {
        output.writeEnum(1, seatSelection_);
      }
      for (int i = 0; i < seatOperation_.size(); i++) {
        output.writeMessage(2, seatOperation_.get(i));
      }
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeEnum(3, seatClimateIntensity_);
      }
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (seatSelection_ != seats_common.SeatsCommon.EnumSeatSelection.ENUM_SEAT_SELECTION_UNSPECIFIED.getNumber()) {
        size += com.google.protobuf.CodedOutputStream
          .computeEnumSize(1, seatSelection_);
      }
      for (int i = 0; i < seatOperation_.size(); i++) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(2, seatOperation_.get(i));
      }
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeEnumSize(3, seatClimateIntensity_);
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof climate_common.ClimateCommon.SeatClimateZoneState)) {
        return super.equals(obj);
      }
      climate_common.ClimateCommon.SeatClimateZoneState other = (climate_common.ClimateCommon.SeatClimateZoneState) obj;

      if (seatSelection_ != other.seatSelection_) return false;
      if (!getSeatOperationList()
          .equals(other.getSeatOperationList())) return false;
      if (hasSeatClimateIntensity() != other.hasSeatClimateIntensity()) return false;
      if (hasSeatClimateIntensity()) {
        if (seatClimateIntensity_ != other.seatClimateIntensity_) return false;
      }
      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + SEAT_SELECTION_FIELD_NUMBER;
      hash = (53 * hash) + seatSelection_;
      if (getSeatOperationCount() > 0) {
        hash = (37 * hash) + SEAT_OPERATION_FIELD_NUMBER;
        hash = (53 * hash) + getSeatOperationList().hashCode();
      }
      if (hasSeatClimateIntensity()) {
        hash = (37 * hash) + SEAT_CLIMATE_INTENSITY_FIELD_NUMBER;
        hash = (53 * hash) + seatClimateIntensity_;
      }
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static climate_common.ClimateCommon.SeatClimateZoneState parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static climate_common.ClimateCommon.SeatClimateZoneState parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static climate_common.ClimateCommon.SeatClimateZoneState parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static climate_common.ClimateCommon.SeatClimateZoneState parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static climate_common.ClimateCommon.SeatClimateZoneState parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static climate_common.ClimateCommon.SeatClimateZoneState parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static climate_common.ClimateCommon.SeatClimateZoneState parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input);
    }
    public static climate_common.ClimateCommon.SeatClimateZoneState parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public static climate_common.ClimateCommon.SeatClimateZoneState parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseDelimitedWithIOException(PARSER, input);
    }

    public static climate_common.ClimateCommon.SeatClimateZoneState parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static climate_common.ClimateCommon.SeatClimateZoneState parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input);
    }
    public static climate_common.ClimateCommon.SeatClimateZoneState parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(climate_common.ClimateCommon.SeatClimateZoneState prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     * This is a nested message allows the application to specify seat state for particular seat selection
     * </pre>
     *
     * Protobuf type {@code climate_common.SeatClimateZoneState}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:climate_common.SeatClimateZoneState)
        climate_common.ClimateCommon.SeatClimateZoneStateOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return climate_common.ClimateCommon.internal_static_climate_common_SeatClimateZoneState_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return climate_common.ClimateCommon.internal_static_climate_common_SeatClimateZoneState_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                climate_common.ClimateCommon.SeatClimateZoneState.class, climate_common.ClimateCommon.SeatClimateZoneState.Builder.class);
      }

      // Construct using climate_common.ClimateCommon.SeatClimateZoneState.newBuilder()
      private Builder() {

      }

      private Builder(
          com.google.protobuf.GeneratedMessage.BuilderParent parent) {
        super(parent);

      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        bitField0_ = 0;
        seatSelection_ = 0;
        if (seatOperationBuilder_ == null) {
          seatOperation_ = java.util.Collections.emptyList();
        } else {
          seatOperation_ = null;
          seatOperationBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000002);
        seatClimateIntensity_ = 0;
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return climate_common.ClimateCommon.internal_static_climate_common_SeatClimateZoneState_descriptor;
      }

      @java.lang.Override
      public climate_common.ClimateCommon.SeatClimateZoneState getDefaultInstanceForType() {
        return climate_common.ClimateCommon.SeatClimateZoneState.getDefaultInstance();
      }

      @java.lang.Override
      public climate_common.ClimateCommon.SeatClimateZoneState build() {
        climate_common.ClimateCommon.SeatClimateZoneState result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public climate_common.ClimateCommon.SeatClimateZoneState buildPartial() {
        climate_common.ClimateCommon.SeatClimateZoneState result = new climate_common.ClimateCommon.SeatClimateZoneState(this);
        buildPartialRepeatedFields(result);
        if (bitField0_ != 0) { buildPartial0(result); }
        onBuilt();
        return result;
      }

      private void buildPartialRepeatedFields(climate_common.ClimateCommon.SeatClimateZoneState result) {
        if (seatOperationBuilder_ == null) {
          if (((bitField0_ & 0x00000002) != 0)) {
            seatOperation_ = java.util.Collections.unmodifiableList(seatOperation_);
            bitField0_ = (bitField0_ & ~0x00000002);
          }
          result.seatOperation_ = seatOperation_;
        } else {
          result.seatOperation_ = seatOperationBuilder_.build();
        }
      }

      private void buildPartial0(climate_common.ClimateCommon.SeatClimateZoneState result) {
        int from_bitField0_ = bitField0_;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.seatSelection_ = seatSelection_;
        }
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000004) != 0)) {
          result.seatClimateIntensity_ = seatClimateIntensity_;
          to_bitField0_ |= 0x00000001;
        }
        result.bitField0_ |= to_bitField0_;
      }

      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof climate_common.ClimateCommon.SeatClimateZoneState) {
          return mergeFrom((climate_common.ClimateCommon.SeatClimateZoneState)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(climate_common.ClimateCommon.SeatClimateZoneState other) {
        if (other == climate_common.ClimateCommon.SeatClimateZoneState.getDefaultInstance()) return this;
        if (other.seatSelection_ != 0) {
          setSeatSelectionValue(other.getSeatSelectionValue());
        }
        if (seatOperationBuilder_ == null) {
          if (!other.seatOperation_.isEmpty()) {
            if (seatOperation_.isEmpty()) {
              seatOperation_ = other.seatOperation_;
              bitField0_ = (bitField0_ & ~0x00000002);
            } else {
              ensureSeatOperationIsMutable();
              seatOperation_.addAll(other.seatOperation_);
            }
            onChanged();
          }
        } else {
          if (!other.seatOperation_.isEmpty()) {
            if (seatOperationBuilder_.isEmpty()) {
              seatOperationBuilder_.dispose();
              seatOperationBuilder_ = null;
              seatOperation_ = other.seatOperation_;
              bitField0_ = (bitField0_ & ~0x00000002);
              seatOperationBuilder_ = 
                com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders ?
                   getSeatOperationFieldBuilder() : null;
            } else {
              seatOperationBuilder_.addAllMessages(other.seatOperation_);
            }
          }
        }
        if (other.hasSeatClimateIntensity()) {
          setSeatClimateIntensity(other.getSeatClimateIntensity());
        }
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 8: {
                seatSelection_ = input.readEnum();
                bitField0_ |= 0x00000001;
                break;
              } // case 8
              case 18: {
                climate_common.ClimateCommon.SeatClimateOperation m =
                    input.readMessage(
                        climate_common.ClimateCommon.SeatClimateOperation.parser(),
                        extensionRegistry);
                if (seatOperationBuilder_ == null) {
                  ensureSeatOperationIsMutable();
                  seatOperation_.add(m);
                } else {
                  seatOperationBuilder_.addMessage(m);
                }
                break;
              } // case 18
              case 24: {
                seatClimateIntensity_ = input.readEnum();
                bitField0_ |= 0x00000004;
                break;
              } // case 24
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }
      private int bitField0_;

      private int seatSelection_ = 0;
      /**
       * <code>.seats_common.EnumSeatSelection seat_selection = 1;</code>
       * @return The enum numeric value on the wire for seatSelection.
       */
      @java.lang.Override public int getSeatSelectionValue() {
        return seatSelection_;
      }
      /**
       * <code>.seats_common.EnumSeatSelection seat_selection = 1;</code>
       * @param value The enum numeric value on the wire for seatSelection to set.
       * @return This builder for chaining.
       */
      public Builder setSeatSelectionValue(int value) {
        seatSelection_ = value;
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }
      /**
       * <code>.seats_common.EnumSeatSelection seat_selection = 1;</code>
       * @return The seatSelection.
       */
      @java.lang.Override
      public seats_common.SeatsCommon.EnumSeatSelection getSeatSelection() {
        seats_common.SeatsCommon.EnumSeatSelection result = seats_common.SeatsCommon.EnumSeatSelection.forNumber(seatSelection_);
        return result == null ? seats_common.SeatsCommon.EnumSeatSelection.UNRECOGNIZED : result;
      }
      /**
       * <code>.seats_common.EnumSeatSelection seat_selection = 1;</code>
       * @param value The seatSelection to set.
       * @return This builder for chaining.
       */
      public Builder setSeatSelection(seats_common.SeatsCommon.EnumSeatSelection value) {
        if (value == null) {
          throw new NullPointerException();
        }
        bitField0_ |= 0x00000001;
        seatSelection_ = value.getNumber();
        onChanged();
        return this;
      }
      /**
       * <code>.seats_common.EnumSeatSelection seat_selection = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearSeatSelection() {
        bitField0_ = (bitField0_ & ~0x00000001);
        seatSelection_ = 0;
        onChanged();
        return this;
      }

      private java.util.List<climate_common.ClimateCommon.SeatClimateOperation> seatOperation_ =
        java.util.Collections.emptyList();
      private void ensureSeatOperationIsMutable() {
        if (!((bitField0_ & 0x00000002) != 0)) {
          seatOperation_ = new java.util.ArrayList<climate_common.ClimateCommon.SeatClimateOperation>(seatOperation_);
          bitField0_ |= 0x00000002;
         }
      }

      private com.google.protobuf.RepeatedFieldBuilder<
          climate_common.ClimateCommon.SeatClimateOperation, climate_common.ClimateCommon.SeatClimateOperation.Builder, climate_common.ClimateCommon.SeatClimateOperationOrBuilder> seatOperationBuilder_;

      /**
       * <code>repeated .climate_common.SeatClimateOperation seat_operation = 2;</code>
       */
      public java.util.List<climate_common.ClimateCommon.SeatClimateOperation> getSeatOperationList() {
        if (seatOperationBuilder_ == null) {
          return java.util.Collections.unmodifiableList(seatOperation_);
        } else {
          return seatOperationBuilder_.getMessageList();
        }
      }
      /**
       * <code>repeated .climate_common.SeatClimateOperation seat_operation = 2;</code>
       */
      public int getSeatOperationCount() {
        if (seatOperationBuilder_ == null) {
          return seatOperation_.size();
        } else {
          return seatOperationBuilder_.getCount();
        }
      }
      /**
       * <code>repeated .climate_common.SeatClimateOperation seat_operation = 2;</code>
       */
      public climate_common.ClimateCommon.SeatClimateOperation getSeatOperation(int index) {
        if (seatOperationBuilder_ == null) {
          return seatOperation_.get(index);
        } else {
          return seatOperationBuilder_.getMessage(index);
        }
      }
      /**
       * <code>repeated .climate_common.SeatClimateOperation seat_operation = 2;</code>
       */
      public Builder setSeatOperation(
          int index, climate_common.ClimateCommon.SeatClimateOperation value) {
        if (seatOperationBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureSeatOperationIsMutable();
          seatOperation_.set(index, value);
          onChanged();
        } else {
          seatOperationBuilder_.setMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .climate_common.SeatClimateOperation seat_operation = 2;</code>
       */
      public Builder setSeatOperation(
          int index, climate_common.ClimateCommon.SeatClimateOperation.Builder builderForValue) {
        if (seatOperationBuilder_ == null) {
          ensureSeatOperationIsMutable();
          seatOperation_.set(index, builderForValue.build());
          onChanged();
        } else {
          seatOperationBuilder_.setMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .climate_common.SeatClimateOperation seat_operation = 2;</code>
       */
      public Builder addSeatOperation(climate_common.ClimateCommon.SeatClimateOperation value) {
        if (seatOperationBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureSeatOperationIsMutable();
          seatOperation_.add(value);
          onChanged();
        } else {
          seatOperationBuilder_.addMessage(value);
        }
        return this;
      }
      /**
       * <code>repeated .climate_common.SeatClimateOperation seat_operation = 2;</code>
       */
      public Builder addSeatOperation(
          int index, climate_common.ClimateCommon.SeatClimateOperation value) {
        if (seatOperationBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureSeatOperationIsMutable();
          seatOperation_.add(index, value);
          onChanged();
        } else {
          seatOperationBuilder_.addMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .climate_common.SeatClimateOperation seat_operation = 2;</code>
       */
      public Builder addSeatOperation(
          climate_common.ClimateCommon.SeatClimateOperation.Builder builderForValue) {
        if (seatOperationBuilder_ == null) {
          ensureSeatOperationIsMutable();
          seatOperation_.add(builderForValue.build());
          onChanged();
        } else {
          seatOperationBuilder_.addMessage(builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .climate_common.SeatClimateOperation seat_operation = 2;</code>
       */
      public Builder addSeatOperation(
          int index, climate_common.ClimateCommon.SeatClimateOperation.Builder builderForValue) {
        if (seatOperationBuilder_ == null) {
          ensureSeatOperationIsMutable();
          seatOperation_.add(index, builderForValue.build());
          onChanged();
        } else {
          seatOperationBuilder_.addMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .climate_common.SeatClimateOperation seat_operation = 2;</code>
       */
      public Builder addAllSeatOperation(
          java.lang.Iterable<? extends climate_common.ClimateCommon.SeatClimateOperation> values) {
        if (seatOperationBuilder_ == null) {
          ensureSeatOperationIsMutable();
          com.google.protobuf.AbstractMessageLite.Builder.addAll(
              values, seatOperation_);
          onChanged();
        } else {
          seatOperationBuilder_.addAllMessages(values);
        }
        return this;
      }
      /**
       * <code>repeated .climate_common.SeatClimateOperation seat_operation = 2;</code>
       */
      public Builder clearSeatOperation() {
        if (seatOperationBuilder_ == null) {
          seatOperation_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000002);
          onChanged();
        } else {
          seatOperationBuilder_.clear();
        }
        return this;
      }
      /**
       * <code>repeated .climate_common.SeatClimateOperation seat_operation = 2;</code>
       */
      public Builder removeSeatOperation(int index) {
        if (seatOperationBuilder_ == null) {
          ensureSeatOperationIsMutable();
          seatOperation_.remove(index);
          onChanged();
        } else {
          seatOperationBuilder_.remove(index);
        }
        return this;
      }
      /**
       * <code>repeated .climate_common.SeatClimateOperation seat_operation = 2;</code>
       */
      public climate_common.ClimateCommon.SeatClimateOperation.Builder getSeatOperationBuilder(
          int index) {
        return getSeatOperationFieldBuilder().getBuilder(index);
      }
      /**
       * <code>repeated .climate_common.SeatClimateOperation seat_operation = 2;</code>
       */
      public climate_common.ClimateCommon.SeatClimateOperationOrBuilder getSeatOperationOrBuilder(
          int index) {
        if (seatOperationBuilder_ == null) {
          return seatOperation_.get(index);  } else {
          return seatOperationBuilder_.getMessageOrBuilder(index);
        }
      }
      /**
       * <code>repeated .climate_common.SeatClimateOperation seat_operation = 2;</code>
       */
      public java.util.List<? extends climate_common.ClimateCommon.SeatClimateOperationOrBuilder> 
           getSeatOperationOrBuilderList() {
        if (seatOperationBuilder_ != null) {
          return seatOperationBuilder_.getMessageOrBuilderList();
        } else {
          return java.util.Collections.unmodifiableList(seatOperation_);
        }
      }
      /**
       * <code>repeated .climate_common.SeatClimateOperation seat_operation = 2;</code>
       */
      public climate_common.ClimateCommon.SeatClimateOperation.Builder addSeatOperationBuilder() {
        return getSeatOperationFieldBuilder().addBuilder(
            climate_common.ClimateCommon.SeatClimateOperation.getDefaultInstance());
      }
      /**
       * <code>repeated .climate_common.SeatClimateOperation seat_operation = 2;</code>
       */
      public climate_common.ClimateCommon.SeatClimateOperation.Builder addSeatOperationBuilder(
          int index) {
        return getSeatOperationFieldBuilder().addBuilder(
            index, climate_common.ClimateCommon.SeatClimateOperation.getDefaultInstance());
      }
      /**
       * <code>repeated .climate_common.SeatClimateOperation seat_operation = 2;</code>
       */
      public java.util.List<climate_common.ClimateCommon.SeatClimateOperation.Builder> 
           getSeatOperationBuilderList() {
        return getSeatOperationFieldBuilder().getBuilderList();
      }
      private com.google.protobuf.RepeatedFieldBuilder<
          climate_common.ClimateCommon.SeatClimateOperation, climate_common.ClimateCommon.SeatClimateOperation.Builder, climate_common.ClimateCommon.SeatClimateOperationOrBuilder> 
          getSeatOperationFieldBuilder() {
        if (seatOperationBuilder_ == null) {
          seatOperationBuilder_ = new com.google.protobuf.RepeatedFieldBuilder<
              climate_common.ClimateCommon.SeatClimateOperation, climate_common.ClimateCommon.SeatClimateOperation.Builder, climate_common.ClimateCommon.SeatClimateOperationOrBuilder>(
                  seatOperation_,
                  ((bitField0_ & 0x00000002) != 0),
                  getParentForChildren(),
                  isClean());
          seatOperation_ = null;
        }
        return seatOperationBuilder_;
      }

      private int seatClimateIntensity_ = 0;
      /**
       * <code>optional .climate_common.EnumSeatClimateIntensity seat_climate_intensity = 3;</code>
       * @return Whether the seatClimateIntensity field is set.
       */
      @java.lang.Override public boolean hasSeatClimateIntensity() {
        return ((bitField0_ & 0x00000004) != 0);
      }
      /**
       * <code>optional .climate_common.EnumSeatClimateIntensity seat_climate_intensity = 3;</code>
       * @return The enum numeric value on the wire for seatClimateIntensity.
       */
      @java.lang.Override public int getSeatClimateIntensityValue() {
        return seatClimateIntensity_;
      }
      /**
       * <code>optional .climate_common.EnumSeatClimateIntensity seat_climate_intensity = 3;</code>
       * @param value The enum numeric value on the wire for seatClimateIntensity to set.
       * @return This builder for chaining.
       */
      public Builder setSeatClimateIntensityValue(int value) {
        seatClimateIntensity_ = value;
        bitField0_ |= 0x00000004;
        onChanged();
        return this;
      }
      /**
       * <code>optional .climate_common.EnumSeatClimateIntensity seat_climate_intensity = 3;</code>
       * @return The seatClimateIntensity.
       */
      @java.lang.Override
      public climate_common.ClimateCommon.EnumSeatClimateIntensity getSeatClimateIntensity() {
        climate_common.ClimateCommon.EnumSeatClimateIntensity result = climate_common.ClimateCommon.EnumSeatClimateIntensity.forNumber(seatClimateIntensity_);
        return result == null ? climate_common.ClimateCommon.EnumSeatClimateIntensity.UNRECOGNIZED : result;
      }
      /**
       * <code>optional .climate_common.EnumSeatClimateIntensity seat_climate_intensity = 3;</code>
       * @param value The seatClimateIntensity to set.
       * @return This builder for chaining.
       */
      public Builder setSeatClimateIntensity(climate_common.ClimateCommon.EnumSeatClimateIntensity value) {
        if (value == null) {
          throw new NullPointerException();
        }
        bitField0_ |= 0x00000004;
        seatClimateIntensity_ = value.getNumber();
        onChanged();
        return this;
      }
      /**
       * <code>optional .climate_common.EnumSeatClimateIntensity seat_climate_intensity = 3;</code>
       * @return This builder for chaining.
       */
      public Builder clearSeatClimateIntensity() {
        bitField0_ = (bitField0_ & ~0x00000004);
        seatClimateIntensity_ = 0;
        onChanged();
        return this;
      }

      // @@protoc_insertion_point(builder_scope:climate_common.SeatClimateZoneState)
    }

    // @@protoc_insertion_point(class_scope:climate_common.SeatClimateZoneState)
    private static final climate_common.ClimateCommon.SeatClimateZoneState DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new climate_common.ClimateCommon.SeatClimateZoneState();
    }

    public static climate_common.ClimateCommon.SeatClimateZoneState getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<SeatClimateZoneState>
        PARSER = new com.google.protobuf.AbstractParser<SeatClimateZoneState>() {
      @java.lang.Override
      public SeatClimateZoneState parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<SeatClimateZoneState> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<SeatClimateZoneState> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public climate_common.ClimateCommon.SeatClimateZoneState getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_climate_common_SeatClimateOperation_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_climate_common_SeatClimateOperation_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_climate_common_SeatClimateZoneState_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_climate_common_SeatClimateZoneState_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n\024climate_common.proto\022\016climate_common\032\022" +
      "seats_common.proto\"\322\001\n\024SeatClimateOperat" +
      "ion\0226\n\tseat_area\030\001 \001(\0162#.climate_common." +
      "EnumSeatClimateArea\0228\n\nseat_state\030\002 \001(\0162" +
      "$.climate_common.EnumSeatClimateState\022H\n" +
      "\026seat_climate_intensity\030\003 \001(\0162(.climate_" +
      "common.EnumSeatClimateIntensity\"\367\001\n\024Seat" +
      "ClimateZoneState\0227\n\016seat_selection\030\001 \001(\016" +
      "2\037.seats_common.EnumSeatSelection\022<\n\016sea" +
      "t_operation\030\002 \003(\0132$.climate_common.SeatC" +
      "limateOperation\022M\n\026seat_climate_intensit" +
      "y\030\003 \001(\0162(.climate_common.EnumSeatClimate" +
      "IntensityH\000\210\001\001B\031\n\027_seat_climate_intensit" +
      "y*\270\004\n\030EnumSeatClimateIntensity\022+\n\'ENUM_S" +
      "EAT_CLIMATE_INTENSITY_UNSPECIFIED\020\000\022#\n\037E" +
      "NUM_SEAT_CLIMATE_INTENSITY_OFF\020\001\022,\n(ENUM" +
      "_SEAT_CLIMATE_INTENSITY_HEAT_LEVEL_1\020\002\022," +
      "\n(ENUM_SEAT_CLIMATE_INTENSITY_COOL_LEVEL" +
      "_1\020\003\022,\n(ENUM_SEAT_CLIMATE_INTENSITY_HEAT" +
      "_LEVEL_2\020\004\022,\n(ENUM_SEAT_CLIMATE_INTENSIT" +
      "Y_COOL_LEVEL_2\020\005\022,\n(ENUM_SEAT_CLIMATE_IN" +
      "TENSITY_HEAT_LEVEL_3\020\006\022,\n(ENUM_SEAT_CLIM" +
      "ATE_INTENSITY_COOL_LEVEL_3\020\007\022,\n(ENUM_SEA" +
      "T_CLIMATE_INTENSITY_HEAT_LEVEL_4\020\010\022,\n(EN" +
      "UM_SEAT_CLIMATE_INTENSITY_COOL_LEVEL_4\020\t" +
      "\022,\n(ENUM_SEAT_CLIMATE_INTENSITY_HEAT_LEV" +
      "EL_5\020\n\022,\n(ENUM_SEAT_CLIMATE_INTENSITY_CO" +
      "OL_LEVEL_5\020\013*\305\002\n\023EnumSeatClimateArea\022&\n\"" +
      "ENUM_SEAT_CLIMATE_AREA_UNSPECIFIED\020\000\022\036\n\032" +
      "ENUM_SEAT_CLIMATE_AREA_ALL\020\001\022\"\n\036ENUM_SEA" +
      "T_CLIMATE_AREA_CUSHION\020\002\022 \n\034ENUM_SEAT_CL" +
      "IMATE_AREA_SQUAB\020\003\022(\n$ENUM_SEAT_CLIMATE_" +
      "AREA_DOOR_ARM_REST\020\004\022*\n&ENUM_SEAT_CLIMAT" +
      "E_AREA_CENTER_ARM_REST\020\005\022$\n ENUM_SEAT_CL" +
      "IMATE_AREA_CALF_REST\020\006\022$\n ENUM_SEAT_CLIM" +
      "ATE_AREA_FOOT_REST\020\007*\245\001\n\024EnumSeatClimate" +
      "State\022\'\n#ENUM_SEAT_CLIMATE_STATE_UNSPECI" +
      "FIED\020\000\022\036\n\032ENUM_SEAT_CLIMATE_STATE_ON\020\001\022\037" +
      "\n\033ENUM_SEAT_CLIMATE_STATE_OFF\020\002\022#\n\037ENUM_" +
      "SEAT_CLIMATE_STATE_INHIBIT\020\003*\255\001\n\027EnumHSW" +
      "TemperatureLevel\022*\n&ENUM_HSW_TEMPERATURE" +
      "_LEVEL_UNSPECIFIED\020\000\022\"\n\036ENUM_HSW_TEMPERA" +
      "TURE_LEVEL_OFF\020\001\022 \n\034ENUM_HSW_TEMPERATURE" +
      "_LEVEL_1\020\002\022 \n\034ENUM_HSW_TEMPERATURE_LEVEL" +
      "_2\020\003b\006proto3"
    };
    descriptor = com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
          seats_common.SeatsCommon.getDescriptor(),
        });
    internal_static_climate_common_SeatClimateOperation_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_climate_common_SeatClimateOperation_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessage.FieldAccessorTable(
        internal_static_climate_common_SeatClimateOperation_descriptor,
        new java.lang.String[] { "SeatArea", "SeatState", "SeatClimateIntensity", });
    internal_static_climate_common_SeatClimateZoneState_descriptor =
      getDescriptor().getMessageTypes().get(1);
    internal_static_climate_common_SeatClimateZoneState_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessage.FieldAccessorTable(
        internal_static_climate_common_SeatClimateZoneState_descriptor,
        new java.lang.String[] { "SeatSelection", "SeatOperation", "SeatClimateIntensity", });
    descriptor.resolveAllFeaturesImmutable();
    seats_common.SeatsCommon.getDescriptor();
  }

  // @@protoc_insertion_point(outer_class_scope)
}
