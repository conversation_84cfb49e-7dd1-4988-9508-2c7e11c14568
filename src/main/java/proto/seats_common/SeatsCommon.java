// Generated by the protocol buffer compiler.  DO NOT EDIT!
// NO CHECKED-IN PROTOBUF GENCODE
// source: seats_common.proto
// Protobuf Java Version: 4.28.2

package seats_common;

public final class SeatsCommon {
  private SeatsCommon() {}
  static {
    com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
      com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
      /* major= */ 4,
      /* minor= */ 28,
      /* patch= */ 2,
      /* suffix= */ "",
      SeatsCommon.class.getName());
  }
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  /**
   * <pre>
   * Following enumeration specifies the seat location
   * </pre>
   *
   * Protobuf enum {@code seats_common.EnumSeatSelection}
   */
  public enum EnumSeatSelection
      implements com.google.protobuf.ProtocolMessageEnum {
    /**
     * <code>ENUM_SEAT_SELECTION_UNSPECIFIED = 0;</code>
     */
    ENUM_SEAT_SELECTION_UNSPECIFIED(0),
    /**
     * <code>ENUM_SEAT_SELECTION_ALL = 1;</code>
     */
    ENUM_SEAT_SELECTION_ALL(1),
    /**
     * <code>ENUM_SEAT_SELECTION_FIRST_ROW_LEFT = 2;</code>
     */
    ENUM_SEAT_SELECTION_FIRST_ROW_LEFT(2),
    /**
     * <code>ENUM_SEAT_SELECTION_FIRST_ROW_RIGHT = 3;</code>
     */
    ENUM_SEAT_SELECTION_FIRST_ROW_RIGHT(3),
    /**
     * <code>ENUM_SEAT_SELECTION_FIRST_ROW_CENTRE = 4;</code>
     */
    ENUM_SEAT_SELECTION_FIRST_ROW_CENTRE(4),
    /**
     * <code>ENUM_SEAT_SELECTION_FIRST_ROW_ALL = 5;</code>
     */
    ENUM_SEAT_SELECTION_FIRST_ROW_ALL(5),
    /**
     * <code>ENUM_SEAT_SELECTION_SECOND_ROW_LEFT = 6;</code>
     */
    ENUM_SEAT_SELECTION_SECOND_ROW_LEFT(6),
    /**
     * <code>ENUM_SEAT_SELECTION_SECOND_ROW_RIGHT = 7;</code>
     */
    ENUM_SEAT_SELECTION_SECOND_ROW_RIGHT(7),
    /**
     * <code>ENUM_SEAT_SELECTION_SECOND_ROW_CENTRE = 8;</code>
     */
    ENUM_SEAT_SELECTION_SECOND_ROW_CENTRE(8),
    /**
     * <code>ENUM_SEAT_SELECTION_SECOND_ROW_ALL = 9;</code>
     */
    ENUM_SEAT_SELECTION_SECOND_ROW_ALL(9),
    /**
     * <code>ENUM_SEAT_SELECTION_THIRD_ROW_LEFT = 10;</code>
     */
    ENUM_SEAT_SELECTION_THIRD_ROW_LEFT(10),
    /**
     * <code>ENUM_SEAT_SELECTION_THIRD_ROW_RIGHT = 11;</code>
     */
    ENUM_SEAT_SELECTION_THIRD_ROW_RIGHT(11),
    /**
     * <code>ENUM_SEAT_SELECTION_THIRD_ROW_CENTRE = 12;</code>
     */
    ENUM_SEAT_SELECTION_THIRD_ROW_CENTRE(12),
    /**
     * <code>ENUM_SEAT_SELECTION_THIRD_ROW_ALL = 13;</code>
     */
    ENUM_SEAT_SELECTION_THIRD_ROW_ALL(13),
    /**
     * <code>ENUM_SEAT_SELECTION_REAR_ALL = 14;</code>
     */
    ENUM_SEAT_SELECTION_REAR_ALL(14),
    UNRECOGNIZED(-1),
    ;

    static {
      com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
        com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
        /* major= */ 4,
        /* minor= */ 28,
        /* patch= */ 2,
        /* suffix= */ "",
        EnumSeatSelection.class.getName());
    }
    /**
     * <code>ENUM_SEAT_SELECTION_UNSPECIFIED = 0;</code>
     */
    public static final int ENUM_SEAT_SELECTION_UNSPECIFIED_VALUE = 0;
    /**
     * <code>ENUM_SEAT_SELECTION_ALL = 1;</code>
     */
    public static final int ENUM_SEAT_SELECTION_ALL_VALUE = 1;
    /**
     * <code>ENUM_SEAT_SELECTION_FIRST_ROW_LEFT = 2;</code>
     */
    public static final int ENUM_SEAT_SELECTION_FIRST_ROW_LEFT_VALUE = 2;
    /**
     * <code>ENUM_SEAT_SELECTION_FIRST_ROW_RIGHT = 3;</code>
     */
    public static final int ENUM_SEAT_SELECTION_FIRST_ROW_RIGHT_VALUE = 3;
    /**
     * <code>ENUM_SEAT_SELECTION_FIRST_ROW_CENTRE = 4;</code>
     */
    public static final int ENUM_SEAT_SELECTION_FIRST_ROW_CENTRE_VALUE = 4;
    /**
     * <code>ENUM_SEAT_SELECTION_FIRST_ROW_ALL = 5;</code>
     */
    public static final int ENUM_SEAT_SELECTION_FIRST_ROW_ALL_VALUE = 5;
    /**
     * <code>ENUM_SEAT_SELECTION_SECOND_ROW_LEFT = 6;</code>
     */
    public static final int ENUM_SEAT_SELECTION_SECOND_ROW_LEFT_VALUE = 6;
    /**
     * <code>ENUM_SEAT_SELECTION_SECOND_ROW_RIGHT = 7;</code>
     */
    public static final int ENUM_SEAT_SELECTION_SECOND_ROW_RIGHT_VALUE = 7;
    /**
     * <code>ENUM_SEAT_SELECTION_SECOND_ROW_CENTRE = 8;</code>
     */
    public static final int ENUM_SEAT_SELECTION_SECOND_ROW_CENTRE_VALUE = 8;
    /**
     * <code>ENUM_SEAT_SELECTION_SECOND_ROW_ALL = 9;</code>
     */
    public static final int ENUM_SEAT_SELECTION_SECOND_ROW_ALL_VALUE = 9;
    /**
     * <code>ENUM_SEAT_SELECTION_THIRD_ROW_LEFT = 10;</code>
     */
    public static final int ENUM_SEAT_SELECTION_THIRD_ROW_LEFT_VALUE = 10;
    /**
     * <code>ENUM_SEAT_SELECTION_THIRD_ROW_RIGHT = 11;</code>
     */
    public static final int ENUM_SEAT_SELECTION_THIRD_ROW_RIGHT_VALUE = 11;
    /**
     * <code>ENUM_SEAT_SELECTION_THIRD_ROW_CENTRE = 12;</code>
     */
    public static final int ENUM_SEAT_SELECTION_THIRD_ROW_CENTRE_VALUE = 12;
    /**
     * <code>ENUM_SEAT_SELECTION_THIRD_ROW_ALL = 13;</code>
     */
    public static final int ENUM_SEAT_SELECTION_THIRD_ROW_ALL_VALUE = 13;
    /**
     * <code>ENUM_SEAT_SELECTION_REAR_ALL = 14;</code>
     */
    public static final int ENUM_SEAT_SELECTION_REAR_ALL_VALUE = 14;


    public final int getNumber() {
      if (this == UNRECOGNIZED) {
        throw new java.lang.IllegalArgumentException(
            "Can't get the number of an unknown enum value.");
      }
      return value;
    }

    /**
     * @param value The numeric wire value of the corresponding enum entry.
     * @return The enum associated with the given numeric wire value.
     * @deprecated Use {@link #forNumber(int)} instead.
     */
    @java.lang.Deprecated
    public static EnumSeatSelection valueOf(int value) {
      return forNumber(value);
    }

    /**
     * @param value The numeric wire value of the corresponding enum entry.
     * @return The enum associated with the given numeric wire value.
     */
    public static EnumSeatSelection forNumber(int value) {
      switch (value) {
        case 0: return ENUM_SEAT_SELECTION_UNSPECIFIED;
        case 1: return ENUM_SEAT_SELECTION_ALL;
        case 2: return ENUM_SEAT_SELECTION_FIRST_ROW_LEFT;
        case 3: return ENUM_SEAT_SELECTION_FIRST_ROW_RIGHT;
        case 4: return ENUM_SEAT_SELECTION_FIRST_ROW_CENTRE;
        case 5: return ENUM_SEAT_SELECTION_FIRST_ROW_ALL;
        case 6: return ENUM_SEAT_SELECTION_SECOND_ROW_LEFT;
        case 7: return ENUM_SEAT_SELECTION_SECOND_ROW_RIGHT;
        case 8: return ENUM_SEAT_SELECTION_SECOND_ROW_CENTRE;
        case 9: return ENUM_SEAT_SELECTION_SECOND_ROW_ALL;
        case 10: return ENUM_SEAT_SELECTION_THIRD_ROW_LEFT;
        case 11: return ENUM_SEAT_SELECTION_THIRD_ROW_RIGHT;
        case 12: return ENUM_SEAT_SELECTION_THIRD_ROW_CENTRE;
        case 13: return ENUM_SEAT_SELECTION_THIRD_ROW_ALL;
        case 14: return ENUM_SEAT_SELECTION_REAR_ALL;
        default: return null;
      }
    }

    public static com.google.protobuf.Internal.EnumLiteMap<EnumSeatSelection>
        internalGetValueMap() {
      return internalValueMap;
    }
    private static final com.google.protobuf.Internal.EnumLiteMap<
        EnumSeatSelection> internalValueMap =
          new com.google.protobuf.Internal.EnumLiteMap<EnumSeatSelection>() {
            public EnumSeatSelection findValueByNumber(int number) {
              return EnumSeatSelection.forNumber(number);
            }
          };

    public final com.google.protobuf.Descriptors.EnumValueDescriptor
        getValueDescriptor() {
      if (this == UNRECOGNIZED) {
        throw new java.lang.IllegalStateException(
            "Can't get the descriptor of an unrecognized enum value.");
      }
      return getDescriptor().getValues().get(ordinal());
    }
    public final com.google.protobuf.Descriptors.EnumDescriptor
        getDescriptorForType() {
      return getDescriptor();
    }
    public static final com.google.protobuf.Descriptors.EnumDescriptor
        getDescriptor() {
      return seats_common.SeatsCommon.getDescriptor().getEnumTypes().get(0);
    }

    private static final EnumSeatSelection[] VALUES = values();

    public static EnumSeatSelection valueOf(
        com.google.protobuf.Descriptors.EnumValueDescriptor desc) {
      if (desc.getType() != getDescriptor()) {
        throw new java.lang.IllegalArgumentException(
          "EnumValueDescriptor is not for this type.");
      }
      if (desc.getIndex() == -1) {
        return UNRECOGNIZED;
      }
      return VALUES[desc.getIndex()];
    }

    private final int value;

    private EnumSeatSelection(int value) {
      this.value = value;
    }

    // @@protoc_insertion_point(enum_scope:seats_common.EnumSeatSelection)
  }


  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n\022seats_common.proto\022\014seats_common*\341\004\n\021E" +
      "numSeatSelection\022#\n\037ENUM_SEAT_SELECTION_" +
      "UNSPECIFIED\020\000\022\033\n\027ENUM_SEAT_SELECTION_ALL" +
      "\020\001\022&\n\"ENUM_SEAT_SELECTION_FIRST_ROW_LEFT" +
      "\020\002\022\'\n#ENUM_SEAT_SELECTION_FIRST_ROW_RIGH" +
      "T\020\003\022(\n$ENUM_SEAT_SELECTION_FIRST_ROW_CEN" +
      "TRE\020\004\022%\n!ENUM_SEAT_SELECTION_FIRST_ROW_A" +
      "LL\020\005\022\'\n#ENUM_SEAT_SELECTION_SECOND_ROW_L" +
      "EFT\020\006\022(\n$ENUM_SEAT_SELECTION_SECOND_ROW_" +
      "RIGHT\020\007\022)\n%ENUM_SEAT_SELECTION_SECOND_RO" +
      "W_CENTRE\020\010\022&\n\"ENUM_SEAT_SELECTION_SECOND" +
      "_ROW_ALL\020\t\022&\n\"ENUM_SEAT_SELECTION_THIRD_" +
      "ROW_LEFT\020\n\022\'\n#ENUM_SEAT_SELECTION_THIRD_" +
      "ROW_RIGHT\020\013\022(\n$ENUM_SEAT_SELECTION_THIRD" +
      "_ROW_CENTRE\020\014\022%\n!ENUM_SEAT_SELECTION_THI" +
      "RD_ROW_ALL\020\r\022 \n\034ENUM_SEAT_SELECTION_REAR" +
      "_ALL\020\016b\006proto3"
    };
    descriptor = com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
        });
    descriptor.resolveAllFeaturesImmutable();
  }

  // @@protoc_insertion_point(outer_class_scope)
}
