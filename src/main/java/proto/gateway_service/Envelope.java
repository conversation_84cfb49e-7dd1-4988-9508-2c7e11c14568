// Generated by the protocol buffer compiler.  DO NOT EDIT!
// NO CHECKED-IN PROTOBUF GENCODE
// source: envelope.proto
// Protobuf Java Version: 4.28.2

package gateway_service;

public final class Envelope {
  private Envelope() {}
  static {
    com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
      com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
      /* major= */ 4,
      /* minor= */ 28,
      /* patch= */ 2,
      /* suffix= */ "",
      Envelope.class.getName());
  }
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  /**
   * <pre>
   * Enum returned from any request indicating success or failure
   * </pre>
   *
   * Protobuf enum {@code gateway_service.EnumOperationStatus}
   */
  public enum EnumOperationStatus
      implements com.google.protobuf.ProtocolMessageEnum {
    /**
     * <code>ENUM_OPERATION_STATUS_UNSPECIFIED = 0;</code>
     */
    ENUM_OPERATION_STATUS_UNSPECIFIED(0),
    /**
     * <pre>
     * Normal return status indication, no error in operation and/or data is valid
     * </pre>
     *
     * <code>ENUM_OPERATION_STATUS_OK = 1;</code>
     */
    ENUM_OPERATION_STATUS_OK(1),
    /**
     * <pre>
     * Error in requested service due to absence of Service hash in configuration file
     * </pre>
     *
     * <code>ENUM_OPERATION_STATUS_HASH_NOT_FOUND = 2;</code>
     */
    ENUM_OPERATION_STATUS_HASH_NOT_FOUND(2),
    /**
     * <pre>
     * Return status when the service requested is not available within the
     * wait_for_service_in_ms time interval
     * </pre>
     *
     * <code>ENUM_OPERATION_STATUS_SERVICE_NOT_AVAILABLE = 3;</code>
     */
    ENUM_OPERATION_STATUS_SERVICE_NOT_AVAILABLE(3),
    /**
     * <pre>
     * Return status when theere is a failure in security requirements for the service request
     * </pre>
     *
     * <code>ENUM_OPERATION_STATUS_SECURITY_FAILURE = 4;</code>
     */
    ENUM_OPERATION_STATUS_SECURITY_FAILURE(4),
    /**
     * <pre>
     * Return status when the request is out of sequence and therefore not executed
     * </pre>
     *
     * <code>ENUM_OPERATION_STATUS_REQUEST_OUT_OF_SEQUENCE = 5;</code>
     */
    ENUM_OPERATION_STATUS_REQUEST_OUT_OF_SEQUENCE(5),
    /**
     * <pre>
     * Return status when the request is out of required time frame as specified in JWT
     * </pre>
     *
     * <code>ENUM_OPERATION_STATUS_REQUEST_EXPIRED = 6;</code>
     */
    ENUM_OPERATION_STATUS_REQUEST_EXPIRED(6),
    /**
     * <pre>
     * Return status when the request was not completed in the time limit given in the request
     * </pre>
     *
     * <code>ENUM_OPERATION_STATUS_REQUEST_TIMED_OUT = 7;</code>
     */
    ENUM_OPERATION_STATUS_REQUEST_TIMED_OUT(7),
    /**
     * <pre>
     * Return status when a network activation failed to be completed in the time limit given in the request
     * </pre>
     *
     * <code>ENUM_OPERATION_STATUS_REQUEST_NETWORK_ACTIVATION_FAILED = 8;</code>
     */
    ENUM_OPERATION_STATUS_REQUEST_NETWORK_ACTIVATION_FAILED(8),
    /**
     * <pre>
     * Unspecified error has occurred
     * </pre>
     *
     * <code>ENUM_OPERATION_STATUS_OPERATION_FAILED = 9;</code>
     */
    ENUM_OPERATION_STATUS_OPERATION_FAILED(9),
    /**
     * <pre>
     * Invalid vehicle unique id provided in request
     * </pre>
     *
     * <code>ENUM_OPERATION_STATUS_INVALID_VUID = 10;</code>
     */
    ENUM_OPERATION_STATUS_INVALID_VUID(10),
    UNRECOGNIZED(-1),
    ;

    static {
      com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
        com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
        /* major= */ 4,
        /* minor= */ 28,
        /* patch= */ 2,
        /* suffix= */ "",
        EnumOperationStatus.class.getName());
    }
    /**
     * <code>ENUM_OPERATION_STATUS_UNSPECIFIED = 0;</code>
     */
    public static final int ENUM_OPERATION_STATUS_UNSPECIFIED_VALUE = 0;
    /**
     * <pre>
     * Normal return status indication, no error in operation and/or data is valid
     * </pre>
     *
     * <code>ENUM_OPERATION_STATUS_OK = 1;</code>
     */
    public static final int ENUM_OPERATION_STATUS_OK_VALUE = 1;
    /**
     * <pre>
     * Error in requested service due to absence of Service hash in configuration file
     * </pre>
     *
     * <code>ENUM_OPERATION_STATUS_HASH_NOT_FOUND = 2;</code>
     */
    public static final int ENUM_OPERATION_STATUS_HASH_NOT_FOUND_VALUE = 2;
    /**
     * <pre>
     * Return status when the service requested is not available within the
     * wait_for_service_in_ms time interval
     * </pre>
     *
     * <code>ENUM_OPERATION_STATUS_SERVICE_NOT_AVAILABLE = 3;</code>
     */
    public static final int ENUM_OPERATION_STATUS_SERVICE_NOT_AVAILABLE_VALUE = 3;
    /**
     * <pre>
     * Return status when theere is a failure in security requirements for the service request
     * </pre>
     *
     * <code>ENUM_OPERATION_STATUS_SECURITY_FAILURE = 4;</code>
     */
    public static final int ENUM_OPERATION_STATUS_SECURITY_FAILURE_VALUE = 4;
    /**
     * <pre>
     * Return status when the request is out of sequence and therefore not executed
     * </pre>
     *
     * <code>ENUM_OPERATION_STATUS_REQUEST_OUT_OF_SEQUENCE = 5;</code>
     */
    public static final int ENUM_OPERATION_STATUS_REQUEST_OUT_OF_SEQUENCE_VALUE = 5;
    /**
     * <pre>
     * Return status when the request is out of required time frame as specified in JWT
     * </pre>
     *
     * <code>ENUM_OPERATION_STATUS_REQUEST_EXPIRED = 6;</code>
     */
    public static final int ENUM_OPERATION_STATUS_REQUEST_EXPIRED_VALUE = 6;
    /**
     * <pre>
     * Return status when the request was not completed in the time limit given in the request
     * </pre>
     *
     * <code>ENUM_OPERATION_STATUS_REQUEST_TIMED_OUT = 7;</code>
     */
    public static final int ENUM_OPERATION_STATUS_REQUEST_TIMED_OUT_VALUE = 7;
    /**
     * <pre>
     * Return status when a network activation failed to be completed in the time limit given in the request
     * </pre>
     *
     * <code>ENUM_OPERATION_STATUS_REQUEST_NETWORK_ACTIVATION_FAILED = 8;</code>
     */
    public static final int ENUM_OPERATION_STATUS_REQUEST_NETWORK_ACTIVATION_FAILED_VALUE = 8;
    /**
     * <pre>
     * Unspecified error has occurred
     * </pre>
     *
     * <code>ENUM_OPERATION_STATUS_OPERATION_FAILED = 9;</code>
     */
    public static final int ENUM_OPERATION_STATUS_OPERATION_FAILED_VALUE = 9;
    /**
     * <pre>
     * Invalid vehicle unique id provided in request
     * </pre>
     *
     * <code>ENUM_OPERATION_STATUS_INVALID_VUID = 10;</code>
     */
    public static final int ENUM_OPERATION_STATUS_INVALID_VUID_VALUE = 10;


    public final int getNumber() {
      if (this == UNRECOGNIZED) {
        throw new java.lang.IllegalArgumentException(
            "Can't get the number of an unknown enum value.");
      }
      return value;
    }

    /**
     * @param value The numeric wire value of the corresponding enum entry.
     * @return The enum associated with the given numeric wire value.
     * @deprecated Use {@link #forNumber(int)} instead.
     */
    @java.lang.Deprecated
    public static EnumOperationStatus valueOf(int value) {
      return forNumber(value);
    }

    /**
     * @param value The numeric wire value of the corresponding enum entry.
     * @return The enum associated with the given numeric wire value.
     */
    public static EnumOperationStatus forNumber(int value) {
      switch (value) {
        case 0: return ENUM_OPERATION_STATUS_UNSPECIFIED;
        case 1: return ENUM_OPERATION_STATUS_OK;
        case 2: return ENUM_OPERATION_STATUS_HASH_NOT_FOUND;
        case 3: return ENUM_OPERATION_STATUS_SERVICE_NOT_AVAILABLE;
        case 4: return ENUM_OPERATION_STATUS_SECURITY_FAILURE;
        case 5: return ENUM_OPERATION_STATUS_REQUEST_OUT_OF_SEQUENCE;
        case 6: return ENUM_OPERATION_STATUS_REQUEST_EXPIRED;
        case 7: return ENUM_OPERATION_STATUS_REQUEST_TIMED_OUT;
        case 8: return ENUM_OPERATION_STATUS_REQUEST_NETWORK_ACTIVATION_FAILED;
        case 9: return ENUM_OPERATION_STATUS_OPERATION_FAILED;
        case 10: return ENUM_OPERATION_STATUS_INVALID_VUID;
        default: return null;
      }
    }

    public static com.google.protobuf.Internal.EnumLiteMap<EnumOperationStatus>
        internalGetValueMap() {
      return internalValueMap;
    }
    private static final com.google.protobuf.Internal.EnumLiteMap<
        EnumOperationStatus> internalValueMap =
          new com.google.protobuf.Internal.EnumLiteMap<EnumOperationStatus>() {
            public EnumOperationStatus findValueByNumber(int number) {
              return EnumOperationStatus.forNumber(number);
            }
          };

    public final com.google.protobuf.Descriptors.EnumValueDescriptor
        getValueDescriptor() {
      if (this == UNRECOGNIZED) {
        throw new java.lang.IllegalStateException(
            "Can't get the descriptor of an unrecognized enum value.");
      }
      return getDescriptor().getValues().get(ordinal());
    }
    public final com.google.protobuf.Descriptors.EnumDescriptor
        getDescriptorForType() {
      return getDescriptor();
    }
    public static final com.google.protobuf.Descriptors.EnumDescriptor
        getDescriptor() {
      return gateway_service.Envelope.getDescriptor().getEnumTypes().get(0);
    }

    private static final EnumOperationStatus[] VALUES = values();

    public static EnumOperationStatus valueOf(
        com.google.protobuf.Descriptors.EnumValueDescriptor desc) {
      if (desc.getType() != getDescriptor()) {
        throw new java.lang.IllegalArgumentException(
          "EnumValueDescriptor is not for this type.");
      }
      if (desc.getIndex() == -1) {
        return UNRECOGNIZED;
      }
      return VALUES[desc.getIndex()];
    }

    private final int value;

    private EnumOperationStatus(int value) {
      this.value = value;
    }

    // @@protoc_insertion_point(enum_scope:gateway_service.EnumOperationStatus)
  }

  /**
   * <pre>
   * Enum to specify the Network Demand action to be taken by Service Gateway or RFC
   * </pre>
   *
   * Protobuf enum {@code gateway_service.EnumNetworkDemandAction}
   */
  public enum EnumNetworkDemandAction
      implements com.google.protobuf.ProtocolMessageEnum {
    /**
     * <code>ENUM_NETWORD_DEMAND_ACTION_UNSPECIFIED = 0;</code>
     */
    ENUM_NETWORD_DEMAND_ACTION_UNSPECIFIED(0),
    /**
     * <pre>
     * Action indicating to RFC to acquire Network Demand
     * </pre>
     *
     * <code>ENUM_NETWORK_DEMAND_ACTION_ACQUIRE = 1;</code>
     */
    ENUM_NETWORK_DEMAND_ACTION_ACQUIRE(1),
    /**
     * <pre>
     * Action indicating to RFC to release Network Demand
     * </pre>
     *
     * <code>ENUM_NETWORK_DEMAND_ACTION_RELEASE = 2;</code>
     */
    ENUM_NETWORK_DEMAND_ACTION_RELEASE(2),
    UNRECOGNIZED(-1),
    ;

    static {
      com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
        com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
        /* major= */ 4,
        /* minor= */ 28,
        /* patch= */ 2,
        /* suffix= */ "",
        EnumNetworkDemandAction.class.getName());
    }
    /**
     * <code>ENUM_NETWORD_DEMAND_ACTION_UNSPECIFIED = 0;</code>
     */
    public static final int ENUM_NETWORD_DEMAND_ACTION_UNSPECIFIED_VALUE = 0;
    /**
     * <pre>
     * Action indicating to RFC to acquire Network Demand
     * </pre>
     *
     * <code>ENUM_NETWORK_DEMAND_ACTION_ACQUIRE = 1;</code>
     */
    public static final int ENUM_NETWORK_DEMAND_ACTION_ACQUIRE_VALUE = 1;
    /**
     * <pre>
     * Action indicating to RFC to release Network Demand
     * </pre>
     *
     * <code>ENUM_NETWORK_DEMAND_ACTION_RELEASE = 2;</code>
     */
    public static final int ENUM_NETWORK_DEMAND_ACTION_RELEASE_VALUE = 2;


    public final int getNumber() {
      if (this == UNRECOGNIZED) {
        throw new java.lang.IllegalArgumentException(
            "Can't get the number of an unknown enum value.");
      }
      return value;
    }

    /**
     * @param value The numeric wire value of the corresponding enum entry.
     * @return The enum associated with the given numeric wire value.
     * @deprecated Use {@link #forNumber(int)} instead.
     */
    @java.lang.Deprecated
    public static EnumNetworkDemandAction valueOf(int value) {
      return forNumber(value);
    }

    /**
     * @param value The numeric wire value of the corresponding enum entry.
     * @return The enum associated with the given numeric wire value.
     */
    public static EnumNetworkDemandAction forNumber(int value) {
      switch (value) {
        case 0: return ENUM_NETWORD_DEMAND_ACTION_UNSPECIFIED;
        case 1: return ENUM_NETWORK_DEMAND_ACTION_ACQUIRE;
        case 2: return ENUM_NETWORK_DEMAND_ACTION_RELEASE;
        default: return null;
      }
    }

    public static com.google.protobuf.Internal.EnumLiteMap<EnumNetworkDemandAction>
        internalGetValueMap() {
      return internalValueMap;
    }
    private static final com.google.protobuf.Internal.EnumLiteMap<
        EnumNetworkDemandAction> internalValueMap =
          new com.google.protobuf.Internal.EnumLiteMap<EnumNetworkDemandAction>() {
            public EnumNetworkDemandAction findValueByNumber(int number) {
              return EnumNetworkDemandAction.forNumber(number);
            }
          };

    public final com.google.protobuf.Descriptors.EnumValueDescriptor
        getValueDescriptor() {
      if (this == UNRECOGNIZED) {
        throw new java.lang.IllegalStateException(
            "Can't get the descriptor of an unrecognized enum value.");
      }
      return getDescriptor().getValues().get(ordinal());
    }
    public final com.google.protobuf.Descriptors.EnumDescriptor
        getDescriptorForType() {
      return getDescriptor();
    }
    public static final com.google.protobuf.Descriptors.EnumDescriptor
        getDescriptor() {
      return gateway_service.Envelope.getDescriptor().getEnumTypes().get(1);
    }

    private static final EnumNetworkDemandAction[] VALUES = values();

    public static EnumNetworkDemandAction valueOf(
        com.google.protobuf.Descriptors.EnumValueDescriptor desc) {
      if (desc.getType() != getDescriptor()) {
        throw new java.lang.IllegalArgumentException(
          "EnumValueDescriptor is not for this type.");
      }
      if (desc.getIndex() == -1) {
        return UNRECOGNIZED;
      }
      return VALUES[desc.getIndex()];
    }

    private final int value;

    private EnumNetworkDemandAction(int value) {
      this.value = value;
    }

    // @@protoc_insertion_point(enum_scope:gateway_service.EnumNetworkDemandAction)
  }

  public interface SubscribeMessageRequestOrBuilder extends
      // @@protoc_insertion_point(interface_extends:gateway_service.SubscribeMessageRequest)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     * The hash identifier used to lookup the required service in the vehicle service gateway configuration file
     * </pre>
     *
     * <code>optional string hash = 1;</code>
     * @return Whether the hash field is set.
     */
    boolean hasHash();
    /**
     * <pre>
     * The hash identifier used to lookup the required service in the vehicle service gateway configuration file
     * </pre>
     *
     * <code>optional string hash = 1;</code>
     * @return The hash.
     */
    java.lang.String getHash();
    /**
     * <pre>
     * The hash identifier used to lookup the required service in the vehicle service gateway configuration file
     * </pre>
     *
     * <code>optional string hash = 1;</code>
     * @return The bytes for hash.
     */
    com.google.protobuf.ByteString
        getHashBytes();

    /**
     * <pre>
     * The number of milliseconds for the service gateway to wait for a service to become available
     * Default 100ms if not set, if service is not available within requested time the request is not actioned
     * and a response of ENUM_OPERATION_STATUS_SERVICE_NOT_AVAILABLE is returned
     * </pre>
     *
     * <code>optional uint32 wait_for_service_in_ms = 2;</code>
     * @return Whether the waitForServiceInMs field is set.
     */
    boolean hasWaitForServiceInMs();
    /**
     * <pre>
     * The number of milliseconds for the service gateway to wait for a service to become available
     * Default 100ms if not set, if service is not available within requested time the request is not actioned
     * and a response of ENUM_OPERATION_STATUS_SERVICE_NOT_AVAILABLE is returned
     * </pre>
     *
     * <code>optional uint32 wait_for_service_in_ms = 2;</code>
     * @return The waitForServiceInMs.
     */
    int getWaitForServiceInMs();
  }
  /**
   * <pre>
   * Subscribe to an asychronous vehicle event
   * Any data received from the event will be published to VCDP using the EventMessage
   * </pre>
   *
   * Protobuf type {@code gateway_service.SubscribeMessageRequest}
   */
  public static final class SubscribeMessageRequest extends
      com.google.protobuf.GeneratedMessage implements
      // @@protoc_insertion_point(message_implements:gateway_service.SubscribeMessageRequest)
      SubscribeMessageRequestOrBuilder {
  private static final long serialVersionUID = 0L;
    static {
      com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
        com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
        /* major= */ 4,
        /* minor= */ 28,
        /* patch= */ 2,
        /* suffix= */ "",
        SubscribeMessageRequest.class.getName());
    }
    // Use SubscribeMessageRequest.newBuilder() to construct.
    private SubscribeMessageRequest(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
    }
    private SubscribeMessageRequest() {
      hash_ = "";
    }

    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return gateway_service.Envelope.internal_static_gateway_service_SubscribeMessageRequest_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return gateway_service.Envelope.internal_static_gateway_service_SubscribeMessageRequest_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              gateway_service.Envelope.SubscribeMessageRequest.class, gateway_service.Envelope.SubscribeMessageRequest.Builder.class);
    }

    private int bitField0_;
    public static final int HASH_FIELD_NUMBER = 1;
    @SuppressWarnings("serial")
    private volatile java.lang.Object hash_ = "";
    /**
     * <pre>
     * The hash identifier used to lookup the required service in the vehicle service gateway configuration file
     * </pre>
     *
     * <code>optional string hash = 1;</code>
     * @return Whether the hash field is set.
     */
    @java.lang.Override
    public boolean hasHash() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <pre>
     * The hash identifier used to lookup the required service in the vehicle service gateway configuration file
     * </pre>
     *
     * <code>optional string hash = 1;</code>
     * @return The hash.
     */
    @java.lang.Override
    public java.lang.String getHash() {
      java.lang.Object ref = hash_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        hash_ = s;
        return s;
      }
    }
    /**
     * <pre>
     * The hash identifier used to lookup the required service in the vehicle service gateway configuration file
     * </pre>
     *
     * <code>optional string hash = 1;</code>
     * @return The bytes for hash.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getHashBytes() {
      java.lang.Object ref = hash_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        hash_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int WAIT_FOR_SERVICE_IN_MS_FIELD_NUMBER = 2;
    private int waitForServiceInMs_ = 0;
    /**
     * <pre>
     * The number of milliseconds for the service gateway to wait for a service to become available
     * Default 100ms if not set, if service is not available within requested time the request is not actioned
     * and a response of ENUM_OPERATION_STATUS_SERVICE_NOT_AVAILABLE is returned
     * </pre>
     *
     * <code>optional uint32 wait_for_service_in_ms = 2;</code>
     * @return Whether the waitForServiceInMs field is set.
     */
    @java.lang.Override
    public boolean hasWaitForServiceInMs() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <pre>
     * The number of milliseconds for the service gateway to wait for a service to become available
     * Default 100ms if not set, if service is not available within requested time the request is not actioned
     * and a response of ENUM_OPERATION_STATUS_SERVICE_NOT_AVAILABLE is returned
     * </pre>
     *
     * <code>optional uint32 wait_for_service_in_ms = 2;</code>
     * @return The waitForServiceInMs.
     */
    @java.lang.Override
    public int getWaitForServiceInMs() {
      return waitForServiceInMs_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        com.google.protobuf.GeneratedMessage.writeString(output, 1, hash_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        output.writeUInt32(2, waitForServiceInMs_);
      }
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.GeneratedMessage.computeStringSize(1, hash_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(2, waitForServiceInMs_);
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof gateway_service.Envelope.SubscribeMessageRequest)) {
        return super.equals(obj);
      }
      gateway_service.Envelope.SubscribeMessageRequest other = (gateway_service.Envelope.SubscribeMessageRequest) obj;

      if (hasHash() != other.hasHash()) return false;
      if (hasHash()) {
        if (!getHash()
            .equals(other.getHash())) return false;
      }
      if (hasWaitForServiceInMs() != other.hasWaitForServiceInMs()) return false;
      if (hasWaitForServiceInMs()) {
        if (getWaitForServiceInMs()
            != other.getWaitForServiceInMs()) return false;
      }
      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasHash()) {
        hash = (37 * hash) + HASH_FIELD_NUMBER;
        hash = (53 * hash) + getHash().hashCode();
      }
      if (hasWaitForServiceInMs()) {
        hash = (37 * hash) + WAIT_FOR_SERVICE_IN_MS_FIELD_NUMBER;
        hash = (53 * hash) + getWaitForServiceInMs();
      }
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static gateway_service.Envelope.SubscribeMessageRequest parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static gateway_service.Envelope.SubscribeMessageRequest parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static gateway_service.Envelope.SubscribeMessageRequest parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static gateway_service.Envelope.SubscribeMessageRequest parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static gateway_service.Envelope.SubscribeMessageRequest parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static gateway_service.Envelope.SubscribeMessageRequest parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static gateway_service.Envelope.SubscribeMessageRequest parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input);
    }
    public static gateway_service.Envelope.SubscribeMessageRequest parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public static gateway_service.Envelope.SubscribeMessageRequest parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseDelimitedWithIOException(PARSER, input);
    }

    public static gateway_service.Envelope.SubscribeMessageRequest parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static gateway_service.Envelope.SubscribeMessageRequest parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input);
    }
    public static gateway_service.Envelope.SubscribeMessageRequest parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(gateway_service.Envelope.SubscribeMessageRequest prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     * Subscribe to an asychronous vehicle event
     * Any data received from the event will be published to VCDP using the EventMessage
     * </pre>
     *
     * Protobuf type {@code gateway_service.SubscribeMessageRequest}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:gateway_service.SubscribeMessageRequest)
        gateway_service.Envelope.SubscribeMessageRequestOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return gateway_service.Envelope.internal_static_gateway_service_SubscribeMessageRequest_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return gateway_service.Envelope.internal_static_gateway_service_SubscribeMessageRequest_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                gateway_service.Envelope.SubscribeMessageRequest.class, gateway_service.Envelope.SubscribeMessageRequest.Builder.class);
      }

      // Construct using gateway_service.Envelope.SubscribeMessageRequest.newBuilder()
      private Builder() {

      }

      private Builder(
          com.google.protobuf.GeneratedMessage.BuilderParent parent) {
        super(parent);

      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        bitField0_ = 0;
        hash_ = "";
        waitForServiceInMs_ = 0;
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return gateway_service.Envelope.internal_static_gateway_service_SubscribeMessageRequest_descriptor;
      }

      @java.lang.Override
      public gateway_service.Envelope.SubscribeMessageRequest getDefaultInstanceForType() {
        return gateway_service.Envelope.SubscribeMessageRequest.getDefaultInstance();
      }

      @java.lang.Override
      public gateway_service.Envelope.SubscribeMessageRequest build() {
        gateway_service.Envelope.SubscribeMessageRequest result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public gateway_service.Envelope.SubscribeMessageRequest buildPartial() {
        gateway_service.Envelope.SubscribeMessageRequest result = new gateway_service.Envelope.SubscribeMessageRequest(this);
        if (bitField0_ != 0) { buildPartial0(result); }
        onBuilt();
        return result;
      }

      private void buildPartial0(gateway_service.Envelope.SubscribeMessageRequest result) {
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.hash_ = hash_;
          to_bitField0_ |= 0x00000001;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          result.waitForServiceInMs_ = waitForServiceInMs_;
          to_bitField0_ |= 0x00000002;
        }
        result.bitField0_ |= to_bitField0_;
      }

      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof gateway_service.Envelope.SubscribeMessageRequest) {
          return mergeFrom((gateway_service.Envelope.SubscribeMessageRequest)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(gateway_service.Envelope.SubscribeMessageRequest other) {
        if (other == gateway_service.Envelope.SubscribeMessageRequest.getDefaultInstance()) return this;
        if (other.hasHash()) {
          hash_ = other.hash_;
          bitField0_ |= 0x00000001;
          onChanged();
        }
        if (other.hasWaitForServiceInMs()) {
          setWaitForServiceInMs(other.getWaitForServiceInMs());
        }
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 10: {
                hash_ = input.readStringRequireUtf8();
                bitField0_ |= 0x00000001;
                break;
              } // case 10
              case 16: {
                waitForServiceInMs_ = input.readUInt32();
                bitField0_ |= 0x00000002;
                break;
              } // case 16
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }
      private int bitField0_;

      private java.lang.Object hash_ = "";
      /**
       * <pre>
       * The hash identifier used to lookup the required service in the vehicle service gateway configuration file
       * </pre>
       *
       * <code>optional string hash = 1;</code>
       * @return Whether the hash field is set.
       */
      public boolean hasHash() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <pre>
       * The hash identifier used to lookup the required service in the vehicle service gateway configuration file
       * </pre>
       *
       * <code>optional string hash = 1;</code>
       * @return The hash.
       */
      public java.lang.String getHash() {
        java.lang.Object ref = hash_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          hash_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       * The hash identifier used to lookup the required service in the vehicle service gateway configuration file
       * </pre>
       *
       * <code>optional string hash = 1;</code>
       * @return The bytes for hash.
       */
      public com.google.protobuf.ByteString
          getHashBytes() {
        java.lang.Object ref = hash_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          hash_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       * The hash identifier used to lookup the required service in the vehicle service gateway configuration file
       * </pre>
       *
       * <code>optional string hash = 1;</code>
       * @param value The hash to set.
       * @return This builder for chaining.
       */
      public Builder setHash(
          java.lang.String value) {
        if (value == null) { throw new NullPointerException(); }
        hash_ = value;
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * The hash identifier used to lookup the required service in the vehicle service gateway configuration file
       * </pre>
       *
       * <code>optional string hash = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearHash() {
        hash_ = getDefaultInstance().getHash();
        bitField0_ = (bitField0_ & ~0x00000001);
        onChanged();
        return this;
      }
      /**
       * <pre>
       * The hash identifier used to lookup the required service in the vehicle service gateway configuration file
       * </pre>
       *
       * <code>optional string hash = 1;</code>
       * @param value The bytes for hash to set.
       * @return This builder for chaining.
       */
      public Builder setHashBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        checkByteStringIsUtf8(value);
        hash_ = value;
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }

      private int waitForServiceInMs_ ;
      /**
       * <pre>
       * The number of milliseconds for the service gateway to wait for a service to become available
       * Default 100ms if not set, if service is not available within requested time the request is not actioned
       * and a response of ENUM_OPERATION_STATUS_SERVICE_NOT_AVAILABLE is returned
       * </pre>
       *
       * <code>optional uint32 wait_for_service_in_ms = 2;</code>
       * @return Whether the waitForServiceInMs field is set.
       */
      @java.lang.Override
      public boolean hasWaitForServiceInMs() {
        return ((bitField0_ & 0x00000002) != 0);
      }
      /**
       * <pre>
       * The number of milliseconds for the service gateway to wait for a service to become available
       * Default 100ms if not set, if service is not available within requested time the request is not actioned
       * and a response of ENUM_OPERATION_STATUS_SERVICE_NOT_AVAILABLE is returned
       * </pre>
       *
       * <code>optional uint32 wait_for_service_in_ms = 2;</code>
       * @return The waitForServiceInMs.
       */
      @java.lang.Override
      public int getWaitForServiceInMs() {
        return waitForServiceInMs_;
      }
      /**
       * <pre>
       * The number of milliseconds for the service gateway to wait for a service to become available
       * Default 100ms if not set, if service is not available within requested time the request is not actioned
       * and a response of ENUM_OPERATION_STATUS_SERVICE_NOT_AVAILABLE is returned
       * </pre>
       *
       * <code>optional uint32 wait_for_service_in_ms = 2;</code>
       * @param value The waitForServiceInMs to set.
       * @return This builder for chaining.
       */
      public Builder setWaitForServiceInMs(int value) {

        waitForServiceInMs_ = value;
        bitField0_ |= 0x00000002;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * The number of milliseconds for the service gateway to wait for a service to become available
       * Default 100ms if not set, if service is not available within requested time the request is not actioned
       * and a response of ENUM_OPERATION_STATUS_SERVICE_NOT_AVAILABLE is returned
       * </pre>
       *
       * <code>optional uint32 wait_for_service_in_ms = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearWaitForServiceInMs() {
        bitField0_ = (bitField0_ & ~0x00000002);
        waitForServiceInMs_ = 0;
        onChanged();
        return this;
      }

      // @@protoc_insertion_point(builder_scope:gateway_service.SubscribeMessageRequest)
    }

    // @@protoc_insertion_point(class_scope:gateway_service.SubscribeMessageRequest)
    private static final gateway_service.Envelope.SubscribeMessageRequest DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new gateway_service.Envelope.SubscribeMessageRequest();
    }

    public static gateway_service.Envelope.SubscribeMessageRequest getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<SubscribeMessageRequest>
        PARSER = new com.google.protobuf.AbstractParser<SubscribeMessageRequest>() {
      @java.lang.Override
      public SubscribeMessageRequest parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<SubscribeMessageRequest> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<SubscribeMessageRequest> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public gateway_service.Envelope.SubscribeMessageRequest getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface SubscribeMessageResponseOrBuilder extends
      // @@protoc_insertion_point(interface_extends:gateway_service.SubscribeMessageResponse)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     * The status result of the request
     * </pre>
     *
     * <code>.gateway_service.EnumOperationStatus result = 1;</code>
     * @return The enum numeric value on the wire for result.
     */
    int getResultValue();
    /**
     * <pre>
     * The status result of the request
     * </pre>
     *
     * <code>.gateway_service.EnumOperationStatus result = 1;</code>
     * @return The result.
     */
    gateway_service.Envelope.EnumOperationStatus getResult();

    /**
     * <pre>
     * The hash identifier used to determine the SubscribeRequest this response relates to.
     * </pre>
     *
     * <code>optional string hash = 2;</code>
     * @return Whether the hash field is set.
     */
    boolean hasHash();
    /**
     * <pre>
     * The hash identifier used to determine the SubscribeRequest this response relates to.
     * </pre>
     *
     * <code>optional string hash = 2;</code>
     * @return The hash.
     */
    java.lang.String getHash();
    /**
     * <pre>
     * The hash identifier used to determine the SubscribeRequest this response relates to.
     * </pre>
     *
     * <code>optional string hash = 2;</code>
     * @return The bytes for hash.
     */
    com.google.protobuf.ByteString
        getHashBytes();
  }
  /**
   * <pre>
   * The response to a SubscribeMessageRequest
   * </pre>
   *
   * Protobuf type {@code gateway_service.SubscribeMessageResponse}
   */
  public static final class SubscribeMessageResponse extends
      com.google.protobuf.GeneratedMessage implements
      // @@protoc_insertion_point(message_implements:gateway_service.SubscribeMessageResponse)
      SubscribeMessageResponseOrBuilder {
  private static final long serialVersionUID = 0L;
    static {
      com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
        com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
        /* major= */ 4,
        /* minor= */ 28,
        /* patch= */ 2,
        /* suffix= */ "",
        SubscribeMessageResponse.class.getName());
    }
    // Use SubscribeMessageResponse.newBuilder() to construct.
    private SubscribeMessageResponse(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
    }
    private SubscribeMessageResponse() {
      result_ = 0;
      hash_ = "";
    }

    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return gateway_service.Envelope.internal_static_gateway_service_SubscribeMessageResponse_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return gateway_service.Envelope.internal_static_gateway_service_SubscribeMessageResponse_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              gateway_service.Envelope.SubscribeMessageResponse.class, gateway_service.Envelope.SubscribeMessageResponse.Builder.class);
    }

    private int bitField0_;
    public static final int RESULT_FIELD_NUMBER = 1;
    private int result_ = 0;
    /**
     * <pre>
     * The status result of the request
     * </pre>
     *
     * <code>.gateway_service.EnumOperationStatus result = 1;</code>
     * @return The enum numeric value on the wire for result.
     */
    @java.lang.Override public int getResultValue() {
      return result_;
    }
    /**
     * <pre>
     * The status result of the request
     * </pre>
     *
     * <code>.gateway_service.EnumOperationStatus result = 1;</code>
     * @return The result.
     */
    @java.lang.Override public gateway_service.Envelope.EnumOperationStatus getResult() {
      gateway_service.Envelope.EnumOperationStatus result = gateway_service.Envelope.EnumOperationStatus.forNumber(result_);
      return result == null ? gateway_service.Envelope.EnumOperationStatus.UNRECOGNIZED : result;
    }

    public static final int HASH_FIELD_NUMBER = 2;
    @SuppressWarnings("serial")
    private volatile java.lang.Object hash_ = "";
    /**
     * <pre>
     * The hash identifier used to determine the SubscribeRequest this response relates to.
     * </pre>
     *
     * <code>optional string hash = 2;</code>
     * @return Whether the hash field is set.
     */
    @java.lang.Override
    public boolean hasHash() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <pre>
     * The hash identifier used to determine the SubscribeRequest this response relates to.
     * </pre>
     *
     * <code>optional string hash = 2;</code>
     * @return The hash.
     */
    @java.lang.Override
    public java.lang.String getHash() {
      java.lang.Object ref = hash_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        hash_ = s;
        return s;
      }
    }
    /**
     * <pre>
     * The hash identifier used to determine the SubscribeRequest this response relates to.
     * </pre>
     *
     * <code>optional string hash = 2;</code>
     * @return The bytes for hash.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getHashBytes() {
      java.lang.Object ref = hash_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        hash_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (result_ != gateway_service.Envelope.EnumOperationStatus.ENUM_OPERATION_STATUS_UNSPECIFIED.getNumber()) {
        output.writeEnum(1, result_);
      }
      if (((bitField0_ & 0x00000001) != 0)) {
        com.google.protobuf.GeneratedMessage.writeString(output, 2, hash_);
      }
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (result_ != gateway_service.Envelope.EnumOperationStatus.ENUM_OPERATION_STATUS_UNSPECIFIED.getNumber()) {
        size += com.google.protobuf.CodedOutputStream
          .computeEnumSize(1, result_);
      }
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.GeneratedMessage.computeStringSize(2, hash_);
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof gateway_service.Envelope.SubscribeMessageResponse)) {
        return super.equals(obj);
      }
      gateway_service.Envelope.SubscribeMessageResponse other = (gateway_service.Envelope.SubscribeMessageResponse) obj;

      if (result_ != other.result_) return false;
      if (hasHash() != other.hasHash()) return false;
      if (hasHash()) {
        if (!getHash()
            .equals(other.getHash())) return false;
      }
      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + RESULT_FIELD_NUMBER;
      hash = (53 * hash) + result_;
      if (hasHash()) {
        hash = (37 * hash) + HASH_FIELD_NUMBER;
        hash = (53 * hash) + getHash().hashCode();
      }
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static gateway_service.Envelope.SubscribeMessageResponse parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static gateway_service.Envelope.SubscribeMessageResponse parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static gateway_service.Envelope.SubscribeMessageResponse parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static gateway_service.Envelope.SubscribeMessageResponse parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static gateway_service.Envelope.SubscribeMessageResponse parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static gateway_service.Envelope.SubscribeMessageResponse parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static gateway_service.Envelope.SubscribeMessageResponse parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input);
    }
    public static gateway_service.Envelope.SubscribeMessageResponse parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public static gateway_service.Envelope.SubscribeMessageResponse parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseDelimitedWithIOException(PARSER, input);
    }

    public static gateway_service.Envelope.SubscribeMessageResponse parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static gateway_service.Envelope.SubscribeMessageResponse parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input);
    }
    public static gateway_service.Envelope.SubscribeMessageResponse parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(gateway_service.Envelope.SubscribeMessageResponse prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     * The response to a SubscribeMessageRequest
     * </pre>
     *
     * Protobuf type {@code gateway_service.SubscribeMessageResponse}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:gateway_service.SubscribeMessageResponse)
        gateway_service.Envelope.SubscribeMessageResponseOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return gateway_service.Envelope.internal_static_gateway_service_SubscribeMessageResponse_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return gateway_service.Envelope.internal_static_gateway_service_SubscribeMessageResponse_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                gateway_service.Envelope.SubscribeMessageResponse.class, gateway_service.Envelope.SubscribeMessageResponse.Builder.class);
      }

      // Construct using gateway_service.Envelope.SubscribeMessageResponse.newBuilder()
      private Builder() {

      }

      private Builder(
          com.google.protobuf.GeneratedMessage.BuilderParent parent) {
        super(parent);

      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        bitField0_ = 0;
        result_ = 0;
        hash_ = "";
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return gateway_service.Envelope.internal_static_gateway_service_SubscribeMessageResponse_descriptor;
      }

      @java.lang.Override
      public gateway_service.Envelope.SubscribeMessageResponse getDefaultInstanceForType() {
        return gateway_service.Envelope.SubscribeMessageResponse.getDefaultInstance();
      }

      @java.lang.Override
      public gateway_service.Envelope.SubscribeMessageResponse build() {
        gateway_service.Envelope.SubscribeMessageResponse result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public gateway_service.Envelope.SubscribeMessageResponse buildPartial() {
        gateway_service.Envelope.SubscribeMessageResponse result = new gateway_service.Envelope.SubscribeMessageResponse(this);
        if (bitField0_ != 0) { buildPartial0(result); }
        onBuilt();
        return result;
      }

      private void buildPartial0(gateway_service.Envelope.SubscribeMessageResponse result) {
        int from_bitField0_ = bitField0_;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.result_ = result_;
        }
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000002) != 0)) {
          result.hash_ = hash_;
          to_bitField0_ |= 0x00000001;
        }
        result.bitField0_ |= to_bitField0_;
      }

      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof gateway_service.Envelope.SubscribeMessageResponse) {
          return mergeFrom((gateway_service.Envelope.SubscribeMessageResponse)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(gateway_service.Envelope.SubscribeMessageResponse other) {
        if (other == gateway_service.Envelope.SubscribeMessageResponse.getDefaultInstance()) return this;
        if (other.result_ != 0) {
          setResultValue(other.getResultValue());
        }
        if (other.hasHash()) {
          hash_ = other.hash_;
          bitField0_ |= 0x00000002;
          onChanged();
        }
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 8: {
                result_ = input.readEnum();
                bitField0_ |= 0x00000001;
                break;
              } // case 8
              case 18: {
                hash_ = input.readStringRequireUtf8();
                bitField0_ |= 0x00000002;
                break;
              } // case 18
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }
      private int bitField0_;

      private int result_ = 0;
      /**
       * <pre>
       * The status result of the request
       * </pre>
       *
       * <code>.gateway_service.EnumOperationStatus result = 1;</code>
       * @return The enum numeric value on the wire for result.
       */
      @java.lang.Override public int getResultValue() {
        return result_;
      }
      /**
       * <pre>
       * The status result of the request
       * </pre>
       *
       * <code>.gateway_service.EnumOperationStatus result = 1;</code>
       * @param value The enum numeric value on the wire for result to set.
       * @return This builder for chaining.
       */
      public Builder setResultValue(int value) {
        result_ = value;
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * The status result of the request
       * </pre>
       *
       * <code>.gateway_service.EnumOperationStatus result = 1;</code>
       * @return The result.
       */
      @java.lang.Override
      public gateway_service.Envelope.EnumOperationStatus getResult() {
        gateway_service.Envelope.EnumOperationStatus result = gateway_service.Envelope.EnumOperationStatus.forNumber(result_);
        return result == null ? gateway_service.Envelope.EnumOperationStatus.UNRECOGNIZED : result;
      }
      /**
       * <pre>
       * The status result of the request
       * </pre>
       *
       * <code>.gateway_service.EnumOperationStatus result = 1;</code>
       * @param value The result to set.
       * @return This builder for chaining.
       */
      public Builder setResult(gateway_service.Envelope.EnumOperationStatus value) {
        if (value == null) {
          throw new NullPointerException();
        }
        bitField0_ |= 0x00000001;
        result_ = value.getNumber();
        onChanged();
        return this;
      }
      /**
       * <pre>
       * The status result of the request
       * </pre>
       *
       * <code>.gateway_service.EnumOperationStatus result = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearResult() {
        bitField0_ = (bitField0_ & ~0x00000001);
        result_ = 0;
        onChanged();
        return this;
      }

      private java.lang.Object hash_ = "";
      /**
       * <pre>
       * The hash identifier used to determine the SubscribeRequest this response relates to.
       * </pre>
       *
       * <code>optional string hash = 2;</code>
       * @return Whether the hash field is set.
       */
      public boolean hasHash() {
        return ((bitField0_ & 0x00000002) != 0);
      }
      /**
       * <pre>
       * The hash identifier used to determine the SubscribeRequest this response relates to.
       * </pre>
       *
       * <code>optional string hash = 2;</code>
       * @return The hash.
       */
      public java.lang.String getHash() {
        java.lang.Object ref = hash_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          hash_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       * The hash identifier used to determine the SubscribeRequest this response relates to.
       * </pre>
       *
       * <code>optional string hash = 2;</code>
       * @return The bytes for hash.
       */
      public com.google.protobuf.ByteString
          getHashBytes() {
        java.lang.Object ref = hash_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          hash_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       * The hash identifier used to determine the SubscribeRequest this response relates to.
       * </pre>
       *
       * <code>optional string hash = 2;</code>
       * @param value The hash to set.
       * @return This builder for chaining.
       */
      public Builder setHash(
          java.lang.String value) {
        if (value == null) { throw new NullPointerException(); }
        hash_ = value;
        bitField0_ |= 0x00000002;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * The hash identifier used to determine the SubscribeRequest this response relates to.
       * </pre>
       *
       * <code>optional string hash = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearHash() {
        hash_ = getDefaultInstance().getHash();
        bitField0_ = (bitField0_ & ~0x00000002);
        onChanged();
        return this;
      }
      /**
       * <pre>
       * The hash identifier used to determine the SubscribeRequest this response relates to.
       * </pre>
       *
       * <code>optional string hash = 2;</code>
       * @param value The bytes for hash to set.
       * @return This builder for chaining.
       */
      public Builder setHashBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        checkByteStringIsUtf8(value);
        hash_ = value;
        bitField0_ |= 0x00000002;
        onChanged();
        return this;
      }

      // @@protoc_insertion_point(builder_scope:gateway_service.SubscribeMessageResponse)
    }

    // @@protoc_insertion_point(class_scope:gateway_service.SubscribeMessageResponse)
    private static final gateway_service.Envelope.SubscribeMessageResponse DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new gateway_service.Envelope.SubscribeMessageResponse();
    }

    public static gateway_service.Envelope.SubscribeMessageResponse getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<SubscribeMessageResponse>
        PARSER = new com.google.protobuf.AbstractParser<SubscribeMessageResponse>() {
      @java.lang.Override
      public SubscribeMessageResponse parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<SubscribeMessageResponse> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<SubscribeMessageResponse> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public gateway_service.Envelope.SubscribeMessageResponse getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface UnsubscribeMessageRequestOrBuilder extends
      // @@protoc_insertion_point(interface_extends:gateway_service.UnsubscribeMessageRequest)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     * The hash identifier used to lookup the required service in the vehicle service gateway configuration file
     * </pre>
     *
     * <code>optional string hash = 1;</code>
     * @return Whether the hash field is set.
     */
    boolean hasHash();
    /**
     * <pre>
     * The hash identifier used to lookup the required service in the vehicle service gateway configuration file
     * </pre>
     *
     * <code>optional string hash = 1;</code>
     * @return The hash.
     */
    java.lang.String getHash();
    /**
     * <pre>
     * The hash identifier used to lookup the required service in the vehicle service gateway configuration file
     * </pre>
     *
     * <code>optional string hash = 1;</code>
     * @return The bytes for hash.
     */
    com.google.protobuf.ByteString
        getHashBytes();
  }
  /**
   * <pre>
   * UnSubscribe from an asychronous vehicle event
   * </pre>
   *
   * Protobuf type {@code gateway_service.UnsubscribeMessageRequest}
   */
  public static final class UnsubscribeMessageRequest extends
      com.google.protobuf.GeneratedMessage implements
      // @@protoc_insertion_point(message_implements:gateway_service.UnsubscribeMessageRequest)
      UnsubscribeMessageRequestOrBuilder {
  private static final long serialVersionUID = 0L;
    static {
      com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
        com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
        /* major= */ 4,
        /* minor= */ 28,
        /* patch= */ 2,
        /* suffix= */ "",
        UnsubscribeMessageRequest.class.getName());
    }
    // Use UnsubscribeMessageRequest.newBuilder() to construct.
    private UnsubscribeMessageRequest(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
    }
    private UnsubscribeMessageRequest() {
      hash_ = "";
    }

    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return gateway_service.Envelope.internal_static_gateway_service_UnsubscribeMessageRequest_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return gateway_service.Envelope.internal_static_gateway_service_UnsubscribeMessageRequest_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              gateway_service.Envelope.UnsubscribeMessageRequest.class, gateway_service.Envelope.UnsubscribeMessageRequest.Builder.class);
    }

    private int bitField0_;
    public static final int HASH_FIELD_NUMBER = 1;
    @SuppressWarnings("serial")
    private volatile java.lang.Object hash_ = "";
    /**
     * <pre>
     * The hash identifier used to lookup the required service in the vehicle service gateway configuration file
     * </pre>
     *
     * <code>optional string hash = 1;</code>
     * @return Whether the hash field is set.
     */
    @java.lang.Override
    public boolean hasHash() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <pre>
     * The hash identifier used to lookup the required service in the vehicle service gateway configuration file
     * </pre>
     *
     * <code>optional string hash = 1;</code>
     * @return The hash.
     */
    @java.lang.Override
    public java.lang.String getHash() {
      java.lang.Object ref = hash_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        hash_ = s;
        return s;
      }
    }
    /**
     * <pre>
     * The hash identifier used to lookup the required service in the vehicle service gateway configuration file
     * </pre>
     *
     * <code>optional string hash = 1;</code>
     * @return The bytes for hash.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getHashBytes() {
      java.lang.Object ref = hash_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        hash_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        com.google.protobuf.GeneratedMessage.writeString(output, 1, hash_);
      }
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.GeneratedMessage.computeStringSize(1, hash_);
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof gateway_service.Envelope.UnsubscribeMessageRequest)) {
        return super.equals(obj);
      }
      gateway_service.Envelope.UnsubscribeMessageRequest other = (gateway_service.Envelope.UnsubscribeMessageRequest) obj;

      if (hasHash() != other.hasHash()) return false;
      if (hasHash()) {
        if (!getHash()
            .equals(other.getHash())) return false;
      }
      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasHash()) {
        hash = (37 * hash) + HASH_FIELD_NUMBER;
        hash = (53 * hash) + getHash().hashCode();
      }
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static gateway_service.Envelope.UnsubscribeMessageRequest parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static gateway_service.Envelope.UnsubscribeMessageRequest parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static gateway_service.Envelope.UnsubscribeMessageRequest parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static gateway_service.Envelope.UnsubscribeMessageRequest parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static gateway_service.Envelope.UnsubscribeMessageRequest parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static gateway_service.Envelope.UnsubscribeMessageRequest parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static gateway_service.Envelope.UnsubscribeMessageRequest parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input);
    }
    public static gateway_service.Envelope.UnsubscribeMessageRequest parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public static gateway_service.Envelope.UnsubscribeMessageRequest parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseDelimitedWithIOException(PARSER, input);
    }

    public static gateway_service.Envelope.UnsubscribeMessageRequest parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static gateway_service.Envelope.UnsubscribeMessageRequest parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input);
    }
    public static gateway_service.Envelope.UnsubscribeMessageRequest parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(gateway_service.Envelope.UnsubscribeMessageRequest prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     * UnSubscribe from an asychronous vehicle event
     * </pre>
     *
     * Protobuf type {@code gateway_service.UnsubscribeMessageRequest}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:gateway_service.UnsubscribeMessageRequest)
        gateway_service.Envelope.UnsubscribeMessageRequestOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return gateway_service.Envelope.internal_static_gateway_service_UnsubscribeMessageRequest_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return gateway_service.Envelope.internal_static_gateway_service_UnsubscribeMessageRequest_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                gateway_service.Envelope.UnsubscribeMessageRequest.class, gateway_service.Envelope.UnsubscribeMessageRequest.Builder.class);
      }

      // Construct using gateway_service.Envelope.UnsubscribeMessageRequest.newBuilder()
      private Builder() {

      }

      private Builder(
          com.google.protobuf.GeneratedMessage.BuilderParent parent) {
        super(parent);

      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        bitField0_ = 0;
        hash_ = "";
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return gateway_service.Envelope.internal_static_gateway_service_UnsubscribeMessageRequest_descriptor;
      }

      @java.lang.Override
      public gateway_service.Envelope.UnsubscribeMessageRequest getDefaultInstanceForType() {
        return gateway_service.Envelope.UnsubscribeMessageRequest.getDefaultInstance();
      }

      @java.lang.Override
      public gateway_service.Envelope.UnsubscribeMessageRequest build() {
        gateway_service.Envelope.UnsubscribeMessageRequest result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public gateway_service.Envelope.UnsubscribeMessageRequest buildPartial() {
        gateway_service.Envelope.UnsubscribeMessageRequest result = new gateway_service.Envelope.UnsubscribeMessageRequest(this);
        if (bitField0_ != 0) { buildPartial0(result); }
        onBuilt();
        return result;
      }

      private void buildPartial0(gateway_service.Envelope.UnsubscribeMessageRequest result) {
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.hash_ = hash_;
          to_bitField0_ |= 0x00000001;
        }
        result.bitField0_ |= to_bitField0_;
      }

      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof gateway_service.Envelope.UnsubscribeMessageRequest) {
          return mergeFrom((gateway_service.Envelope.UnsubscribeMessageRequest)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(gateway_service.Envelope.UnsubscribeMessageRequest other) {
        if (other == gateway_service.Envelope.UnsubscribeMessageRequest.getDefaultInstance()) return this;
        if (other.hasHash()) {
          hash_ = other.hash_;
          bitField0_ |= 0x00000001;
          onChanged();
        }
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 10: {
                hash_ = input.readStringRequireUtf8();
                bitField0_ |= 0x00000001;
                break;
              } // case 10
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }
      private int bitField0_;

      private java.lang.Object hash_ = "";
      /**
       * <pre>
       * The hash identifier used to lookup the required service in the vehicle service gateway configuration file
       * </pre>
       *
       * <code>optional string hash = 1;</code>
       * @return Whether the hash field is set.
       */
      public boolean hasHash() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <pre>
       * The hash identifier used to lookup the required service in the vehicle service gateway configuration file
       * </pre>
       *
       * <code>optional string hash = 1;</code>
       * @return The hash.
       */
      public java.lang.String getHash() {
        java.lang.Object ref = hash_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          hash_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       * The hash identifier used to lookup the required service in the vehicle service gateway configuration file
       * </pre>
       *
       * <code>optional string hash = 1;</code>
       * @return The bytes for hash.
       */
      public com.google.protobuf.ByteString
          getHashBytes() {
        java.lang.Object ref = hash_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          hash_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       * The hash identifier used to lookup the required service in the vehicle service gateway configuration file
       * </pre>
       *
       * <code>optional string hash = 1;</code>
       * @param value The hash to set.
       * @return This builder for chaining.
       */
      public Builder setHash(
          java.lang.String value) {
        if (value == null) { throw new NullPointerException(); }
        hash_ = value;
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * The hash identifier used to lookup the required service in the vehicle service gateway configuration file
       * </pre>
       *
       * <code>optional string hash = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearHash() {
        hash_ = getDefaultInstance().getHash();
        bitField0_ = (bitField0_ & ~0x00000001);
        onChanged();
        return this;
      }
      /**
       * <pre>
       * The hash identifier used to lookup the required service in the vehicle service gateway configuration file
       * </pre>
       *
       * <code>optional string hash = 1;</code>
       * @param value The bytes for hash to set.
       * @return This builder for chaining.
       */
      public Builder setHashBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        checkByteStringIsUtf8(value);
        hash_ = value;
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }

      // @@protoc_insertion_point(builder_scope:gateway_service.UnsubscribeMessageRequest)
    }

    // @@protoc_insertion_point(class_scope:gateway_service.UnsubscribeMessageRequest)
    private static final gateway_service.Envelope.UnsubscribeMessageRequest DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new gateway_service.Envelope.UnsubscribeMessageRequest();
    }

    public static gateway_service.Envelope.UnsubscribeMessageRequest getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<UnsubscribeMessageRequest>
        PARSER = new com.google.protobuf.AbstractParser<UnsubscribeMessageRequest>() {
      @java.lang.Override
      public UnsubscribeMessageRequest parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<UnsubscribeMessageRequest> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<UnsubscribeMessageRequest> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public gateway_service.Envelope.UnsubscribeMessageRequest getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface UnsubscribeMessageResponseOrBuilder extends
      // @@protoc_insertion_point(interface_extends:gateway_service.UnsubscribeMessageResponse)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     * The status result of the request
     * </pre>
     *
     * <code>.gateway_service.EnumOperationStatus result = 1;</code>
     * @return The enum numeric value on the wire for result.
     */
    int getResultValue();
    /**
     * <pre>
     * The status result of the request
     * </pre>
     *
     * <code>.gateway_service.EnumOperationStatus result = 1;</code>
     * @return The result.
     */
    gateway_service.Envelope.EnumOperationStatus getResult();

    /**
     * <pre>
     * The hash identifier used to determine the UnsubscribeRequest this response relates to.
     * </pre>
     *
     * <code>optional string hash = 2;</code>
     * @return Whether the hash field is set.
     */
    boolean hasHash();
    /**
     * <pre>
     * The hash identifier used to determine the UnsubscribeRequest this response relates to.
     * </pre>
     *
     * <code>optional string hash = 2;</code>
     * @return The hash.
     */
    java.lang.String getHash();
    /**
     * <pre>
     * The hash identifier used to determine the UnsubscribeRequest this response relates to.
     * </pre>
     *
     * <code>optional string hash = 2;</code>
     * @return The bytes for hash.
     */
    com.google.protobuf.ByteString
        getHashBytes();
  }
  /**
   * <pre>
   * The response to a UnsubscribeMessageRequest
   * </pre>
   *
   * Protobuf type {@code gateway_service.UnsubscribeMessageResponse}
   */
  public static final class UnsubscribeMessageResponse extends
      com.google.protobuf.GeneratedMessage implements
      // @@protoc_insertion_point(message_implements:gateway_service.UnsubscribeMessageResponse)
      UnsubscribeMessageResponseOrBuilder {
  private static final long serialVersionUID = 0L;
    static {
      com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
        com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
        /* major= */ 4,
        /* minor= */ 28,
        /* patch= */ 2,
        /* suffix= */ "",
        UnsubscribeMessageResponse.class.getName());
    }
    // Use UnsubscribeMessageResponse.newBuilder() to construct.
    private UnsubscribeMessageResponse(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
    }
    private UnsubscribeMessageResponse() {
      result_ = 0;
      hash_ = "";
    }

    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return gateway_service.Envelope.internal_static_gateway_service_UnsubscribeMessageResponse_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return gateway_service.Envelope.internal_static_gateway_service_UnsubscribeMessageResponse_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              gateway_service.Envelope.UnsubscribeMessageResponse.class, gateway_service.Envelope.UnsubscribeMessageResponse.Builder.class);
    }

    private int bitField0_;
    public static final int RESULT_FIELD_NUMBER = 1;
    private int result_ = 0;
    /**
     * <pre>
     * The status result of the request
     * </pre>
     *
     * <code>.gateway_service.EnumOperationStatus result = 1;</code>
     * @return The enum numeric value on the wire for result.
     */
    @java.lang.Override public int getResultValue() {
      return result_;
    }
    /**
     * <pre>
     * The status result of the request
     * </pre>
     *
     * <code>.gateway_service.EnumOperationStatus result = 1;</code>
     * @return The result.
     */
    @java.lang.Override public gateway_service.Envelope.EnumOperationStatus getResult() {
      gateway_service.Envelope.EnumOperationStatus result = gateway_service.Envelope.EnumOperationStatus.forNumber(result_);
      return result == null ? gateway_service.Envelope.EnumOperationStatus.UNRECOGNIZED : result;
    }

    public static final int HASH_FIELD_NUMBER = 2;
    @SuppressWarnings("serial")
    private volatile java.lang.Object hash_ = "";
    /**
     * <pre>
     * The hash identifier used to determine the UnsubscribeRequest this response relates to.
     * </pre>
     *
     * <code>optional string hash = 2;</code>
     * @return Whether the hash field is set.
     */
    @java.lang.Override
    public boolean hasHash() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <pre>
     * The hash identifier used to determine the UnsubscribeRequest this response relates to.
     * </pre>
     *
     * <code>optional string hash = 2;</code>
     * @return The hash.
     */
    @java.lang.Override
    public java.lang.String getHash() {
      java.lang.Object ref = hash_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        hash_ = s;
        return s;
      }
    }
    /**
     * <pre>
     * The hash identifier used to determine the UnsubscribeRequest this response relates to.
     * </pre>
     *
     * <code>optional string hash = 2;</code>
     * @return The bytes for hash.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getHashBytes() {
      java.lang.Object ref = hash_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        hash_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (result_ != gateway_service.Envelope.EnumOperationStatus.ENUM_OPERATION_STATUS_UNSPECIFIED.getNumber()) {
        output.writeEnum(1, result_);
      }
      if (((bitField0_ & 0x00000001) != 0)) {
        com.google.protobuf.GeneratedMessage.writeString(output, 2, hash_);
      }
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (result_ != gateway_service.Envelope.EnumOperationStatus.ENUM_OPERATION_STATUS_UNSPECIFIED.getNumber()) {
        size += com.google.protobuf.CodedOutputStream
          .computeEnumSize(1, result_);
      }
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.GeneratedMessage.computeStringSize(2, hash_);
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof gateway_service.Envelope.UnsubscribeMessageResponse)) {
        return super.equals(obj);
      }
      gateway_service.Envelope.UnsubscribeMessageResponse other = (gateway_service.Envelope.UnsubscribeMessageResponse) obj;

      if (result_ != other.result_) return false;
      if (hasHash() != other.hasHash()) return false;
      if (hasHash()) {
        if (!getHash()
            .equals(other.getHash())) return false;
      }
      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + RESULT_FIELD_NUMBER;
      hash = (53 * hash) + result_;
      if (hasHash()) {
        hash = (37 * hash) + HASH_FIELD_NUMBER;
        hash = (53 * hash) + getHash().hashCode();
      }
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static gateway_service.Envelope.UnsubscribeMessageResponse parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static gateway_service.Envelope.UnsubscribeMessageResponse parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static gateway_service.Envelope.UnsubscribeMessageResponse parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static gateway_service.Envelope.UnsubscribeMessageResponse parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static gateway_service.Envelope.UnsubscribeMessageResponse parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static gateway_service.Envelope.UnsubscribeMessageResponse parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static gateway_service.Envelope.UnsubscribeMessageResponse parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input);
    }
    public static gateway_service.Envelope.UnsubscribeMessageResponse parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public static gateway_service.Envelope.UnsubscribeMessageResponse parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseDelimitedWithIOException(PARSER, input);
    }

    public static gateway_service.Envelope.UnsubscribeMessageResponse parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static gateway_service.Envelope.UnsubscribeMessageResponse parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input);
    }
    public static gateway_service.Envelope.UnsubscribeMessageResponse parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(gateway_service.Envelope.UnsubscribeMessageResponse prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     * The response to a UnsubscribeMessageRequest
     * </pre>
     *
     * Protobuf type {@code gateway_service.UnsubscribeMessageResponse}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:gateway_service.UnsubscribeMessageResponse)
        gateway_service.Envelope.UnsubscribeMessageResponseOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return gateway_service.Envelope.internal_static_gateway_service_UnsubscribeMessageResponse_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return gateway_service.Envelope.internal_static_gateway_service_UnsubscribeMessageResponse_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                gateway_service.Envelope.UnsubscribeMessageResponse.class, gateway_service.Envelope.UnsubscribeMessageResponse.Builder.class);
      }

      // Construct using gateway_service.Envelope.UnsubscribeMessageResponse.newBuilder()
      private Builder() {

      }

      private Builder(
          com.google.protobuf.GeneratedMessage.BuilderParent parent) {
        super(parent);

      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        bitField0_ = 0;
        result_ = 0;
        hash_ = "";
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return gateway_service.Envelope.internal_static_gateway_service_UnsubscribeMessageResponse_descriptor;
      }

      @java.lang.Override
      public gateway_service.Envelope.UnsubscribeMessageResponse getDefaultInstanceForType() {
        return gateway_service.Envelope.UnsubscribeMessageResponse.getDefaultInstance();
      }

      @java.lang.Override
      public gateway_service.Envelope.UnsubscribeMessageResponse build() {
        gateway_service.Envelope.UnsubscribeMessageResponse result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public gateway_service.Envelope.UnsubscribeMessageResponse buildPartial() {
        gateway_service.Envelope.UnsubscribeMessageResponse result = new gateway_service.Envelope.UnsubscribeMessageResponse(this);
        if (bitField0_ != 0) { buildPartial0(result); }
        onBuilt();
        return result;
      }

      private void buildPartial0(gateway_service.Envelope.UnsubscribeMessageResponse result) {
        int from_bitField0_ = bitField0_;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.result_ = result_;
        }
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000002) != 0)) {
          result.hash_ = hash_;
          to_bitField0_ |= 0x00000001;
        }
        result.bitField0_ |= to_bitField0_;
      }

      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof gateway_service.Envelope.UnsubscribeMessageResponse) {
          return mergeFrom((gateway_service.Envelope.UnsubscribeMessageResponse)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(gateway_service.Envelope.UnsubscribeMessageResponse other) {
        if (other == gateway_service.Envelope.UnsubscribeMessageResponse.getDefaultInstance()) return this;
        if (other.result_ != 0) {
          setResultValue(other.getResultValue());
        }
        if (other.hasHash()) {
          hash_ = other.hash_;
          bitField0_ |= 0x00000002;
          onChanged();
        }
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 8: {
                result_ = input.readEnum();
                bitField0_ |= 0x00000001;
                break;
              } // case 8
              case 18: {
                hash_ = input.readStringRequireUtf8();
                bitField0_ |= 0x00000002;
                break;
              } // case 18
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }
      private int bitField0_;

      private int result_ = 0;
      /**
       * <pre>
       * The status result of the request
       * </pre>
       *
       * <code>.gateway_service.EnumOperationStatus result = 1;</code>
       * @return The enum numeric value on the wire for result.
       */
      @java.lang.Override public int getResultValue() {
        return result_;
      }
      /**
       * <pre>
       * The status result of the request
       * </pre>
       *
       * <code>.gateway_service.EnumOperationStatus result = 1;</code>
       * @param value The enum numeric value on the wire for result to set.
       * @return This builder for chaining.
       */
      public Builder setResultValue(int value) {
        result_ = value;
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * The status result of the request
       * </pre>
       *
       * <code>.gateway_service.EnumOperationStatus result = 1;</code>
       * @return The result.
       */
      @java.lang.Override
      public gateway_service.Envelope.EnumOperationStatus getResult() {
        gateway_service.Envelope.EnumOperationStatus result = gateway_service.Envelope.EnumOperationStatus.forNumber(result_);
        return result == null ? gateway_service.Envelope.EnumOperationStatus.UNRECOGNIZED : result;
      }
      /**
       * <pre>
       * The status result of the request
       * </pre>
       *
       * <code>.gateway_service.EnumOperationStatus result = 1;</code>
       * @param value The result to set.
       * @return This builder for chaining.
       */
      public Builder setResult(gateway_service.Envelope.EnumOperationStatus value) {
        if (value == null) {
          throw new NullPointerException();
        }
        bitField0_ |= 0x00000001;
        result_ = value.getNumber();
        onChanged();
        return this;
      }
      /**
       * <pre>
       * The status result of the request
       * </pre>
       *
       * <code>.gateway_service.EnumOperationStatus result = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearResult() {
        bitField0_ = (bitField0_ & ~0x00000001);
        result_ = 0;
        onChanged();
        return this;
      }

      private java.lang.Object hash_ = "";
      /**
       * <pre>
       * The hash identifier used to determine the UnsubscribeRequest this response relates to.
       * </pre>
       *
       * <code>optional string hash = 2;</code>
       * @return Whether the hash field is set.
       */
      public boolean hasHash() {
        return ((bitField0_ & 0x00000002) != 0);
      }
      /**
       * <pre>
       * The hash identifier used to determine the UnsubscribeRequest this response relates to.
       * </pre>
       *
       * <code>optional string hash = 2;</code>
       * @return The hash.
       */
      public java.lang.String getHash() {
        java.lang.Object ref = hash_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          hash_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       * The hash identifier used to determine the UnsubscribeRequest this response relates to.
       * </pre>
       *
       * <code>optional string hash = 2;</code>
       * @return The bytes for hash.
       */
      public com.google.protobuf.ByteString
          getHashBytes() {
        java.lang.Object ref = hash_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          hash_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       * The hash identifier used to determine the UnsubscribeRequest this response relates to.
       * </pre>
       *
       * <code>optional string hash = 2;</code>
       * @param value The hash to set.
       * @return This builder for chaining.
       */
      public Builder setHash(
          java.lang.String value) {
        if (value == null) { throw new NullPointerException(); }
        hash_ = value;
        bitField0_ |= 0x00000002;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * The hash identifier used to determine the UnsubscribeRequest this response relates to.
       * </pre>
       *
       * <code>optional string hash = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearHash() {
        hash_ = getDefaultInstance().getHash();
        bitField0_ = (bitField0_ & ~0x00000002);
        onChanged();
        return this;
      }
      /**
       * <pre>
       * The hash identifier used to determine the UnsubscribeRequest this response relates to.
       * </pre>
       *
       * <code>optional string hash = 2;</code>
       * @param value The bytes for hash to set.
       * @return This builder for chaining.
       */
      public Builder setHashBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        checkByteStringIsUtf8(value);
        hash_ = value;
        bitField0_ |= 0x00000002;
        onChanged();
        return this;
      }

      // @@protoc_insertion_point(builder_scope:gateway_service.UnsubscribeMessageResponse)
    }

    // @@protoc_insertion_point(class_scope:gateway_service.UnsubscribeMessageResponse)
    private static final gateway_service.Envelope.UnsubscribeMessageResponse DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new gateway_service.Envelope.UnsubscribeMessageResponse();
    }

    public static gateway_service.Envelope.UnsubscribeMessageResponse getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<UnsubscribeMessageResponse>
        PARSER = new com.google.protobuf.AbstractParser<UnsubscribeMessageResponse>() {
      @java.lang.Override
      public UnsubscribeMessageResponse parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<UnsubscribeMessageResponse> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<UnsubscribeMessageResponse> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public gateway_service.Envelope.UnsubscribeMessageResponse getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface RequestMessageOrBuilder extends
      // @@protoc_insertion_point(interface_extends:gateway_service.RequestMessage)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     * The hash identifier used to lookup the required service in the vehicle service gateway configuration file
     * </pre>
     *
     * <code>optional string hash = 1;</code>
     * @return Whether the hash field is set.
     */
    boolean hasHash();
    /**
     * <pre>
     * The hash identifier used to lookup the required service in the vehicle service gateway configuration file
     * </pre>
     *
     * <code>optional string hash = 1;</code>
     * @return The hash.
     */
    java.lang.String getHash();
    /**
     * <pre>
     * The hash identifier used to lookup the required service in the vehicle service gateway configuration file
     * </pre>
     *
     * <code>optional string hash = 1;</code>
     * @return The bytes for hash.
     */
    com.google.protobuf.ByteString
        getHashBytes();

    /**
     * <pre>
     * An opaque identifier to identify a unique request, returned in corresponding response
     * </pre>
     *
     * <code>optional uint32 request_context = 2;</code>
     * @return Whether the requestContext field is set.
     */
    boolean hasRequestContext();
    /**
     * <pre>
     * An opaque identifier to identify a unique request, returned in corresponding response
     * </pre>
     *
     * <code>optional uint32 request_context = 2;</code>
     * @return The requestContext.
     */
    int getRequestContext();

    /**
     * <pre>
     * The number of milliseconds for the service gateway to wait for a service to become available
     * Default 100ms if not set, if service is not available within requested time the request is not actioned
     * and a response of ENUM_OPERATION_STATUS_SERVICE_NOT_AVAILABLE is returned
     * </pre>
     *
     * <code>optional uint32 wait_for_service_in_ms = 3;</code>
     * @return Whether the waitForServiceInMs field is set.
     */
    boolean hasWaitForServiceInMs();
    /**
     * <pre>
     * The number of milliseconds for the service gateway to wait for a service to become available
     * Default 100ms if not set, if service is not available within requested time the request is not actioned
     * and a response of ENUM_OPERATION_STATUS_SERVICE_NOT_AVAILABLE is returned
     * </pre>
     *
     * <code>optional uint32 wait_for_service_in_ms = 3;</code>
     * @return The waitForServiceInMs.
     */
    int getWaitForServiceInMs();

    /**
     * <pre>
     * Required. The number of milliseconds for the service gateway to wait for the specified service to respond
     * If the service does not respond within the timeout the service gateway will discard any context, therefore if
     * the service responds after the timeout the service gateway will not update the failure response even though
     * the service may have succeeded
     * </pre>
     *
     * <code>optional uint32 response_timeout_ms = 4;</code>
     * @return Whether the responseTimeoutMs field is set.
     */
    boolean hasResponseTimeoutMs();
    /**
     * <pre>
     * Required. The number of milliseconds for the service gateway to wait for the specified service to respond
     * If the service does not respond within the timeout the service gateway will discard any context, therefore if
     * the service responds after the timeout the service gateway will not update the failure response even though
     * the service may have succeeded
     * </pre>
     *
     * <code>optional uint32 response_timeout_ms = 4;</code>
     * @return The responseTimeoutMs.
     */
    int getResponseTimeoutMs();

    /**
     * <pre>
     * Protobuf encoded message payload sent to the service
     * </pre>
     *
     * <code>optional bytes message_payload = 5;</code>
     * @return Whether the messagePayload field is set.
     */
    boolean hasMessagePayload();
    /**
     * <pre>
     * Protobuf encoded message payload sent to the service
     * </pre>
     *
     * <code>optional bytes message_payload = 5;</code>
     * @return The messagePayload.
     */
    com.google.protobuf.ByteString getMessagePayload();
  }
  /**
   * <pre>
   * Service Request by VCDP to vehicle service gateway
   * </pre>
   *
   * Protobuf type {@code gateway_service.RequestMessage}
   */
  public static final class RequestMessage extends
      com.google.protobuf.GeneratedMessage implements
      // @@protoc_insertion_point(message_implements:gateway_service.RequestMessage)
      RequestMessageOrBuilder {
  private static final long serialVersionUID = 0L;
    static {
      com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
        com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
        /* major= */ 4,
        /* minor= */ 28,
        /* patch= */ 2,
        /* suffix= */ "",
        RequestMessage.class.getName());
    }
    // Use RequestMessage.newBuilder() to construct.
    private RequestMessage(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
    }
    private RequestMessage() {
      hash_ = "";
      messagePayload_ = com.google.protobuf.ByteString.EMPTY;
    }

    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return gateway_service.Envelope.internal_static_gateway_service_RequestMessage_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return gateway_service.Envelope.internal_static_gateway_service_RequestMessage_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              gateway_service.Envelope.RequestMessage.class, gateway_service.Envelope.RequestMessage.Builder.class);
    }

    private int bitField0_;
    public static final int HASH_FIELD_NUMBER = 1;
    @SuppressWarnings("serial")
    private volatile java.lang.Object hash_ = "";
    /**
     * <pre>
     * The hash identifier used to lookup the required service in the vehicle service gateway configuration file
     * </pre>
     *
     * <code>optional string hash = 1;</code>
     * @return Whether the hash field is set.
     */
    @java.lang.Override
    public boolean hasHash() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <pre>
     * The hash identifier used to lookup the required service in the vehicle service gateway configuration file
     * </pre>
     *
     * <code>optional string hash = 1;</code>
     * @return The hash.
     */
    @java.lang.Override
    public java.lang.String getHash() {
      java.lang.Object ref = hash_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        hash_ = s;
        return s;
      }
    }
    /**
     * <pre>
     * The hash identifier used to lookup the required service in the vehicle service gateway configuration file
     * </pre>
     *
     * <code>optional string hash = 1;</code>
     * @return The bytes for hash.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getHashBytes() {
      java.lang.Object ref = hash_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        hash_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int REQUEST_CONTEXT_FIELD_NUMBER = 2;
    private int requestContext_ = 0;
    /**
     * <pre>
     * An opaque identifier to identify a unique request, returned in corresponding response
     * </pre>
     *
     * <code>optional uint32 request_context = 2;</code>
     * @return Whether the requestContext field is set.
     */
    @java.lang.Override
    public boolean hasRequestContext() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <pre>
     * An opaque identifier to identify a unique request, returned in corresponding response
     * </pre>
     *
     * <code>optional uint32 request_context = 2;</code>
     * @return The requestContext.
     */
    @java.lang.Override
    public int getRequestContext() {
      return requestContext_;
    }

    public static final int WAIT_FOR_SERVICE_IN_MS_FIELD_NUMBER = 3;
    private int waitForServiceInMs_ = 0;
    /**
     * <pre>
     * The number of milliseconds for the service gateway to wait for a service to become available
     * Default 100ms if not set, if service is not available within requested time the request is not actioned
     * and a response of ENUM_OPERATION_STATUS_SERVICE_NOT_AVAILABLE is returned
     * </pre>
     *
     * <code>optional uint32 wait_for_service_in_ms = 3;</code>
     * @return Whether the waitForServiceInMs field is set.
     */
    @java.lang.Override
    public boolean hasWaitForServiceInMs() {
      return ((bitField0_ & 0x00000004) != 0);
    }
    /**
     * <pre>
     * The number of milliseconds for the service gateway to wait for a service to become available
     * Default 100ms if not set, if service is not available within requested time the request is not actioned
     * and a response of ENUM_OPERATION_STATUS_SERVICE_NOT_AVAILABLE is returned
     * </pre>
     *
     * <code>optional uint32 wait_for_service_in_ms = 3;</code>
     * @return The waitForServiceInMs.
     */
    @java.lang.Override
    public int getWaitForServiceInMs() {
      return waitForServiceInMs_;
    }

    public static final int RESPONSE_TIMEOUT_MS_FIELD_NUMBER = 4;
    private int responseTimeoutMs_ = 0;
    /**
     * <pre>
     * Required. The number of milliseconds for the service gateway to wait for the specified service to respond
     * If the service does not respond within the timeout the service gateway will discard any context, therefore if
     * the service responds after the timeout the service gateway will not update the failure response even though
     * the service may have succeeded
     * </pre>
     *
     * <code>optional uint32 response_timeout_ms = 4;</code>
     * @return Whether the responseTimeoutMs field is set.
     */
    @java.lang.Override
    public boolean hasResponseTimeoutMs() {
      return ((bitField0_ & 0x00000008) != 0);
    }
    /**
     * <pre>
     * Required. The number of milliseconds for the service gateway to wait for the specified service to respond
     * If the service does not respond within the timeout the service gateway will discard any context, therefore if
     * the service responds after the timeout the service gateway will not update the failure response even though
     * the service may have succeeded
     * </pre>
     *
     * <code>optional uint32 response_timeout_ms = 4;</code>
     * @return The responseTimeoutMs.
     */
    @java.lang.Override
    public int getResponseTimeoutMs() {
      return responseTimeoutMs_;
    }

    public static final int MESSAGE_PAYLOAD_FIELD_NUMBER = 5;
    private com.google.protobuf.ByteString messagePayload_ = com.google.protobuf.ByteString.EMPTY;
    /**
     * <pre>
     * Protobuf encoded message payload sent to the service
     * </pre>
     *
     * <code>optional bytes message_payload = 5;</code>
     * @return Whether the messagePayload field is set.
     */
    @java.lang.Override
    public boolean hasMessagePayload() {
      return ((bitField0_ & 0x00000010) != 0);
    }
    /**
     * <pre>
     * Protobuf encoded message payload sent to the service
     * </pre>
     *
     * <code>optional bytes message_payload = 5;</code>
     * @return The messagePayload.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString getMessagePayload() {
      return messagePayload_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        com.google.protobuf.GeneratedMessage.writeString(output, 1, hash_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        output.writeUInt32(2, requestContext_);
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        output.writeUInt32(3, waitForServiceInMs_);
      }
      if (((bitField0_ & 0x00000008) != 0)) {
        output.writeUInt32(4, responseTimeoutMs_);
      }
      if (((bitField0_ & 0x00000010) != 0)) {
        output.writeBytes(5, messagePayload_);
      }
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.GeneratedMessage.computeStringSize(1, hash_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(2, requestContext_);
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(3, waitForServiceInMs_);
      }
      if (((bitField0_ & 0x00000008) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(4, responseTimeoutMs_);
      }
      if (((bitField0_ & 0x00000010) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(5, messagePayload_);
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof gateway_service.Envelope.RequestMessage)) {
        return super.equals(obj);
      }
      gateway_service.Envelope.RequestMessage other = (gateway_service.Envelope.RequestMessage) obj;

      if (hasHash() != other.hasHash()) return false;
      if (hasHash()) {
        if (!getHash()
            .equals(other.getHash())) return false;
      }
      if (hasRequestContext() != other.hasRequestContext()) return false;
      if (hasRequestContext()) {
        if (getRequestContext()
            != other.getRequestContext()) return false;
      }
      if (hasWaitForServiceInMs() != other.hasWaitForServiceInMs()) return false;
      if (hasWaitForServiceInMs()) {
        if (getWaitForServiceInMs()
            != other.getWaitForServiceInMs()) return false;
      }
      if (hasResponseTimeoutMs() != other.hasResponseTimeoutMs()) return false;
      if (hasResponseTimeoutMs()) {
        if (getResponseTimeoutMs()
            != other.getResponseTimeoutMs()) return false;
      }
      if (hasMessagePayload() != other.hasMessagePayload()) return false;
      if (hasMessagePayload()) {
        if (!getMessagePayload()
            .equals(other.getMessagePayload())) return false;
      }
      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasHash()) {
        hash = (37 * hash) + HASH_FIELD_NUMBER;
        hash = (53 * hash) + getHash().hashCode();
      }
      if (hasRequestContext()) {
        hash = (37 * hash) + REQUEST_CONTEXT_FIELD_NUMBER;
        hash = (53 * hash) + getRequestContext();
      }
      if (hasWaitForServiceInMs()) {
        hash = (37 * hash) + WAIT_FOR_SERVICE_IN_MS_FIELD_NUMBER;
        hash = (53 * hash) + getWaitForServiceInMs();
      }
      if (hasResponseTimeoutMs()) {
        hash = (37 * hash) + RESPONSE_TIMEOUT_MS_FIELD_NUMBER;
        hash = (53 * hash) + getResponseTimeoutMs();
      }
      if (hasMessagePayload()) {
        hash = (37 * hash) + MESSAGE_PAYLOAD_FIELD_NUMBER;
        hash = (53 * hash) + getMessagePayload().hashCode();
      }
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static gateway_service.Envelope.RequestMessage parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static gateway_service.Envelope.RequestMessage parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static gateway_service.Envelope.RequestMessage parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static gateway_service.Envelope.RequestMessage parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static gateway_service.Envelope.RequestMessage parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static gateway_service.Envelope.RequestMessage parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static gateway_service.Envelope.RequestMessage parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input);
    }
    public static gateway_service.Envelope.RequestMessage parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public static gateway_service.Envelope.RequestMessage parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseDelimitedWithIOException(PARSER, input);
    }

    public static gateway_service.Envelope.RequestMessage parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static gateway_service.Envelope.RequestMessage parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input);
    }
    public static gateway_service.Envelope.RequestMessage parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(gateway_service.Envelope.RequestMessage prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     * Service Request by VCDP to vehicle service gateway
     * </pre>
     *
     * Protobuf type {@code gateway_service.RequestMessage}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:gateway_service.RequestMessage)
        gateway_service.Envelope.RequestMessageOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return gateway_service.Envelope.internal_static_gateway_service_RequestMessage_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return gateway_service.Envelope.internal_static_gateway_service_RequestMessage_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                gateway_service.Envelope.RequestMessage.class, gateway_service.Envelope.RequestMessage.Builder.class);
      }

      // Construct using gateway_service.Envelope.RequestMessage.newBuilder()
      private Builder() {

      }

      private Builder(
          com.google.protobuf.GeneratedMessage.BuilderParent parent) {
        super(parent);

      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        bitField0_ = 0;
        hash_ = "";
        requestContext_ = 0;
        waitForServiceInMs_ = 0;
        responseTimeoutMs_ = 0;
        messagePayload_ = com.google.protobuf.ByteString.EMPTY;
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return gateway_service.Envelope.internal_static_gateway_service_RequestMessage_descriptor;
      }

      @java.lang.Override
      public gateway_service.Envelope.RequestMessage getDefaultInstanceForType() {
        return gateway_service.Envelope.RequestMessage.getDefaultInstance();
      }

      @java.lang.Override
      public gateway_service.Envelope.RequestMessage build() {
        gateway_service.Envelope.RequestMessage result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public gateway_service.Envelope.RequestMessage buildPartial() {
        gateway_service.Envelope.RequestMessage result = new gateway_service.Envelope.RequestMessage(this);
        if (bitField0_ != 0) { buildPartial0(result); }
        onBuilt();
        return result;
      }

      private void buildPartial0(gateway_service.Envelope.RequestMessage result) {
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.hash_ = hash_;
          to_bitField0_ |= 0x00000001;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          result.requestContext_ = requestContext_;
          to_bitField0_ |= 0x00000002;
        }
        if (((from_bitField0_ & 0x00000004) != 0)) {
          result.waitForServiceInMs_ = waitForServiceInMs_;
          to_bitField0_ |= 0x00000004;
        }
        if (((from_bitField0_ & 0x00000008) != 0)) {
          result.responseTimeoutMs_ = responseTimeoutMs_;
          to_bitField0_ |= 0x00000008;
        }
        if (((from_bitField0_ & 0x00000010) != 0)) {
          result.messagePayload_ = messagePayload_;
          to_bitField0_ |= 0x00000010;
        }
        result.bitField0_ |= to_bitField0_;
      }

      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof gateway_service.Envelope.RequestMessage) {
          return mergeFrom((gateway_service.Envelope.RequestMessage)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(gateway_service.Envelope.RequestMessage other) {
        if (other == gateway_service.Envelope.RequestMessage.getDefaultInstance()) return this;
        if (other.hasHash()) {
          hash_ = other.hash_;
          bitField0_ |= 0x00000001;
          onChanged();
        }
        if (other.hasRequestContext()) {
          setRequestContext(other.getRequestContext());
        }
        if (other.hasWaitForServiceInMs()) {
          setWaitForServiceInMs(other.getWaitForServiceInMs());
        }
        if (other.hasResponseTimeoutMs()) {
          setResponseTimeoutMs(other.getResponseTimeoutMs());
        }
        if (other.hasMessagePayload()) {
          setMessagePayload(other.getMessagePayload());
        }
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 10: {
                hash_ = input.readStringRequireUtf8();
                bitField0_ |= 0x00000001;
                break;
              } // case 10
              case 16: {
                requestContext_ = input.readUInt32();
                bitField0_ |= 0x00000002;
                break;
              } // case 16
              case 24: {
                waitForServiceInMs_ = input.readUInt32();
                bitField0_ |= 0x00000004;
                break;
              } // case 24
              case 32: {
                responseTimeoutMs_ = input.readUInt32();
                bitField0_ |= 0x00000008;
                break;
              } // case 32
              case 42: {
                messagePayload_ = input.readBytes();
                bitField0_ |= 0x00000010;
                break;
              } // case 42
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }
      private int bitField0_;

      private java.lang.Object hash_ = "";
      /**
       * <pre>
       * The hash identifier used to lookup the required service in the vehicle service gateway configuration file
       * </pre>
       *
       * <code>optional string hash = 1;</code>
       * @return Whether the hash field is set.
       */
      public boolean hasHash() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <pre>
       * The hash identifier used to lookup the required service in the vehicle service gateway configuration file
       * </pre>
       *
       * <code>optional string hash = 1;</code>
       * @return The hash.
       */
      public java.lang.String getHash() {
        java.lang.Object ref = hash_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          hash_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       * The hash identifier used to lookup the required service in the vehicle service gateway configuration file
       * </pre>
       *
       * <code>optional string hash = 1;</code>
       * @return The bytes for hash.
       */
      public com.google.protobuf.ByteString
          getHashBytes() {
        java.lang.Object ref = hash_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          hash_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       * The hash identifier used to lookup the required service in the vehicle service gateway configuration file
       * </pre>
       *
       * <code>optional string hash = 1;</code>
       * @param value The hash to set.
       * @return This builder for chaining.
       */
      public Builder setHash(
          java.lang.String value) {
        if (value == null) { throw new NullPointerException(); }
        hash_ = value;
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * The hash identifier used to lookup the required service in the vehicle service gateway configuration file
       * </pre>
       *
       * <code>optional string hash = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearHash() {
        hash_ = getDefaultInstance().getHash();
        bitField0_ = (bitField0_ & ~0x00000001);
        onChanged();
        return this;
      }
      /**
       * <pre>
       * The hash identifier used to lookup the required service in the vehicle service gateway configuration file
       * </pre>
       *
       * <code>optional string hash = 1;</code>
       * @param value The bytes for hash to set.
       * @return This builder for chaining.
       */
      public Builder setHashBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        checkByteStringIsUtf8(value);
        hash_ = value;
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }

      private int requestContext_ ;
      /**
       * <pre>
       * An opaque identifier to identify a unique request, returned in corresponding response
       * </pre>
       *
       * <code>optional uint32 request_context = 2;</code>
       * @return Whether the requestContext field is set.
       */
      @java.lang.Override
      public boolean hasRequestContext() {
        return ((bitField0_ & 0x00000002) != 0);
      }
      /**
       * <pre>
       * An opaque identifier to identify a unique request, returned in corresponding response
       * </pre>
       *
       * <code>optional uint32 request_context = 2;</code>
       * @return The requestContext.
       */
      @java.lang.Override
      public int getRequestContext() {
        return requestContext_;
      }
      /**
       * <pre>
       * An opaque identifier to identify a unique request, returned in corresponding response
       * </pre>
       *
       * <code>optional uint32 request_context = 2;</code>
       * @param value The requestContext to set.
       * @return This builder for chaining.
       */
      public Builder setRequestContext(int value) {

        requestContext_ = value;
        bitField0_ |= 0x00000002;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * An opaque identifier to identify a unique request, returned in corresponding response
       * </pre>
       *
       * <code>optional uint32 request_context = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearRequestContext() {
        bitField0_ = (bitField0_ & ~0x00000002);
        requestContext_ = 0;
        onChanged();
        return this;
      }

      private int waitForServiceInMs_ ;
      /**
       * <pre>
       * The number of milliseconds for the service gateway to wait for a service to become available
       * Default 100ms if not set, if service is not available within requested time the request is not actioned
       * and a response of ENUM_OPERATION_STATUS_SERVICE_NOT_AVAILABLE is returned
       * </pre>
       *
       * <code>optional uint32 wait_for_service_in_ms = 3;</code>
       * @return Whether the waitForServiceInMs field is set.
       */
      @java.lang.Override
      public boolean hasWaitForServiceInMs() {
        return ((bitField0_ & 0x00000004) != 0);
      }
      /**
       * <pre>
       * The number of milliseconds for the service gateway to wait for a service to become available
       * Default 100ms if not set, if service is not available within requested time the request is not actioned
       * and a response of ENUM_OPERATION_STATUS_SERVICE_NOT_AVAILABLE is returned
       * </pre>
       *
       * <code>optional uint32 wait_for_service_in_ms = 3;</code>
       * @return The waitForServiceInMs.
       */
      @java.lang.Override
      public int getWaitForServiceInMs() {
        return waitForServiceInMs_;
      }
      /**
       * <pre>
       * The number of milliseconds for the service gateway to wait for a service to become available
       * Default 100ms if not set, if service is not available within requested time the request is not actioned
       * and a response of ENUM_OPERATION_STATUS_SERVICE_NOT_AVAILABLE is returned
       * </pre>
       *
       * <code>optional uint32 wait_for_service_in_ms = 3;</code>
       * @param value The waitForServiceInMs to set.
       * @return This builder for chaining.
       */
      public Builder setWaitForServiceInMs(int value) {

        waitForServiceInMs_ = value;
        bitField0_ |= 0x00000004;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * The number of milliseconds for the service gateway to wait for a service to become available
       * Default 100ms if not set, if service is not available within requested time the request is not actioned
       * and a response of ENUM_OPERATION_STATUS_SERVICE_NOT_AVAILABLE is returned
       * </pre>
       *
       * <code>optional uint32 wait_for_service_in_ms = 3;</code>
       * @return This builder for chaining.
       */
      public Builder clearWaitForServiceInMs() {
        bitField0_ = (bitField0_ & ~0x00000004);
        waitForServiceInMs_ = 0;
        onChanged();
        return this;
      }

      private int responseTimeoutMs_ ;
      /**
       * <pre>
       * Required. The number of milliseconds for the service gateway to wait for the specified service to respond
       * If the service does not respond within the timeout the service gateway will discard any context, therefore if
       * the service responds after the timeout the service gateway will not update the failure response even though
       * the service may have succeeded
       * </pre>
       *
       * <code>optional uint32 response_timeout_ms = 4;</code>
       * @return Whether the responseTimeoutMs field is set.
       */
      @java.lang.Override
      public boolean hasResponseTimeoutMs() {
        return ((bitField0_ & 0x00000008) != 0);
      }
      /**
       * <pre>
       * Required. The number of milliseconds for the service gateway to wait for the specified service to respond
       * If the service does not respond within the timeout the service gateway will discard any context, therefore if
       * the service responds after the timeout the service gateway will not update the failure response even though
       * the service may have succeeded
       * </pre>
       *
       * <code>optional uint32 response_timeout_ms = 4;</code>
       * @return The responseTimeoutMs.
       */
      @java.lang.Override
      public int getResponseTimeoutMs() {
        return responseTimeoutMs_;
      }
      /**
       * <pre>
       * Required. The number of milliseconds for the service gateway to wait for the specified service to respond
       * If the service does not respond within the timeout the service gateway will discard any context, therefore if
       * the service responds after the timeout the service gateway will not update the failure response even though
       * the service may have succeeded
       * </pre>
       *
       * <code>optional uint32 response_timeout_ms = 4;</code>
       * @param value The responseTimeoutMs to set.
       * @return This builder for chaining.
       */
      public Builder setResponseTimeoutMs(int value) {

        responseTimeoutMs_ = value;
        bitField0_ |= 0x00000008;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * Required. The number of milliseconds for the service gateway to wait for the specified service to respond
       * If the service does not respond within the timeout the service gateway will discard any context, therefore if
       * the service responds after the timeout the service gateway will not update the failure response even though
       * the service may have succeeded
       * </pre>
       *
       * <code>optional uint32 response_timeout_ms = 4;</code>
       * @return This builder for chaining.
       */
      public Builder clearResponseTimeoutMs() {
        bitField0_ = (bitField0_ & ~0x00000008);
        responseTimeoutMs_ = 0;
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString messagePayload_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <pre>
       * Protobuf encoded message payload sent to the service
       * </pre>
       *
       * <code>optional bytes message_payload = 5;</code>
       * @return Whether the messagePayload field is set.
       */
      @java.lang.Override
      public boolean hasMessagePayload() {
        return ((bitField0_ & 0x00000010) != 0);
      }
      /**
       * <pre>
       * Protobuf encoded message payload sent to the service
       * </pre>
       *
       * <code>optional bytes message_payload = 5;</code>
       * @return The messagePayload.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString getMessagePayload() {
        return messagePayload_;
      }
      /**
       * <pre>
       * Protobuf encoded message payload sent to the service
       * </pre>
       *
       * <code>optional bytes message_payload = 5;</code>
       * @param value The messagePayload to set.
       * @return This builder for chaining.
       */
      public Builder setMessagePayload(com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        messagePayload_ = value;
        bitField0_ |= 0x00000010;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * Protobuf encoded message payload sent to the service
       * </pre>
       *
       * <code>optional bytes message_payload = 5;</code>
       * @return This builder for chaining.
       */
      public Builder clearMessagePayload() {
        bitField0_ = (bitField0_ & ~0x00000010);
        messagePayload_ = getDefaultInstance().getMessagePayload();
        onChanged();
        return this;
      }

      // @@protoc_insertion_point(builder_scope:gateway_service.RequestMessage)
    }

    // @@protoc_insertion_point(class_scope:gateway_service.RequestMessage)
    private static final gateway_service.Envelope.RequestMessage DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new gateway_service.Envelope.RequestMessage();
    }

    public static gateway_service.Envelope.RequestMessage getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<RequestMessage>
        PARSER = new com.google.protobuf.AbstractParser<RequestMessage>() {
      @java.lang.Override
      public RequestMessage parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<RequestMessage> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<RequestMessage> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public gateway_service.Envelope.RequestMessage getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface ResponseMessageOrBuilder extends
      // @@protoc_insertion_point(interface_extends:gateway_service.ResponseMessage)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     * The status result of the request
     * </pre>
     *
     * <code>.gateway_service.EnumOperationStatus result = 1;</code>
     * @return The enum numeric value on the wire for result.
     */
    int getResultValue();
    /**
     * <pre>
     * The status result of the request
     * </pre>
     *
     * <code>.gateway_service.EnumOperationStatus result = 1;</code>
     * @return The result.
     */
    gateway_service.Envelope.EnumOperationStatus getResult();

    /**
     * <pre>
     * An opaque identifier to identify the request message this response relates to.
     * </pre>
     *
     * <code>optional uint32 request_context = 2;</code>
     * @return Whether the requestContext field is set.
     */
    boolean hasRequestContext();
    /**
     * <pre>
     * An opaque identifier to identify the request message this response relates to.
     * </pre>
     *
     * <code>optional uint32 request_context = 2;</code>
     * @return The requestContext.
     */
    int getRequestContext();

    /**
     * <pre>
     * The hash identifier used to determine the event this message relates to.
     * </pre>
     *
     * <code>optional string hash = 3;</code>
     * @return Whether the hash field is set.
     */
    boolean hasHash();
    /**
     * <pre>
     * The hash identifier used to determine the event this message relates to.
     * </pre>
     *
     * <code>optional string hash = 3;</code>
     * @return The hash.
     */
    java.lang.String getHash();
    /**
     * <pre>
     * The hash identifier used to determine the event this message relates to.
     * </pre>
     *
     * <code>optional string hash = 3;</code>
     * @return The bytes for hash.
     */
    com.google.protobuf.ByteString
        getHashBytes();

    /**
     * <pre>
     * Protobuf encoded message payload returned from the service
     * </pre>
     *
     * <code>optional bytes message_payload = 4;</code>
     * @return Whether the messagePayload field is set.
     */
    boolean hasMessagePayload();
    /**
     * <pre>
     * Protobuf encoded message payload returned from the service
     * </pre>
     *
     * <code>optional bytes message_payload = 4;</code>
     * @return The messagePayload.
     */
    com.google.protobuf.ByteString getMessagePayload();
  }
  /**
   * <pre>
   * The response to a RequestMessage
   * </pre>
   *
   * Protobuf type {@code gateway_service.ResponseMessage}
   */
  public static final class ResponseMessage extends
      com.google.protobuf.GeneratedMessage implements
      // @@protoc_insertion_point(message_implements:gateway_service.ResponseMessage)
      ResponseMessageOrBuilder {
  private static final long serialVersionUID = 0L;
    static {
      com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
        com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
        /* major= */ 4,
        /* minor= */ 28,
        /* patch= */ 2,
        /* suffix= */ "",
        ResponseMessage.class.getName());
    }
    // Use ResponseMessage.newBuilder() to construct.
    private ResponseMessage(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
    }
    private ResponseMessage() {
      result_ = 0;
      hash_ = "";
      messagePayload_ = com.google.protobuf.ByteString.EMPTY;
    }

    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return gateway_service.Envelope.internal_static_gateway_service_ResponseMessage_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return gateway_service.Envelope.internal_static_gateway_service_ResponseMessage_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              gateway_service.Envelope.ResponseMessage.class, gateway_service.Envelope.ResponseMessage.Builder.class);
    }

    private int bitField0_;
    public static final int RESULT_FIELD_NUMBER = 1;
    private int result_ = 0;
    /**
     * <pre>
     * The status result of the request
     * </pre>
     *
     * <code>.gateway_service.EnumOperationStatus result = 1;</code>
     * @return The enum numeric value on the wire for result.
     */
    @java.lang.Override public int getResultValue() {
      return result_;
    }
    /**
     * <pre>
     * The status result of the request
     * </pre>
     *
     * <code>.gateway_service.EnumOperationStatus result = 1;</code>
     * @return The result.
     */
    @java.lang.Override public gateway_service.Envelope.EnumOperationStatus getResult() {
      gateway_service.Envelope.EnumOperationStatus result = gateway_service.Envelope.EnumOperationStatus.forNumber(result_);
      return result == null ? gateway_service.Envelope.EnumOperationStatus.UNRECOGNIZED : result;
    }

    public static final int REQUEST_CONTEXT_FIELD_NUMBER = 2;
    private int requestContext_ = 0;
    /**
     * <pre>
     * An opaque identifier to identify the request message this response relates to.
     * </pre>
     *
     * <code>optional uint32 request_context = 2;</code>
     * @return Whether the requestContext field is set.
     */
    @java.lang.Override
    public boolean hasRequestContext() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <pre>
     * An opaque identifier to identify the request message this response relates to.
     * </pre>
     *
     * <code>optional uint32 request_context = 2;</code>
     * @return The requestContext.
     */
    @java.lang.Override
    public int getRequestContext() {
      return requestContext_;
    }

    public static final int HASH_FIELD_NUMBER = 3;
    @SuppressWarnings("serial")
    private volatile java.lang.Object hash_ = "";
    /**
     * <pre>
     * The hash identifier used to determine the event this message relates to.
     * </pre>
     *
     * <code>optional string hash = 3;</code>
     * @return Whether the hash field is set.
     */
    @java.lang.Override
    public boolean hasHash() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <pre>
     * The hash identifier used to determine the event this message relates to.
     * </pre>
     *
     * <code>optional string hash = 3;</code>
     * @return The hash.
     */
    @java.lang.Override
    public java.lang.String getHash() {
      java.lang.Object ref = hash_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        hash_ = s;
        return s;
      }
    }
    /**
     * <pre>
     * The hash identifier used to determine the event this message relates to.
     * </pre>
     *
     * <code>optional string hash = 3;</code>
     * @return The bytes for hash.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getHashBytes() {
      java.lang.Object ref = hash_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        hash_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int MESSAGE_PAYLOAD_FIELD_NUMBER = 4;
    private com.google.protobuf.ByteString messagePayload_ = com.google.protobuf.ByteString.EMPTY;
    /**
     * <pre>
     * Protobuf encoded message payload returned from the service
     * </pre>
     *
     * <code>optional bytes message_payload = 4;</code>
     * @return Whether the messagePayload field is set.
     */
    @java.lang.Override
    public boolean hasMessagePayload() {
      return ((bitField0_ & 0x00000004) != 0);
    }
    /**
     * <pre>
     * Protobuf encoded message payload returned from the service
     * </pre>
     *
     * <code>optional bytes message_payload = 4;</code>
     * @return The messagePayload.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString getMessagePayload() {
      return messagePayload_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (result_ != gateway_service.Envelope.EnumOperationStatus.ENUM_OPERATION_STATUS_UNSPECIFIED.getNumber()) {
        output.writeEnum(1, result_);
      }
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeUInt32(2, requestContext_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        com.google.protobuf.GeneratedMessage.writeString(output, 3, hash_);
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        output.writeBytes(4, messagePayload_);
      }
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (result_ != gateway_service.Envelope.EnumOperationStatus.ENUM_OPERATION_STATUS_UNSPECIFIED.getNumber()) {
        size += com.google.protobuf.CodedOutputStream
          .computeEnumSize(1, result_);
      }
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(2, requestContext_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        size += com.google.protobuf.GeneratedMessage.computeStringSize(3, hash_);
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(4, messagePayload_);
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof gateway_service.Envelope.ResponseMessage)) {
        return super.equals(obj);
      }
      gateway_service.Envelope.ResponseMessage other = (gateway_service.Envelope.ResponseMessage) obj;

      if (result_ != other.result_) return false;
      if (hasRequestContext() != other.hasRequestContext()) return false;
      if (hasRequestContext()) {
        if (getRequestContext()
            != other.getRequestContext()) return false;
      }
      if (hasHash() != other.hasHash()) return false;
      if (hasHash()) {
        if (!getHash()
            .equals(other.getHash())) return false;
      }
      if (hasMessagePayload() != other.hasMessagePayload()) return false;
      if (hasMessagePayload()) {
        if (!getMessagePayload()
            .equals(other.getMessagePayload())) return false;
      }
      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + RESULT_FIELD_NUMBER;
      hash = (53 * hash) + result_;
      if (hasRequestContext()) {
        hash = (37 * hash) + REQUEST_CONTEXT_FIELD_NUMBER;
        hash = (53 * hash) + getRequestContext();
      }
      if (hasHash()) {
        hash = (37 * hash) + HASH_FIELD_NUMBER;
        hash = (53 * hash) + getHash().hashCode();
      }
      if (hasMessagePayload()) {
        hash = (37 * hash) + MESSAGE_PAYLOAD_FIELD_NUMBER;
        hash = (53 * hash) + getMessagePayload().hashCode();
      }
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static gateway_service.Envelope.ResponseMessage parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static gateway_service.Envelope.ResponseMessage parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static gateway_service.Envelope.ResponseMessage parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static gateway_service.Envelope.ResponseMessage parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static gateway_service.Envelope.ResponseMessage parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static gateway_service.Envelope.ResponseMessage parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static gateway_service.Envelope.ResponseMessage parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input);
    }
    public static gateway_service.Envelope.ResponseMessage parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public static gateway_service.Envelope.ResponseMessage parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseDelimitedWithIOException(PARSER, input);
    }

    public static gateway_service.Envelope.ResponseMessage parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static gateway_service.Envelope.ResponseMessage parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input);
    }
    public static gateway_service.Envelope.ResponseMessage parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(gateway_service.Envelope.ResponseMessage prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     * The response to a RequestMessage
     * </pre>
     *
     * Protobuf type {@code gateway_service.ResponseMessage}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:gateway_service.ResponseMessage)
        gateway_service.Envelope.ResponseMessageOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return gateway_service.Envelope.internal_static_gateway_service_ResponseMessage_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return gateway_service.Envelope.internal_static_gateway_service_ResponseMessage_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                gateway_service.Envelope.ResponseMessage.class, gateway_service.Envelope.ResponseMessage.Builder.class);
      }

      // Construct using gateway_service.Envelope.ResponseMessage.newBuilder()
      private Builder() {

      }

      private Builder(
          com.google.protobuf.GeneratedMessage.BuilderParent parent) {
        super(parent);

      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        bitField0_ = 0;
        result_ = 0;
        requestContext_ = 0;
        hash_ = "";
        messagePayload_ = com.google.protobuf.ByteString.EMPTY;
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return gateway_service.Envelope.internal_static_gateway_service_ResponseMessage_descriptor;
      }

      @java.lang.Override
      public gateway_service.Envelope.ResponseMessage getDefaultInstanceForType() {
        return gateway_service.Envelope.ResponseMessage.getDefaultInstance();
      }

      @java.lang.Override
      public gateway_service.Envelope.ResponseMessage build() {
        gateway_service.Envelope.ResponseMessage result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public gateway_service.Envelope.ResponseMessage buildPartial() {
        gateway_service.Envelope.ResponseMessage result = new gateway_service.Envelope.ResponseMessage(this);
        if (bitField0_ != 0) { buildPartial0(result); }
        onBuilt();
        return result;
      }

      private void buildPartial0(gateway_service.Envelope.ResponseMessage result) {
        int from_bitField0_ = bitField0_;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.result_ = result_;
        }
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000002) != 0)) {
          result.requestContext_ = requestContext_;
          to_bitField0_ |= 0x00000001;
        }
        if (((from_bitField0_ & 0x00000004) != 0)) {
          result.hash_ = hash_;
          to_bitField0_ |= 0x00000002;
        }
        if (((from_bitField0_ & 0x00000008) != 0)) {
          result.messagePayload_ = messagePayload_;
          to_bitField0_ |= 0x00000004;
        }
        result.bitField0_ |= to_bitField0_;
      }

      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof gateway_service.Envelope.ResponseMessage) {
          return mergeFrom((gateway_service.Envelope.ResponseMessage)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(gateway_service.Envelope.ResponseMessage other) {
        if (other == gateway_service.Envelope.ResponseMessage.getDefaultInstance()) return this;
        if (other.result_ != 0) {
          setResultValue(other.getResultValue());
        }
        if (other.hasRequestContext()) {
          setRequestContext(other.getRequestContext());
        }
        if (other.hasHash()) {
          hash_ = other.hash_;
          bitField0_ |= 0x00000004;
          onChanged();
        }
        if (other.hasMessagePayload()) {
          setMessagePayload(other.getMessagePayload());
        }
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 8: {
                result_ = input.readEnum();
                bitField0_ |= 0x00000001;
                break;
              } // case 8
              case 16: {
                requestContext_ = input.readUInt32();
                bitField0_ |= 0x00000002;
                break;
              } // case 16
              case 26: {
                hash_ = input.readStringRequireUtf8();
                bitField0_ |= 0x00000004;
                break;
              } // case 26
              case 34: {
                messagePayload_ = input.readBytes();
                bitField0_ |= 0x00000008;
                break;
              } // case 34
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }
      private int bitField0_;

      private int result_ = 0;
      /**
       * <pre>
       * The status result of the request
       * </pre>
       *
       * <code>.gateway_service.EnumOperationStatus result = 1;</code>
       * @return The enum numeric value on the wire for result.
       */
      @java.lang.Override public int getResultValue() {
        return result_;
      }
      /**
       * <pre>
       * The status result of the request
       * </pre>
       *
       * <code>.gateway_service.EnumOperationStatus result = 1;</code>
       * @param value The enum numeric value on the wire for result to set.
       * @return This builder for chaining.
       */
      public Builder setResultValue(int value) {
        result_ = value;
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * The status result of the request
       * </pre>
       *
       * <code>.gateway_service.EnumOperationStatus result = 1;</code>
       * @return The result.
       */
      @java.lang.Override
      public gateway_service.Envelope.EnumOperationStatus getResult() {
        gateway_service.Envelope.EnumOperationStatus result = gateway_service.Envelope.EnumOperationStatus.forNumber(result_);
        return result == null ? gateway_service.Envelope.EnumOperationStatus.UNRECOGNIZED : result;
      }
      /**
       * <pre>
       * The status result of the request
       * </pre>
       *
       * <code>.gateway_service.EnumOperationStatus result = 1;</code>
       * @param value The result to set.
       * @return This builder for chaining.
       */
      public Builder setResult(gateway_service.Envelope.EnumOperationStatus value) {
        if (value == null) {
          throw new NullPointerException();
        }
        bitField0_ |= 0x00000001;
        result_ = value.getNumber();
        onChanged();
        return this;
      }
      /**
       * <pre>
       * The status result of the request
       * </pre>
       *
       * <code>.gateway_service.EnumOperationStatus result = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearResult() {
        bitField0_ = (bitField0_ & ~0x00000001);
        result_ = 0;
        onChanged();
        return this;
      }

      private int requestContext_ ;
      /**
       * <pre>
       * An opaque identifier to identify the request message this response relates to.
       * </pre>
       *
       * <code>optional uint32 request_context = 2;</code>
       * @return Whether the requestContext field is set.
       */
      @java.lang.Override
      public boolean hasRequestContext() {
        return ((bitField0_ & 0x00000002) != 0);
      }
      /**
       * <pre>
       * An opaque identifier to identify the request message this response relates to.
       * </pre>
       *
       * <code>optional uint32 request_context = 2;</code>
       * @return The requestContext.
       */
      @java.lang.Override
      public int getRequestContext() {
        return requestContext_;
      }
      /**
       * <pre>
       * An opaque identifier to identify the request message this response relates to.
       * </pre>
       *
       * <code>optional uint32 request_context = 2;</code>
       * @param value The requestContext to set.
       * @return This builder for chaining.
       */
      public Builder setRequestContext(int value) {

        requestContext_ = value;
        bitField0_ |= 0x00000002;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * An opaque identifier to identify the request message this response relates to.
       * </pre>
       *
       * <code>optional uint32 request_context = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearRequestContext() {
        bitField0_ = (bitField0_ & ~0x00000002);
        requestContext_ = 0;
        onChanged();
        return this;
      }

      private java.lang.Object hash_ = "";
      /**
       * <pre>
       * The hash identifier used to determine the event this message relates to.
       * </pre>
       *
       * <code>optional string hash = 3;</code>
       * @return Whether the hash field is set.
       */
      public boolean hasHash() {
        return ((bitField0_ & 0x00000004) != 0);
      }
      /**
       * <pre>
       * The hash identifier used to determine the event this message relates to.
       * </pre>
       *
       * <code>optional string hash = 3;</code>
       * @return The hash.
       */
      public java.lang.String getHash() {
        java.lang.Object ref = hash_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          hash_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       * The hash identifier used to determine the event this message relates to.
       * </pre>
       *
       * <code>optional string hash = 3;</code>
       * @return The bytes for hash.
       */
      public com.google.protobuf.ByteString
          getHashBytes() {
        java.lang.Object ref = hash_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          hash_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       * The hash identifier used to determine the event this message relates to.
       * </pre>
       *
       * <code>optional string hash = 3;</code>
       * @param value The hash to set.
       * @return This builder for chaining.
       */
      public Builder setHash(
          java.lang.String value) {
        if (value == null) { throw new NullPointerException(); }
        hash_ = value;
        bitField0_ |= 0x00000004;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * The hash identifier used to determine the event this message relates to.
       * </pre>
       *
       * <code>optional string hash = 3;</code>
       * @return This builder for chaining.
       */
      public Builder clearHash() {
        hash_ = getDefaultInstance().getHash();
        bitField0_ = (bitField0_ & ~0x00000004);
        onChanged();
        return this;
      }
      /**
       * <pre>
       * The hash identifier used to determine the event this message relates to.
       * </pre>
       *
       * <code>optional string hash = 3;</code>
       * @param value The bytes for hash to set.
       * @return This builder for chaining.
       */
      public Builder setHashBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        checkByteStringIsUtf8(value);
        hash_ = value;
        bitField0_ |= 0x00000004;
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString messagePayload_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <pre>
       * Protobuf encoded message payload returned from the service
       * </pre>
       *
       * <code>optional bytes message_payload = 4;</code>
       * @return Whether the messagePayload field is set.
       */
      @java.lang.Override
      public boolean hasMessagePayload() {
        return ((bitField0_ & 0x00000008) != 0);
      }
      /**
       * <pre>
       * Protobuf encoded message payload returned from the service
       * </pre>
       *
       * <code>optional bytes message_payload = 4;</code>
       * @return The messagePayload.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString getMessagePayload() {
        return messagePayload_;
      }
      /**
       * <pre>
       * Protobuf encoded message payload returned from the service
       * </pre>
       *
       * <code>optional bytes message_payload = 4;</code>
       * @param value The messagePayload to set.
       * @return This builder for chaining.
       */
      public Builder setMessagePayload(com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        messagePayload_ = value;
        bitField0_ |= 0x00000008;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * Protobuf encoded message payload returned from the service
       * </pre>
       *
       * <code>optional bytes message_payload = 4;</code>
       * @return This builder for chaining.
       */
      public Builder clearMessagePayload() {
        bitField0_ = (bitField0_ & ~0x00000008);
        messagePayload_ = getDefaultInstance().getMessagePayload();
        onChanged();
        return this;
      }

      // @@protoc_insertion_point(builder_scope:gateway_service.ResponseMessage)
    }

    // @@protoc_insertion_point(class_scope:gateway_service.ResponseMessage)
    private static final gateway_service.Envelope.ResponseMessage DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new gateway_service.Envelope.ResponseMessage();
    }

    public static gateway_service.Envelope.ResponseMessage getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<ResponseMessage>
        PARSER = new com.google.protobuf.AbstractParser<ResponseMessage>() {
      @java.lang.Override
      public ResponseMessage parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<ResponseMessage> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<ResponseMessage> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public gateway_service.Envelope.ResponseMessage getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface EventMessageOrBuilder extends
      // @@protoc_insertion_point(interface_extends:gateway_service.EventMessage)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     * The hash identifier used to determine the event this message relates to.
     * </pre>
     *
     * <code>optional string hash = 1;</code>
     * @return Whether the hash field is set.
     */
    boolean hasHash();
    /**
     * <pre>
     * The hash identifier used to determine the event this message relates to.
     * </pre>
     *
     * <code>optional string hash = 1;</code>
     * @return The hash.
     */
    java.lang.String getHash();
    /**
     * <pre>
     * The hash identifier used to determine the event this message relates to.
     * </pre>
     *
     * <code>optional string hash = 1;</code>
     * @return The bytes for hash.
     */
    com.google.protobuf.ByteString
        getHashBytes();

    /**
     * <pre>
     * Protobuf encoded message payload provided by the service
     * </pre>
     *
     * <code>optional bytes message_payload = 2;</code>
     * @return Whether the messagePayload field is set.
     */
    boolean hasMessagePayload();
    /**
     * <pre>
     * Protobuf encoded message payload provided by the service
     * </pre>
     *
     * <code>optional bytes message_payload = 2;</code>
     * @return The messagePayload.
     */
    com.google.protobuf.ByteString getMessagePayload();
  }
  /**
   * <pre>
   * Data received from any event that will be published to VCDP
   * </pre>
   *
   * Protobuf type {@code gateway_service.EventMessage}
   */
  public static final class EventMessage extends
      com.google.protobuf.GeneratedMessage implements
      // @@protoc_insertion_point(message_implements:gateway_service.EventMessage)
      EventMessageOrBuilder {
  private static final long serialVersionUID = 0L;
    static {
      com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
        com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
        /* major= */ 4,
        /* minor= */ 28,
        /* patch= */ 2,
        /* suffix= */ "",
        EventMessage.class.getName());
    }
    // Use EventMessage.newBuilder() to construct.
    private EventMessage(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
    }
    private EventMessage() {
      hash_ = "";
      messagePayload_ = com.google.protobuf.ByteString.EMPTY;
    }

    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return gateway_service.Envelope.internal_static_gateway_service_EventMessage_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return gateway_service.Envelope.internal_static_gateway_service_EventMessage_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              gateway_service.Envelope.EventMessage.class, gateway_service.Envelope.EventMessage.Builder.class);
    }

    private int bitField0_;
    public static final int HASH_FIELD_NUMBER = 1;
    @SuppressWarnings("serial")
    private volatile java.lang.Object hash_ = "";
    /**
     * <pre>
     * The hash identifier used to determine the event this message relates to.
     * </pre>
     *
     * <code>optional string hash = 1;</code>
     * @return Whether the hash field is set.
     */
    @java.lang.Override
    public boolean hasHash() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <pre>
     * The hash identifier used to determine the event this message relates to.
     * </pre>
     *
     * <code>optional string hash = 1;</code>
     * @return The hash.
     */
    @java.lang.Override
    public java.lang.String getHash() {
      java.lang.Object ref = hash_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        hash_ = s;
        return s;
      }
    }
    /**
     * <pre>
     * The hash identifier used to determine the event this message relates to.
     * </pre>
     *
     * <code>optional string hash = 1;</code>
     * @return The bytes for hash.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getHashBytes() {
      java.lang.Object ref = hash_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        hash_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int MESSAGE_PAYLOAD_FIELD_NUMBER = 2;
    private com.google.protobuf.ByteString messagePayload_ = com.google.protobuf.ByteString.EMPTY;
    /**
     * <pre>
     * Protobuf encoded message payload provided by the service
     * </pre>
     *
     * <code>optional bytes message_payload = 2;</code>
     * @return Whether the messagePayload field is set.
     */
    @java.lang.Override
    public boolean hasMessagePayload() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <pre>
     * Protobuf encoded message payload provided by the service
     * </pre>
     *
     * <code>optional bytes message_payload = 2;</code>
     * @return The messagePayload.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString getMessagePayload() {
      return messagePayload_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        com.google.protobuf.GeneratedMessage.writeString(output, 1, hash_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        output.writeBytes(2, messagePayload_);
      }
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.GeneratedMessage.computeStringSize(1, hash_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(2, messagePayload_);
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof gateway_service.Envelope.EventMessage)) {
        return super.equals(obj);
      }
      gateway_service.Envelope.EventMessage other = (gateway_service.Envelope.EventMessage) obj;

      if (hasHash() != other.hasHash()) return false;
      if (hasHash()) {
        if (!getHash()
            .equals(other.getHash())) return false;
      }
      if (hasMessagePayload() != other.hasMessagePayload()) return false;
      if (hasMessagePayload()) {
        if (!getMessagePayload()
            .equals(other.getMessagePayload())) return false;
      }
      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasHash()) {
        hash = (37 * hash) + HASH_FIELD_NUMBER;
        hash = (53 * hash) + getHash().hashCode();
      }
      if (hasMessagePayload()) {
        hash = (37 * hash) + MESSAGE_PAYLOAD_FIELD_NUMBER;
        hash = (53 * hash) + getMessagePayload().hashCode();
      }
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static gateway_service.Envelope.EventMessage parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static gateway_service.Envelope.EventMessage parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static gateway_service.Envelope.EventMessage parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static gateway_service.Envelope.EventMessage parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static gateway_service.Envelope.EventMessage parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static gateway_service.Envelope.EventMessage parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static gateway_service.Envelope.EventMessage parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input);
    }
    public static gateway_service.Envelope.EventMessage parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public static gateway_service.Envelope.EventMessage parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseDelimitedWithIOException(PARSER, input);
    }

    public static gateway_service.Envelope.EventMessage parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static gateway_service.Envelope.EventMessage parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input);
    }
    public static gateway_service.Envelope.EventMessage parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(gateway_service.Envelope.EventMessage prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     * Data received from any event that will be published to VCDP
     * </pre>
     *
     * Protobuf type {@code gateway_service.EventMessage}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:gateway_service.EventMessage)
        gateway_service.Envelope.EventMessageOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return gateway_service.Envelope.internal_static_gateway_service_EventMessage_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return gateway_service.Envelope.internal_static_gateway_service_EventMessage_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                gateway_service.Envelope.EventMessage.class, gateway_service.Envelope.EventMessage.Builder.class);
      }

      // Construct using gateway_service.Envelope.EventMessage.newBuilder()
      private Builder() {

      }

      private Builder(
          com.google.protobuf.GeneratedMessage.BuilderParent parent) {
        super(parent);

      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        bitField0_ = 0;
        hash_ = "";
        messagePayload_ = com.google.protobuf.ByteString.EMPTY;
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return gateway_service.Envelope.internal_static_gateway_service_EventMessage_descriptor;
      }

      @java.lang.Override
      public gateway_service.Envelope.EventMessage getDefaultInstanceForType() {
        return gateway_service.Envelope.EventMessage.getDefaultInstance();
      }

      @java.lang.Override
      public gateway_service.Envelope.EventMessage build() {
        gateway_service.Envelope.EventMessage result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public gateway_service.Envelope.EventMessage buildPartial() {
        gateway_service.Envelope.EventMessage result = new gateway_service.Envelope.EventMessage(this);
        if (bitField0_ != 0) { buildPartial0(result); }
        onBuilt();
        return result;
      }

      private void buildPartial0(gateway_service.Envelope.EventMessage result) {
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.hash_ = hash_;
          to_bitField0_ |= 0x00000001;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          result.messagePayload_ = messagePayload_;
          to_bitField0_ |= 0x00000002;
        }
        result.bitField0_ |= to_bitField0_;
      }

      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof gateway_service.Envelope.EventMessage) {
          return mergeFrom((gateway_service.Envelope.EventMessage)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(gateway_service.Envelope.EventMessage other) {
        if (other == gateway_service.Envelope.EventMessage.getDefaultInstance()) return this;
        if (other.hasHash()) {
          hash_ = other.hash_;
          bitField0_ |= 0x00000001;
          onChanged();
        }
        if (other.hasMessagePayload()) {
          setMessagePayload(other.getMessagePayload());
        }
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 10: {
                hash_ = input.readStringRequireUtf8();
                bitField0_ |= 0x00000001;
                break;
              } // case 10
              case 18: {
                messagePayload_ = input.readBytes();
                bitField0_ |= 0x00000002;
                break;
              } // case 18
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }
      private int bitField0_;

      private java.lang.Object hash_ = "";
      /**
       * <pre>
       * The hash identifier used to determine the event this message relates to.
       * </pre>
       *
       * <code>optional string hash = 1;</code>
       * @return Whether the hash field is set.
       */
      public boolean hasHash() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <pre>
       * The hash identifier used to determine the event this message relates to.
       * </pre>
       *
       * <code>optional string hash = 1;</code>
       * @return The hash.
       */
      public java.lang.String getHash() {
        java.lang.Object ref = hash_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          hash_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       * The hash identifier used to determine the event this message relates to.
       * </pre>
       *
       * <code>optional string hash = 1;</code>
       * @return The bytes for hash.
       */
      public com.google.protobuf.ByteString
          getHashBytes() {
        java.lang.Object ref = hash_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          hash_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       * The hash identifier used to determine the event this message relates to.
       * </pre>
       *
       * <code>optional string hash = 1;</code>
       * @param value The hash to set.
       * @return This builder for chaining.
       */
      public Builder setHash(
          java.lang.String value) {
        if (value == null) { throw new NullPointerException(); }
        hash_ = value;
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * The hash identifier used to determine the event this message relates to.
       * </pre>
       *
       * <code>optional string hash = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearHash() {
        hash_ = getDefaultInstance().getHash();
        bitField0_ = (bitField0_ & ~0x00000001);
        onChanged();
        return this;
      }
      /**
       * <pre>
       * The hash identifier used to determine the event this message relates to.
       * </pre>
       *
       * <code>optional string hash = 1;</code>
       * @param value The bytes for hash to set.
       * @return This builder for chaining.
       */
      public Builder setHashBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        checkByteStringIsUtf8(value);
        hash_ = value;
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString messagePayload_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <pre>
       * Protobuf encoded message payload provided by the service
       * </pre>
       *
       * <code>optional bytes message_payload = 2;</code>
       * @return Whether the messagePayload field is set.
       */
      @java.lang.Override
      public boolean hasMessagePayload() {
        return ((bitField0_ & 0x00000002) != 0);
      }
      /**
       * <pre>
       * Protobuf encoded message payload provided by the service
       * </pre>
       *
       * <code>optional bytes message_payload = 2;</code>
       * @return The messagePayload.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString getMessagePayload() {
        return messagePayload_;
      }
      /**
       * <pre>
       * Protobuf encoded message payload provided by the service
       * </pre>
       *
       * <code>optional bytes message_payload = 2;</code>
       * @param value The messagePayload to set.
       * @return This builder for chaining.
       */
      public Builder setMessagePayload(com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        messagePayload_ = value;
        bitField0_ |= 0x00000002;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * Protobuf encoded message payload provided by the service
       * </pre>
       *
       * <code>optional bytes message_payload = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearMessagePayload() {
        bitField0_ = (bitField0_ & ~0x00000002);
        messagePayload_ = getDefaultInstance().getMessagePayload();
        onChanged();
        return this;
      }

      // @@protoc_insertion_point(builder_scope:gateway_service.EventMessage)
    }

    // @@protoc_insertion_point(class_scope:gateway_service.EventMessage)
    private static final gateway_service.Envelope.EventMessage DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new gateway_service.Envelope.EventMessage();
    }

    public static gateway_service.Envelope.EventMessage getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<EventMessage>
        PARSER = new com.google.protobuf.AbstractParser<EventMessage>() {
      @java.lang.Override
      public EventMessage parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<EventMessage> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<EventMessage> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public gateway_service.Envelope.EventMessage getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface RFCRequestMessageOrBuilder extends
      // @@protoc_insertion_point(interface_extends:gateway_service.RFCRequestMessage)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     * An opaque identifier to identify a unique request, returned in corresponding response
     * </pre>
     *
     * <code>optional uint32 request_context = 1;</code>
     * @return Whether the requestContext field is set.
     */
    boolean hasRequestContext();
    /**
     * <pre>
     * An opaque identifier to identify a unique request, returned in corresponding response
     * </pre>
     *
     * <code>optional uint32 request_context = 1;</code>
     * @return The requestContext.
     */
    int getRequestContext();

    /**
     * <pre>
     * The network demand action to be taken by RFC or Service Gateway
     * </pre>
     *
     * <code>.gateway_service.EnumNetworkDemandAction network_action = 2;</code>
     * @return The enum numeric value on the wire for networkAction.
     */
    int getNetworkActionValue();
    /**
     * <pre>
     * The network demand action to be taken by RFC or Service Gateway
     * </pre>
     *
     * <code>.gateway_service.EnumNetworkDemandAction network_action = 2;</code>
     * @return The networkAction.
     */
    gateway_service.Envelope.EnumNetworkDemandAction getNetworkAction();

    /**
     * <pre>
     * Network demand required by RFC to make the relevant CVLC request
     * </pre>
     *
     * <code>optional uint32 network_demand = 3;</code>
     * @return Whether the networkDemand field is set.
     */
    boolean hasNetworkDemand();
    /**
     * <pre>
     * Network demand required by RFC to make the relevant CVLC request
     * </pre>
     *
     * <code>optional uint32 network_demand = 3;</code>
     * @return The networkDemand.
     */
    int getNetworkDemand();

    /**
     * <pre>
     * The time for RFC to keep the network demand alive
     * </pre>
     *
     * <code>optional uint32 network_demand_time_ms = 4;</code>
     * @return Whether the networkDemandTimeMs field is set.
     */
    boolean hasNetworkDemandTimeMs();
    /**
     * <pre>
     * The time for RFC to keep the network demand alive
     * </pre>
     *
     * <code>optional uint32 network_demand_time_ms = 4;</code>
     * @return The networkDemandTimeMs.
     */
    int getNetworkDemandTimeMs();

    /**
     * <code>.gateway_service.SubscribeMessageRequest subscribe_request = 5;</code>
     * @return Whether the subscribeRequest field is set.
     */
    boolean hasSubscribeRequest();
    /**
     * <code>.gateway_service.SubscribeMessageRequest subscribe_request = 5;</code>
     * @return The subscribeRequest.
     */
    gateway_service.Envelope.SubscribeMessageRequest getSubscribeRequest();
    /**
     * <code>.gateway_service.SubscribeMessageRequest subscribe_request = 5;</code>
     */
    gateway_service.Envelope.SubscribeMessageRequestOrBuilder getSubscribeRequestOrBuilder();

    /**
     * <code>.gateway_service.RequestMessage request_message = 6;</code>
     * @return Whether the requestMessage field is set.
     */
    boolean hasRequestMessage();
    /**
     * <code>.gateway_service.RequestMessage request_message = 6;</code>
     * @return The requestMessage.
     */
    gateway_service.Envelope.RequestMessage getRequestMessage();
    /**
     * <code>.gateway_service.RequestMessage request_message = 6;</code>
     */
    gateway_service.Envelope.RequestMessageOrBuilder getRequestMessageOrBuilder();

    gateway_service.Envelope.RFCRequestMessage.MessageCase getMessageCase();
  }
  /**
   * <pre>
   * RFC messages are used to change the network and service availablity using CVLC
   * and then provide another GatewayRequestMessage that is used to implement the request
   * Note, normally there will be no response from an RFCRequestMessage because the response will be generated from
   * either the SubscribeMessageRequest or RequestMessage
   * </pre>
   *
   * Protobuf type {@code gateway_service.RFCRequestMessage}
   */
  public static final class RFCRequestMessage extends
      com.google.protobuf.GeneratedMessage implements
      // @@protoc_insertion_point(message_implements:gateway_service.RFCRequestMessage)
      RFCRequestMessageOrBuilder {
  private static final long serialVersionUID = 0L;
    static {
      com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
        com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
        /* major= */ 4,
        /* minor= */ 28,
        /* patch= */ 2,
        /* suffix= */ "",
        RFCRequestMessage.class.getName());
    }
    // Use RFCRequestMessage.newBuilder() to construct.
    private RFCRequestMessage(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
    }
    private RFCRequestMessage() {
      networkAction_ = 0;
    }

    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return gateway_service.Envelope.internal_static_gateway_service_RFCRequestMessage_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return gateway_service.Envelope.internal_static_gateway_service_RFCRequestMessage_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              gateway_service.Envelope.RFCRequestMessage.class, gateway_service.Envelope.RFCRequestMessage.Builder.class);
    }

    private int bitField0_;
    private int messageCase_ = 0;
    @SuppressWarnings("serial")
    private java.lang.Object message_;
    public enum MessageCase
        implements com.google.protobuf.Internal.EnumLite,
            com.google.protobuf.AbstractMessage.InternalOneOfEnum {
      SUBSCRIBE_REQUEST(5),
      REQUEST_MESSAGE(6),
      MESSAGE_NOT_SET(0);
      private final int value;
      private MessageCase(int value) {
        this.value = value;
      }
      /**
       * @param value The number of the enum to look for.
       * @return The enum associated with the given number.
       * @deprecated Use {@link #forNumber(int)} instead.
       */
      @java.lang.Deprecated
      public static MessageCase valueOf(int value) {
        return forNumber(value);
      }

      public static MessageCase forNumber(int value) {
        switch (value) {
          case 5: return SUBSCRIBE_REQUEST;
          case 6: return REQUEST_MESSAGE;
          case 0: return MESSAGE_NOT_SET;
          default: return null;
        }
      }
      public int getNumber() {
        return this.value;
      }
    };

    public MessageCase
    getMessageCase() {
      return MessageCase.forNumber(
          messageCase_);
    }

    public static final int REQUEST_CONTEXT_FIELD_NUMBER = 1;
    private int requestContext_ = 0;
    /**
     * <pre>
     * An opaque identifier to identify a unique request, returned in corresponding response
     * </pre>
     *
     * <code>optional uint32 request_context = 1;</code>
     * @return Whether the requestContext field is set.
     */
    @java.lang.Override
    public boolean hasRequestContext() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <pre>
     * An opaque identifier to identify a unique request, returned in corresponding response
     * </pre>
     *
     * <code>optional uint32 request_context = 1;</code>
     * @return The requestContext.
     */
    @java.lang.Override
    public int getRequestContext() {
      return requestContext_;
    }

    public static final int NETWORK_ACTION_FIELD_NUMBER = 2;
    private int networkAction_ = 0;
    /**
     * <pre>
     * The network demand action to be taken by RFC or Service Gateway
     * </pre>
     *
     * <code>.gateway_service.EnumNetworkDemandAction network_action = 2;</code>
     * @return The enum numeric value on the wire for networkAction.
     */
    @java.lang.Override public int getNetworkActionValue() {
      return networkAction_;
    }
    /**
     * <pre>
     * The network demand action to be taken by RFC or Service Gateway
     * </pre>
     *
     * <code>.gateway_service.EnumNetworkDemandAction network_action = 2;</code>
     * @return The networkAction.
     */
    @java.lang.Override public gateway_service.Envelope.EnumNetworkDemandAction getNetworkAction() {
      gateway_service.Envelope.EnumNetworkDemandAction result = gateway_service.Envelope.EnumNetworkDemandAction.forNumber(networkAction_);
      return result == null ? gateway_service.Envelope.EnumNetworkDemandAction.UNRECOGNIZED : result;
    }

    public static final int NETWORK_DEMAND_FIELD_NUMBER = 3;
    private int networkDemand_ = 0;
    /**
     * <pre>
     * Network demand required by RFC to make the relevant CVLC request
     * </pre>
     *
     * <code>optional uint32 network_demand = 3;</code>
     * @return Whether the networkDemand field is set.
     */
    @java.lang.Override
    public boolean hasNetworkDemand() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <pre>
     * Network demand required by RFC to make the relevant CVLC request
     * </pre>
     *
     * <code>optional uint32 network_demand = 3;</code>
     * @return The networkDemand.
     */
    @java.lang.Override
    public int getNetworkDemand() {
      return networkDemand_;
    }

    public static final int NETWORK_DEMAND_TIME_MS_FIELD_NUMBER = 4;
    private int networkDemandTimeMs_ = 0;
    /**
     * <pre>
     * The time for RFC to keep the network demand alive
     * </pre>
     *
     * <code>optional uint32 network_demand_time_ms = 4;</code>
     * @return Whether the networkDemandTimeMs field is set.
     */
    @java.lang.Override
    public boolean hasNetworkDemandTimeMs() {
      return ((bitField0_ & 0x00000004) != 0);
    }
    /**
     * <pre>
     * The time for RFC to keep the network demand alive
     * </pre>
     *
     * <code>optional uint32 network_demand_time_ms = 4;</code>
     * @return The networkDemandTimeMs.
     */
    @java.lang.Override
    public int getNetworkDemandTimeMs() {
      return networkDemandTimeMs_;
    }

    public static final int SUBSCRIBE_REQUEST_FIELD_NUMBER = 5;
    /**
     * <code>.gateway_service.SubscribeMessageRequest subscribe_request = 5;</code>
     * @return Whether the subscribeRequest field is set.
     */
    @java.lang.Override
    public boolean hasSubscribeRequest() {
      return messageCase_ == 5;
    }
    /**
     * <code>.gateway_service.SubscribeMessageRequest subscribe_request = 5;</code>
     * @return The subscribeRequest.
     */
    @java.lang.Override
    public gateway_service.Envelope.SubscribeMessageRequest getSubscribeRequest() {
      if (messageCase_ == 5) {
         return (gateway_service.Envelope.SubscribeMessageRequest) message_;
      }
      return gateway_service.Envelope.SubscribeMessageRequest.getDefaultInstance();
    }
    /**
     * <code>.gateway_service.SubscribeMessageRequest subscribe_request = 5;</code>
     */
    @java.lang.Override
    public gateway_service.Envelope.SubscribeMessageRequestOrBuilder getSubscribeRequestOrBuilder() {
      if (messageCase_ == 5) {
         return (gateway_service.Envelope.SubscribeMessageRequest) message_;
      }
      return gateway_service.Envelope.SubscribeMessageRequest.getDefaultInstance();
    }

    public static final int REQUEST_MESSAGE_FIELD_NUMBER = 6;
    /**
     * <code>.gateway_service.RequestMessage request_message = 6;</code>
     * @return Whether the requestMessage field is set.
     */
    @java.lang.Override
    public boolean hasRequestMessage() {
      return messageCase_ == 6;
    }
    /**
     * <code>.gateway_service.RequestMessage request_message = 6;</code>
     * @return The requestMessage.
     */
    @java.lang.Override
    public gateway_service.Envelope.RequestMessage getRequestMessage() {
      if (messageCase_ == 6) {
         return (gateway_service.Envelope.RequestMessage) message_;
      }
      return gateway_service.Envelope.RequestMessage.getDefaultInstance();
    }
    /**
     * <code>.gateway_service.RequestMessage request_message = 6;</code>
     */
    @java.lang.Override
    public gateway_service.Envelope.RequestMessageOrBuilder getRequestMessageOrBuilder() {
      if (messageCase_ == 6) {
         return (gateway_service.Envelope.RequestMessage) message_;
      }
      return gateway_service.Envelope.RequestMessage.getDefaultInstance();
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeUInt32(1, requestContext_);
      }
      if (networkAction_ != gateway_service.Envelope.EnumNetworkDemandAction.ENUM_NETWORD_DEMAND_ACTION_UNSPECIFIED.getNumber()) {
        output.writeEnum(2, networkAction_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        output.writeUInt32(3, networkDemand_);
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        output.writeUInt32(4, networkDemandTimeMs_);
      }
      if (messageCase_ == 5) {
        output.writeMessage(5, (gateway_service.Envelope.SubscribeMessageRequest) message_);
      }
      if (messageCase_ == 6) {
        output.writeMessage(6, (gateway_service.Envelope.RequestMessage) message_);
      }
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(1, requestContext_);
      }
      if (networkAction_ != gateway_service.Envelope.EnumNetworkDemandAction.ENUM_NETWORD_DEMAND_ACTION_UNSPECIFIED.getNumber()) {
        size += com.google.protobuf.CodedOutputStream
          .computeEnumSize(2, networkAction_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(3, networkDemand_);
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(4, networkDemandTimeMs_);
      }
      if (messageCase_ == 5) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(5, (gateway_service.Envelope.SubscribeMessageRequest) message_);
      }
      if (messageCase_ == 6) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(6, (gateway_service.Envelope.RequestMessage) message_);
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof gateway_service.Envelope.RFCRequestMessage)) {
        return super.equals(obj);
      }
      gateway_service.Envelope.RFCRequestMessage other = (gateway_service.Envelope.RFCRequestMessage) obj;

      if (hasRequestContext() != other.hasRequestContext()) return false;
      if (hasRequestContext()) {
        if (getRequestContext()
            != other.getRequestContext()) return false;
      }
      if (networkAction_ != other.networkAction_) return false;
      if (hasNetworkDemand() != other.hasNetworkDemand()) return false;
      if (hasNetworkDemand()) {
        if (getNetworkDemand()
            != other.getNetworkDemand()) return false;
      }
      if (hasNetworkDemandTimeMs() != other.hasNetworkDemandTimeMs()) return false;
      if (hasNetworkDemandTimeMs()) {
        if (getNetworkDemandTimeMs()
            != other.getNetworkDemandTimeMs()) return false;
      }
      if (!getMessageCase().equals(other.getMessageCase())) return false;
      switch (messageCase_) {
        case 5:
          if (!getSubscribeRequest()
              .equals(other.getSubscribeRequest())) return false;
          break;
        case 6:
          if (!getRequestMessage()
              .equals(other.getRequestMessage())) return false;
          break;
        case 0:
        default:
      }
      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasRequestContext()) {
        hash = (37 * hash) + REQUEST_CONTEXT_FIELD_NUMBER;
        hash = (53 * hash) + getRequestContext();
      }
      hash = (37 * hash) + NETWORK_ACTION_FIELD_NUMBER;
      hash = (53 * hash) + networkAction_;
      if (hasNetworkDemand()) {
        hash = (37 * hash) + NETWORK_DEMAND_FIELD_NUMBER;
        hash = (53 * hash) + getNetworkDemand();
      }
      if (hasNetworkDemandTimeMs()) {
        hash = (37 * hash) + NETWORK_DEMAND_TIME_MS_FIELD_NUMBER;
        hash = (53 * hash) + getNetworkDemandTimeMs();
      }
      switch (messageCase_) {
        case 5:
          hash = (37 * hash) + SUBSCRIBE_REQUEST_FIELD_NUMBER;
          hash = (53 * hash) + getSubscribeRequest().hashCode();
          break;
        case 6:
          hash = (37 * hash) + REQUEST_MESSAGE_FIELD_NUMBER;
          hash = (53 * hash) + getRequestMessage().hashCode();
          break;
        case 0:
        default:
      }
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static gateway_service.Envelope.RFCRequestMessage parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static gateway_service.Envelope.RFCRequestMessage parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static gateway_service.Envelope.RFCRequestMessage parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static gateway_service.Envelope.RFCRequestMessage parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static gateway_service.Envelope.RFCRequestMessage parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static gateway_service.Envelope.RFCRequestMessage parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static gateway_service.Envelope.RFCRequestMessage parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input);
    }
    public static gateway_service.Envelope.RFCRequestMessage parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public static gateway_service.Envelope.RFCRequestMessage parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseDelimitedWithIOException(PARSER, input);
    }

    public static gateway_service.Envelope.RFCRequestMessage parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static gateway_service.Envelope.RFCRequestMessage parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input);
    }
    public static gateway_service.Envelope.RFCRequestMessage parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(gateway_service.Envelope.RFCRequestMessage prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     * RFC messages are used to change the network and service availablity using CVLC
     * and then provide another GatewayRequestMessage that is used to implement the request
     * Note, normally there will be no response from an RFCRequestMessage because the response will be generated from
     * either the SubscribeMessageRequest or RequestMessage
     * </pre>
     *
     * Protobuf type {@code gateway_service.RFCRequestMessage}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:gateway_service.RFCRequestMessage)
        gateway_service.Envelope.RFCRequestMessageOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return gateway_service.Envelope.internal_static_gateway_service_RFCRequestMessage_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return gateway_service.Envelope.internal_static_gateway_service_RFCRequestMessage_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                gateway_service.Envelope.RFCRequestMessage.class, gateway_service.Envelope.RFCRequestMessage.Builder.class);
      }

      // Construct using gateway_service.Envelope.RFCRequestMessage.newBuilder()
      private Builder() {

      }

      private Builder(
          com.google.protobuf.GeneratedMessage.BuilderParent parent) {
        super(parent);

      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        bitField0_ = 0;
        requestContext_ = 0;
        networkAction_ = 0;
        networkDemand_ = 0;
        networkDemandTimeMs_ = 0;
        if (subscribeRequestBuilder_ != null) {
          subscribeRequestBuilder_.clear();
        }
        if (requestMessageBuilder_ != null) {
          requestMessageBuilder_.clear();
        }
        messageCase_ = 0;
        message_ = null;
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return gateway_service.Envelope.internal_static_gateway_service_RFCRequestMessage_descriptor;
      }

      @java.lang.Override
      public gateway_service.Envelope.RFCRequestMessage getDefaultInstanceForType() {
        return gateway_service.Envelope.RFCRequestMessage.getDefaultInstance();
      }

      @java.lang.Override
      public gateway_service.Envelope.RFCRequestMessage build() {
        gateway_service.Envelope.RFCRequestMessage result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public gateway_service.Envelope.RFCRequestMessage buildPartial() {
        gateway_service.Envelope.RFCRequestMessage result = new gateway_service.Envelope.RFCRequestMessage(this);
        if (bitField0_ != 0) { buildPartial0(result); }
        buildPartialOneofs(result);
        onBuilt();
        return result;
      }

      private void buildPartial0(gateway_service.Envelope.RFCRequestMessage result) {
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.requestContext_ = requestContext_;
          to_bitField0_ |= 0x00000001;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          result.networkAction_ = networkAction_;
        }
        if (((from_bitField0_ & 0x00000004) != 0)) {
          result.networkDemand_ = networkDemand_;
          to_bitField0_ |= 0x00000002;
        }
        if (((from_bitField0_ & 0x00000008) != 0)) {
          result.networkDemandTimeMs_ = networkDemandTimeMs_;
          to_bitField0_ |= 0x00000004;
        }
        result.bitField0_ |= to_bitField0_;
      }

      private void buildPartialOneofs(gateway_service.Envelope.RFCRequestMessage result) {
        result.messageCase_ = messageCase_;
        result.message_ = this.message_;
        if (messageCase_ == 5 &&
            subscribeRequestBuilder_ != null) {
          result.message_ = subscribeRequestBuilder_.build();
        }
        if (messageCase_ == 6 &&
            requestMessageBuilder_ != null) {
          result.message_ = requestMessageBuilder_.build();
        }
      }

      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof gateway_service.Envelope.RFCRequestMessage) {
          return mergeFrom((gateway_service.Envelope.RFCRequestMessage)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(gateway_service.Envelope.RFCRequestMessage other) {
        if (other == gateway_service.Envelope.RFCRequestMessage.getDefaultInstance()) return this;
        if (other.hasRequestContext()) {
          setRequestContext(other.getRequestContext());
        }
        if (other.networkAction_ != 0) {
          setNetworkActionValue(other.getNetworkActionValue());
        }
        if (other.hasNetworkDemand()) {
          setNetworkDemand(other.getNetworkDemand());
        }
        if (other.hasNetworkDemandTimeMs()) {
          setNetworkDemandTimeMs(other.getNetworkDemandTimeMs());
        }
        switch (other.getMessageCase()) {
          case SUBSCRIBE_REQUEST: {
            mergeSubscribeRequest(other.getSubscribeRequest());
            break;
          }
          case REQUEST_MESSAGE: {
            mergeRequestMessage(other.getRequestMessage());
            break;
          }
          case MESSAGE_NOT_SET: {
            break;
          }
        }
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 8: {
                requestContext_ = input.readUInt32();
                bitField0_ |= 0x00000001;
                break;
              } // case 8
              case 16: {
                networkAction_ = input.readEnum();
                bitField0_ |= 0x00000002;
                break;
              } // case 16
              case 24: {
                networkDemand_ = input.readUInt32();
                bitField0_ |= 0x00000004;
                break;
              } // case 24
              case 32: {
                networkDemandTimeMs_ = input.readUInt32();
                bitField0_ |= 0x00000008;
                break;
              } // case 32
              case 42: {
                input.readMessage(
                    getSubscribeRequestFieldBuilder().getBuilder(),
                    extensionRegistry);
                messageCase_ = 5;
                break;
              } // case 42
              case 50: {
                input.readMessage(
                    getRequestMessageFieldBuilder().getBuilder(),
                    extensionRegistry);
                messageCase_ = 6;
                break;
              } // case 50
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }
      private int messageCase_ = 0;
      private java.lang.Object message_;
      public MessageCase
          getMessageCase() {
        return MessageCase.forNumber(
            messageCase_);
      }

      public Builder clearMessage() {
        messageCase_ = 0;
        message_ = null;
        onChanged();
        return this;
      }

      private int bitField0_;

      private int requestContext_ ;
      /**
       * <pre>
       * An opaque identifier to identify a unique request, returned in corresponding response
       * </pre>
       *
       * <code>optional uint32 request_context = 1;</code>
       * @return Whether the requestContext field is set.
       */
      @java.lang.Override
      public boolean hasRequestContext() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <pre>
       * An opaque identifier to identify a unique request, returned in corresponding response
       * </pre>
       *
       * <code>optional uint32 request_context = 1;</code>
       * @return The requestContext.
       */
      @java.lang.Override
      public int getRequestContext() {
        return requestContext_;
      }
      /**
       * <pre>
       * An opaque identifier to identify a unique request, returned in corresponding response
       * </pre>
       *
       * <code>optional uint32 request_context = 1;</code>
       * @param value The requestContext to set.
       * @return This builder for chaining.
       */
      public Builder setRequestContext(int value) {

        requestContext_ = value;
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * An opaque identifier to identify a unique request, returned in corresponding response
       * </pre>
       *
       * <code>optional uint32 request_context = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearRequestContext() {
        bitField0_ = (bitField0_ & ~0x00000001);
        requestContext_ = 0;
        onChanged();
        return this;
      }

      private int networkAction_ = 0;
      /**
       * <pre>
       * The network demand action to be taken by RFC or Service Gateway
       * </pre>
       *
       * <code>.gateway_service.EnumNetworkDemandAction network_action = 2;</code>
       * @return The enum numeric value on the wire for networkAction.
       */
      @java.lang.Override public int getNetworkActionValue() {
        return networkAction_;
      }
      /**
       * <pre>
       * The network demand action to be taken by RFC or Service Gateway
       * </pre>
       *
       * <code>.gateway_service.EnumNetworkDemandAction network_action = 2;</code>
       * @param value The enum numeric value on the wire for networkAction to set.
       * @return This builder for chaining.
       */
      public Builder setNetworkActionValue(int value) {
        networkAction_ = value;
        bitField0_ |= 0x00000002;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * The network demand action to be taken by RFC or Service Gateway
       * </pre>
       *
       * <code>.gateway_service.EnumNetworkDemandAction network_action = 2;</code>
       * @return The networkAction.
       */
      @java.lang.Override
      public gateway_service.Envelope.EnumNetworkDemandAction getNetworkAction() {
        gateway_service.Envelope.EnumNetworkDemandAction result = gateway_service.Envelope.EnumNetworkDemandAction.forNumber(networkAction_);
        return result == null ? gateway_service.Envelope.EnumNetworkDemandAction.UNRECOGNIZED : result;
      }
      /**
       * <pre>
       * The network demand action to be taken by RFC or Service Gateway
       * </pre>
       *
       * <code>.gateway_service.EnumNetworkDemandAction network_action = 2;</code>
       * @param value The networkAction to set.
       * @return This builder for chaining.
       */
      public Builder setNetworkAction(gateway_service.Envelope.EnumNetworkDemandAction value) {
        if (value == null) {
          throw new NullPointerException();
        }
        bitField0_ |= 0x00000002;
        networkAction_ = value.getNumber();
        onChanged();
        return this;
      }
      /**
       * <pre>
       * The network demand action to be taken by RFC or Service Gateway
       * </pre>
       *
       * <code>.gateway_service.EnumNetworkDemandAction network_action = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearNetworkAction() {
        bitField0_ = (bitField0_ & ~0x00000002);
        networkAction_ = 0;
        onChanged();
        return this;
      }

      private int networkDemand_ ;
      /**
       * <pre>
       * Network demand required by RFC to make the relevant CVLC request
       * </pre>
       *
       * <code>optional uint32 network_demand = 3;</code>
       * @return Whether the networkDemand field is set.
       */
      @java.lang.Override
      public boolean hasNetworkDemand() {
        return ((bitField0_ & 0x00000004) != 0);
      }
      /**
       * <pre>
       * Network demand required by RFC to make the relevant CVLC request
       * </pre>
       *
       * <code>optional uint32 network_demand = 3;</code>
       * @return The networkDemand.
       */
      @java.lang.Override
      public int getNetworkDemand() {
        return networkDemand_;
      }
      /**
       * <pre>
       * Network demand required by RFC to make the relevant CVLC request
       * </pre>
       *
       * <code>optional uint32 network_demand = 3;</code>
       * @param value The networkDemand to set.
       * @return This builder for chaining.
       */
      public Builder setNetworkDemand(int value) {

        networkDemand_ = value;
        bitField0_ |= 0x00000004;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * Network demand required by RFC to make the relevant CVLC request
       * </pre>
       *
       * <code>optional uint32 network_demand = 3;</code>
       * @return This builder for chaining.
       */
      public Builder clearNetworkDemand() {
        bitField0_ = (bitField0_ & ~0x00000004);
        networkDemand_ = 0;
        onChanged();
        return this;
      }

      private int networkDemandTimeMs_ ;
      /**
       * <pre>
       * The time for RFC to keep the network demand alive
       * </pre>
       *
       * <code>optional uint32 network_demand_time_ms = 4;</code>
       * @return Whether the networkDemandTimeMs field is set.
       */
      @java.lang.Override
      public boolean hasNetworkDemandTimeMs() {
        return ((bitField0_ & 0x00000008) != 0);
      }
      /**
       * <pre>
       * The time for RFC to keep the network demand alive
       * </pre>
       *
       * <code>optional uint32 network_demand_time_ms = 4;</code>
       * @return The networkDemandTimeMs.
       */
      @java.lang.Override
      public int getNetworkDemandTimeMs() {
        return networkDemandTimeMs_;
      }
      /**
       * <pre>
       * The time for RFC to keep the network demand alive
       * </pre>
       *
       * <code>optional uint32 network_demand_time_ms = 4;</code>
       * @param value The networkDemandTimeMs to set.
       * @return This builder for chaining.
       */
      public Builder setNetworkDemandTimeMs(int value) {

        networkDemandTimeMs_ = value;
        bitField0_ |= 0x00000008;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * The time for RFC to keep the network demand alive
       * </pre>
       *
       * <code>optional uint32 network_demand_time_ms = 4;</code>
       * @return This builder for chaining.
       */
      public Builder clearNetworkDemandTimeMs() {
        bitField0_ = (bitField0_ & ~0x00000008);
        networkDemandTimeMs_ = 0;
        onChanged();
        return this;
      }

      private com.google.protobuf.SingleFieldBuilder<
          gateway_service.Envelope.SubscribeMessageRequest, gateway_service.Envelope.SubscribeMessageRequest.Builder, gateway_service.Envelope.SubscribeMessageRequestOrBuilder> subscribeRequestBuilder_;
      /**
       * <code>.gateway_service.SubscribeMessageRequest subscribe_request = 5;</code>
       * @return Whether the subscribeRequest field is set.
       */
      @java.lang.Override
      public boolean hasSubscribeRequest() {
        return messageCase_ == 5;
      }
      /**
       * <code>.gateway_service.SubscribeMessageRequest subscribe_request = 5;</code>
       * @return The subscribeRequest.
       */
      @java.lang.Override
      public gateway_service.Envelope.SubscribeMessageRequest getSubscribeRequest() {
        if (subscribeRequestBuilder_ == null) {
          if (messageCase_ == 5) {
            return (gateway_service.Envelope.SubscribeMessageRequest) message_;
          }
          return gateway_service.Envelope.SubscribeMessageRequest.getDefaultInstance();
        } else {
          if (messageCase_ == 5) {
            return subscribeRequestBuilder_.getMessage();
          }
          return gateway_service.Envelope.SubscribeMessageRequest.getDefaultInstance();
        }
      }
      /**
       * <code>.gateway_service.SubscribeMessageRequest subscribe_request = 5;</code>
       */
      public Builder setSubscribeRequest(gateway_service.Envelope.SubscribeMessageRequest value) {
        if (subscribeRequestBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          message_ = value;
          onChanged();
        } else {
          subscribeRequestBuilder_.setMessage(value);
        }
        messageCase_ = 5;
        return this;
      }
      /**
       * <code>.gateway_service.SubscribeMessageRequest subscribe_request = 5;</code>
       */
      public Builder setSubscribeRequest(
          gateway_service.Envelope.SubscribeMessageRequest.Builder builderForValue) {
        if (subscribeRequestBuilder_ == null) {
          message_ = builderForValue.build();
          onChanged();
        } else {
          subscribeRequestBuilder_.setMessage(builderForValue.build());
        }
        messageCase_ = 5;
        return this;
      }
      /**
       * <code>.gateway_service.SubscribeMessageRequest subscribe_request = 5;</code>
       */
      public Builder mergeSubscribeRequest(gateway_service.Envelope.SubscribeMessageRequest value) {
        if (subscribeRequestBuilder_ == null) {
          if (messageCase_ == 5 &&
              message_ != gateway_service.Envelope.SubscribeMessageRequest.getDefaultInstance()) {
            message_ = gateway_service.Envelope.SubscribeMessageRequest.newBuilder((gateway_service.Envelope.SubscribeMessageRequest) message_)
                .mergeFrom(value).buildPartial();
          } else {
            message_ = value;
          }
          onChanged();
        } else {
          if (messageCase_ == 5) {
            subscribeRequestBuilder_.mergeFrom(value);
          } else {
            subscribeRequestBuilder_.setMessage(value);
          }
        }
        messageCase_ = 5;
        return this;
      }
      /**
       * <code>.gateway_service.SubscribeMessageRequest subscribe_request = 5;</code>
       */
      public Builder clearSubscribeRequest() {
        if (subscribeRequestBuilder_ == null) {
          if (messageCase_ == 5) {
            messageCase_ = 0;
            message_ = null;
            onChanged();
          }
        } else {
          if (messageCase_ == 5) {
            messageCase_ = 0;
            message_ = null;
          }
          subscribeRequestBuilder_.clear();
        }
        return this;
      }
      /**
       * <code>.gateway_service.SubscribeMessageRequest subscribe_request = 5;</code>
       */
      public gateway_service.Envelope.SubscribeMessageRequest.Builder getSubscribeRequestBuilder() {
        return getSubscribeRequestFieldBuilder().getBuilder();
      }
      /**
       * <code>.gateway_service.SubscribeMessageRequest subscribe_request = 5;</code>
       */
      @java.lang.Override
      public gateway_service.Envelope.SubscribeMessageRequestOrBuilder getSubscribeRequestOrBuilder() {
        if ((messageCase_ == 5) && (subscribeRequestBuilder_ != null)) {
          return subscribeRequestBuilder_.getMessageOrBuilder();
        } else {
          if (messageCase_ == 5) {
            return (gateway_service.Envelope.SubscribeMessageRequest) message_;
          }
          return gateway_service.Envelope.SubscribeMessageRequest.getDefaultInstance();
        }
      }
      /**
       * <code>.gateway_service.SubscribeMessageRequest subscribe_request = 5;</code>
       */
      private com.google.protobuf.SingleFieldBuilder<
          gateway_service.Envelope.SubscribeMessageRequest, gateway_service.Envelope.SubscribeMessageRequest.Builder, gateway_service.Envelope.SubscribeMessageRequestOrBuilder> 
          getSubscribeRequestFieldBuilder() {
        if (subscribeRequestBuilder_ == null) {
          if (!(messageCase_ == 5)) {
            message_ = gateway_service.Envelope.SubscribeMessageRequest.getDefaultInstance();
          }
          subscribeRequestBuilder_ = new com.google.protobuf.SingleFieldBuilder<
              gateway_service.Envelope.SubscribeMessageRequest, gateway_service.Envelope.SubscribeMessageRequest.Builder, gateway_service.Envelope.SubscribeMessageRequestOrBuilder>(
                  (gateway_service.Envelope.SubscribeMessageRequest) message_,
                  getParentForChildren(),
                  isClean());
          message_ = null;
        }
        messageCase_ = 5;
        onChanged();
        return subscribeRequestBuilder_;
      }

      private com.google.protobuf.SingleFieldBuilder<
          gateway_service.Envelope.RequestMessage, gateway_service.Envelope.RequestMessage.Builder, gateway_service.Envelope.RequestMessageOrBuilder> requestMessageBuilder_;
      /**
       * <code>.gateway_service.RequestMessage request_message = 6;</code>
       * @return Whether the requestMessage field is set.
       */
      @java.lang.Override
      public boolean hasRequestMessage() {
        return messageCase_ == 6;
      }
      /**
       * <code>.gateway_service.RequestMessage request_message = 6;</code>
       * @return The requestMessage.
       */
      @java.lang.Override
      public gateway_service.Envelope.RequestMessage getRequestMessage() {
        if (requestMessageBuilder_ == null) {
          if (messageCase_ == 6) {
            return (gateway_service.Envelope.RequestMessage) message_;
          }
          return gateway_service.Envelope.RequestMessage.getDefaultInstance();
        } else {
          if (messageCase_ == 6) {
            return requestMessageBuilder_.getMessage();
          }
          return gateway_service.Envelope.RequestMessage.getDefaultInstance();
        }
      }
      /**
       * <code>.gateway_service.RequestMessage request_message = 6;</code>
       */
      public Builder setRequestMessage(gateway_service.Envelope.RequestMessage value) {
        if (requestMessageBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          message_ = value;
          onChanged();
        } else {
          requestMessageBuilder_.setMessage(value);
        }
        messageCase_ = 6;
        return this;
      }
      /**
       * <code>.gateway_service.RequestMessage request_message = 6;</code>
       */
      public Builder setRequestMessage(
          gateway_service.Envelope.RequestMessage.Builder builderForValue) {
        if (requestMessageBuilder_ == null) {
          message_ = builderForValue.build();
          onChanged();
        } else {
          requestMessageBuilder_.setMessage(builderForValue.build());
        }
        messageCase_ = 6;
        return this;
      }
      /**
       * <code>.gateway_service.RequestMessage request_message = 6;</code>
       */
      public Builder mergeRequestMessage(gateway_service.Envelope.RequestMessage value) {
        if (requestMessageBuilder_ == null) {
          if (messageCase_ == 6 &&
              message_ != gateway_service.Envelope.RequestMessage.getDefaultInstance()) {
            message_ = gateway_service.Envelope.RequestMessage.newBuilder((gateway_service.Envelope.RequestMessage) message_)
                .mergeFrom(value).buildPartial();
          } else {
            message_ = value;
          }
          onChanged();
        } else {
          if (messageCase_ == 6) {
            requestMessageBuilder_.mergeFrom(value);
          } else {
            requestMessageBuilder_.setMessage(value);
          }
        }
        messageCase_ = 6;
        return this;
      }
      /**
       * <code>.gateway_service.RequestMessage request_message = 6;</code>
       */
      public Builder clearRequestMessage() {
        if (requestMessageBuilder_ == null) {
          if (messageCase_ == 6) {
            messageCase_ = 0;
            message_ = null;
            onChanged();
          }
        } else {
          if (messageCase_ == 6) {
            messageCase_ = 0;
            message_ = null;
          }
          requestMessageBuilder_.clear();
        }
        return this;
      }
      /**
       * <code>.gateway_service.RequestMessage request_message = 6;</code>
       */
      public gateway_service.Envelope.RequestMessage.Builder getRequestMessageBuilder() {
        return getRequestMessageFieldBuilder().getBuilder();
      }
      /**
       * <code>.gateway_service.RequestMessage request_message = 6;</code>
       */
      @java.lang.Override
      public gateway_service.Envelope.RequestMessageOrBuilder getRequestMessageOrBuilder() {
        if ((messageCase_ == 6) && (requestMessageBuilder_ != null)) {
          return requestMessageBuilder_.getMessageOrBuilder();
        } else {
          if (messageCase_ == 6) {
            return (gateway_service.Envelope.RequestMessage) message_;
          }
          return gateway_service.Envelope.RequestMessage.getDefaultInstance();
        }
      }
      /**
       * <code>.gateway_service.RequestMessage request_message = 6;</code>
       */
      private com.google.protobuf.SingleFieldBuilder<
          gateway_service.Envelope.RequestMessage, gateway_service.Envelope.RequestMessage.Builder, gateway_service.Envelope.RequestMessageOrBuilder> 
          getRequestMessageFieldBuilder() {
        if (requestMessageBuilder_ == null) {
          if (!(messageCase_ == 6)) {
            message_ = gateway_service.Envelope.RequestMessage.getDefaultInstance();
          }
          requestMessageBuilder_ = new com.google.protobuf.SingleFieldBuilder<
              gateway_service.Envelope.RequestMessage, gateway_service.Envelope.RequestMessage.Builder, gateway_service.Envelope.RequestMessageOrBuilder>(
                  (gateway_service.Envelope.RequestMessage) message_,
                  getParentForChildren(),
                  isClean());
          message_ = null;
        }
        messageCase_ = 6;
        onChanged();
        return requestMessageBuilder_;
      }

      // @@protoc_insertion_point(builder_scope:gateway_service.RFCRequestMessage)
    }

    // @@protoc_insertion_point(class_scope:gateway_service.RFCRequestMessage)
    private static final gateway_service.Envelope.RFCRequestMessage DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new gateway_service.Envelope.RFCRequestMessage();
    }

    public static gateway_service.Envelope.RFCRequestMessage getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<RFCRequestMessage>
        PARSER = new com.google.protobuf.AbstractParser<RFCRequestMessage>() {
      @java.lang.Override
      public RFCRequestMessage parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<RFCRequestMessage> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<RFCRequestMessage> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public gateway_service.Envelope.RFCRequestMessage getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface RFCResponseMessageOrBuilder extends
      // @@protoc_insertion_point(interface_extends:gateway_service.RFCResponseMessage)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     * The status result of the request
     * </pre>
     *
     * <code>.gateway_service.EnumOperationStatus result = 1;</code>
     * @return The enum numeric value on the wire for result.
     */
    int getResultValue();
    /**
     * <pre>
     * The status result of the request
     * </pre>
     *
     * <code>.gateway_service.EnumOperationStatus result = 1;</code>
     * @return The result.
     */
    gateway_service.Envelope.EnumOperationStatus getResult();

    /**
     * <pre>
     * An opaque identifier to identify the RFC request message this response relates to.
     * </pre>
     *
     * <code>optional uint32 request_context = 2;</code>
     * @return Whether the requestContext field is set.
     */
    boolean hasRequestContext();
    /**
     * <pre>
     * An opaque identifier to identify the RFC request message this response relates to.
     * </pre>
     *
     * <code>optional uint32 request_context = 2;</code>
     * @return The requestContext.
     */
    int getRequestContext();
  }
  /**
   * <pre>
   * The response to a failed RFCRequestMessage
   * Note, this will only be sent if the RFC request failed because the service gateway request
   * message being dropped and therefore have no response
   * </pre>
   *
   * Protobuf type {@code gateway_service.RFCResponseMessage}
   */
  public static final class RFCResponseMessage extends
      com.google.protobuf.GeneratedMessage implements
      // @@protoc_insertion_point(message_implements:gateway_service.RFCResponseMessage)
      RFCResponseMessageOrBuilder {
  private static final long serialVersionUID = 0L;
    static {
      com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
        com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
        /* major= */ 4,
        /* minor= */ 28,
        /* patch= */ 2,
        /* suffix= */ "",
        RFCResponseMessage.class.getName());
    }
    // Use RFCResponseMessage.newBuilder() to construct.
    private RFCResponseMessage(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
    }
    private RFCResponseMessage() {
      result_ = 0;
    }

    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return gateway_service.Envelope.internal_static_gateway_service_RFCResponseMessage_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return gateway_service.Envelope.internal_static_gateway_service_RFCResponseMessage_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              gateway_service.Envelope.RFCResponseMessage.class, gateway_service.Envelope.RFCResponseMessage.Builder.class);
    }

    private int bitField0_;
    public static final int RESULT_FIELD_NUMBER = 1;
    private int result_ = 0;
    /**
     * <pre>
     * The status result of the request
     * </pre>
     *
     * <code>.gateway_service.EnumOperationStatus result = 1;</code>
     * @return The enum numeric value on the wire for result.
     */
    @java.lang.Override public int getResultValue() {
      return result_;
    }
    /**
     * <pre>
     * The status result of the request
     * </pre>
     *
     * <code>.gateway_service.EnumOperationStatus result = 1;</code>
     * @return The result.
     */
    @java.lang.Override public gateway_service.Envelope.EnumOperationStatus getResult() {
      gateway_service.Envelope.EnumOperationStatus result = gateway_service.Envelope.EnumOperationStatus.forNumber(result_);
      return result == null ? gateway_service.Envelope.EnumOperationStatus.UNRECOGNIZED : result;
    }

    public static final int REQUEST_CONTEXT_FIELD_NUMBER = 2;
    private int requestContext_ = 0;
    /**
     * <pre>
     * An opaque identifier to identify the RFC request message this response relates to.
     * </pre>
     *
     * <code>optional uint32 request_context = 2;</code>
     * @return Whether the requestContext field is set.
     */
    @java.lang.Override
    public boolean hasRequestContext() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <pre>
     * An opaque identifier to identify the RFC request message this response relates to.
     * </pre>
     *
     * <code>optional uint32 request_context = 2;</code>
     * @return The requestContext.
     */
    @java.lang.Override
    public int getRequestContext() {
      return requestContext_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (result_ != gateway_service.Envelope.EnumOperationStatus.ENUM_OPERATION_STATUS_UNSPECIFIED.getNumber()) {
        output.writeEnum(1, result_);
      }
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeUInt32(2, requestContext_);
      }
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (result_ != gateway_service.Envelope.EnumOperationStatus.ENUM_OPERATION_STATUS_UNSPECIFIED.getNumber()) {
        size += com.google.protobuf.CodedOutputStream
          .computeEnumSize(1, result_);
      }
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(2, requestContext_);
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof gateway_service.Envelope.RFCResponseMessage)) {
        return super.equals(obj);
      }
      gateway_service.Envelope.RFCResponseMessage other = (gateway_service.Envelope.RFCResponseMessage) obj;

      if (result_ != other.result_) return false;
      if (hasRequestContext() != other.hasRequestContext()) return false;
      if (hasRequestContext()) {
        if (getRequestContext()
            != other.getRequestContext()) return false;
      }
      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + RESULT_FIELD_NUMBER;
      hash = (53 * hash) + result_;
      if (hasRequestContext()) {
        hash = (37 * hash) + REQUEST_CONTEXT_FIELD_NUMBER;
        hash = (53 * hash) + getRequestContext();
      }
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static gateway_service.Envelope.RFCResponseMessage parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static gateway_service.Envelope.RFCResponseMessage parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static gateway_service.Envelope.RFCResponseMessage parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static gateway_service.Envelope.RFCResponseMessage parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static gateway_service.Envelope.RFCResponseMessage parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static gateway_service.Envelope.RFCResponseMessage parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static gateway_service.Envelope.RFCResponseMessage parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input);
    }
    public static gateway_service.Envelope.RFCResponseMessage parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public static gateway_service.Envelope.RFCResponseMessage parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseDelimitedWithIOException(PARSER, input);
    }

    public static gateway_service.Envelope.RFCResponseMessage parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static gateway_service.Envelope.RFCResponseMessage parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input);
    }
    public static gateway_service.Envelope.RFCResponseMessage parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(gateway_service.Envelope.RFCResponseMessage prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     * The response to a failed RFCRequestMessage
     * Note, this will only be sent if the RFC request failed because the service gateway request
     * message being dropped and therefore have no response
     * </pre>
     *
     * Protobuf type {@code gateway_service.RFCResponseMessage}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:gateway_service.RFCResponseMessage)
        gateway_service.Envelope.RFCResponseMessageOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return gateway_service.Envelope.internal_static_gateway_service_RFCResponseMessage_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return gateway_service.Envelope.internal_static_gateway_service_RFCResponseMessage_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                gateway_service.Envelope.RFCResponseMessage.class, gateway_service.Envelope.RFCResponseMessage.Builder.class);
      }

      // Construct using gateway_service.Envelope.RFCResponseMessage.newBuilder()
      private Builder() {

      }

      private Builder(
          com.google.protobuf.GeneratedMessage.BuilderParent parent) {
        super(parent);

      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        bitField0_ = 0;
        result_ = 0;
        requestContext_ = 0;
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return gateway_service.Envelope.internal_static_gateway_service_RFCResponseMessage_descriptor;
      }

      @java.lang.Override
      public gateway_service.Envelope.RFCResponseMessage getDefaultInstanceForType() {
        return gateway_service.Envelope.RFCResponseMessage.getDefaultInstance();
      }

      @java.lang.Override
      public gateway_service.Envelope.RFCResponseMessage build() {
        gateway_service.Envelope.RFCResponseMessage result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public gateway_service.Envelope.RFCResponseMessage buildPartial() {
        gateway_service.Envelope.RFCResponseMessage result = new gateway_service.Envelope.RFCResponseMessage(this);
        if (bitField0_ != 0) { buildPartial0(result); }
        onBuilt();
        return result;
      }

      private void buildPartial0(gateway_service.Envelope.RFCResponseMessage result) {
        int from_bitField0_ = bitField0_;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.result_ = result_;
        }
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000002) != 0)) {
          result.requestContext_ = requestContext_;
          to_bitField0_ |= 0x00000001;
        }
        result.bitField0_ |= to_bitField0_;
      }

      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof gateway_service.Envelope.RFCResponseMessage) {
          return mergeFrom((gateway_service.Envelope.RFCResponseMessage)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(gateway_service.Envelope.RFCResponseMessage other) {
        if (other == gateway_service.Envelope.RFCResponseMessage.getDefaultInstance()) return this;
        if (other.result_ != 0) {
          setResultValue(other.getResultValue());
        }
        if (other.hasRequestContext()) {
          setRequestContext(other.getRequestContext());
        }
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 8: {
                result_ = input.readEnum();
                bitField0_ |= 0x00000001;
                break;
              } // case 8
              case 16: {
                requestContext_ = input.readUInt32();
                bitField0_ |= 0x00000002;
                break;
              } // case 16
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }
      private int bitField0_;

      private int result_ = 0;
      /**
       * <pre>
       * The status result of the request
       * </pre>
       *
       * <code>.gateway_service.EnumOperationStatus result = 1;</code>
       * @return The enum numeric value on the wire for result.
       */
      @java.lang.Override public int getResultValue() {
        return result_;
      }
      /**
       * <pre>
       * The status result of the request
       * </pre>
       *
       * <code>.gateway_service.EnumOperationStatus result = 1;</code>
       * @param value The enum numeric value on the wire for result to set.
       * @return This builder for chaining.
       */
      public Builder setResultValue(int value) {
        result_ = value;
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * The status result of the request
       * </pre>
       *
       * <code>.gateway_service.EnumOperationStatus result = 1;</code>
       * @return The result.
       */
      @java.lang.Override
      public gateway_service.Envelope.EnumOperationStatus getResult() {
        gateway_service.Envelope.EnumOperationStatus result = gateway_service.Envelope.EnumOperationStatus.forNumber(result_);
        return result == null ? gateway_service.Envelope.EnumOperationStatus.UNRECOGNIZED : result;
      }
      /**
       * <pre>
       * The status result of the request
       * </pre>
       *
       * <code>.gateway_service.EnumOperationStatus result = 1;</code>
       * @param value The result to set.
       * @return This builder for chaining.
       */
      public Builder setResult(gateway_service.Envelope.EnumOperationStatus value) {
        if (value == null) {
          throw new NullPointerException();
        }
        bitField0_ |= 0x00000001;
        result_ = value.getNumber();
        onChanged();
        return this;
      }
      /**
       * <pre>
       * The status result of the request
       * </pre>
       *
       * <code>.gateway_service.EnumOperationStatus result = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearResult() {
        bitField0_ = (bitField0_ & ~0x00000001);
        result_ = 0;
        onChanged();
        return this;
      }

      private int requestContext_ ;
      /**
       * <pre>
       * An opaque identifier to identify the RFC request message this response relates to.
       * </pre>
       *
       * <code>optional uint32 request_context = 2;</code>
       * @return Whether the requestContext field is set.
       */
      @java.lang.Override
      public boolean hasRequestContext() {
        return ((bitField0_ & 0x00000002) != 0);
      }
      /**
       * <pre>
       * An opaque identifier to identify the RFC request message this response relates to.
       * </pre>
       *
       * <code>optional uint32 request_context = 2;</code>
       * @return The requestContext.
       */
      @java.lang.Override
      public int getRequestContext() {
        return requestContext_;
      }
      /**
       * <pre>
       * An opaque identifier to identify the RFC request message this response relates to.
       * </pre>
       *
       * <code>optional uint32 request_context = 2;</code>
       * @param value The requestContext to set.
       * @return This builder for chaining.
       */
      public Builder setRequestContext(int value) {

        requestContext_ = value;
        bitField0_ |= 0x00000002;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * An opaque identifier to identify the RFC request message this response relates to.
       * </pre>
       *
       * <code>optional uint32 request_context = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearRequestContext() {
        bitField0_ = (bitField0_ & ~0x00000002);
        requestContext_ = 0;
        onChanged();
        return this;
      }

      // @@protoc_insertion_point(builder_scope:gateway_service.RFCResponseMessage)
    }

    // @@protoc_insertion_point(class_scope:gateway_service.RFCResponseMessage)
    private static final gateway_service.Envelope.RFCResponseMessage DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new gateway_service.Envelope.RFCResponseMessage();
    }

    public static gateway_service.Envelope.RFCResponseMessage getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<RFCResponseMessage>
        PARSER = new com.google.protobuf.AbstractParser<RFCResponseMessage>() {
      @java.lang.Override
      public RFCResponseMessage parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<RFCResponseMessage> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<RFCResponseMessage> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public gateway_service.Envelope.RFCResponseMessage getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface GatewayRequestMessageOrBuilder extends
      // @@protoc_insertion_point(interface_extends:gateway_service.GatewayRequestMessage)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>.gateway_service.SubscribeMessageRequest subscribe_request = 1;</code>
     * @return Whether the subscribeRequest field is set.
     */
    boolean hasSubscribeRequest();
    /**
     * <code>.gateway_service.SubscribeMessageRequest subscribe_request = 1;</code>
     * @return The subscribeRequest.
     */
    gateway_service.Envelope.SubscribeMessageRequest getSubscribeRequest();
    /**
     * <code>.gateway_service.SubscribeMessageRequest subscribe_request = 1;</code>
     */
    gateway_service.Envelope.SubscribeMessageRequestOrBuilder getSubscribeRequestOrBuilder();

    /**
     * <code>.gateway_service.UnsubscribeMessageRequest unsubscribe_request = 2;</code>
     * @return Whether the unsubscribeRequest field is set.
     */
    boolean hasUnsubscribeRequest();
    /**
     * <code>.gateway_service.UnsubscribeMessageRequest unsubscribe_request = 2;</code>
     * @return The unsubscribeRequest.
     */
    gateway_service.Envelope.UnsubscribeMessageRequest getUnsubscribeRequest();
    /**
     * <code>.gateway_service.UnsubscribeMessageRequest unsubscribe_request = 2;</code>
     */
    gateway_service.Envelope.UnsubscribeMessageRequestOrBuilder getUnsubscribeRequestOrBuilder();

    /**
     * <code>.gateway_service.RequestMessage request_message = 3;</code>
     * @return Whether the requestMessage field is set.
     */
    boolean hasRequestMessage();
    /**
     * <code>.gateway_service.RequestMessage request_message = 3;</code>
     * @return The requestMessage.
     */
    gateway_service.Envelope.RequestMessage getRequestMessage();
    /**
     * <code>.gateway_service.RequestMessage request_message = 3;</code>
     */
    gateway_service.Envelope.RequestMessageOrBuilder getRequestMessageOrBuilder();

    /**
     * <code>.gateway_service.RFCRequestMessage rfc_message = 4;</code>
     * @return Whether the rfcMessage field is set.
     */
    boolean hasRfcMessage();
    /**
     * <code>.gateway_service.RFCRequestMessage rfc_message = 4;</code>
     * @return The rfcMessage.
     */
    gateway_service.Envelope.RFCRequestMessage getRfcMessage();
    /**
     * <code>.gateway_service.RFCRequestMessage rfc_message = 4;</code>
     */
    gateway_service.Envelope.RFCRequestMessageOrBuilder getRfcMessageOrBuilder();

    /**
     * <pre>
     * The message creation time in UTC seconds from UNIX epoch
     * </pre>
     *
     * <code>optional uint64 creation_time = 5;</code>
     * @return Whether the creationTime field is set.
     */
    boolean hasCreationTime();
    /**
     * <pre>
     * The message creation time in UTC seconds from UNIX epoch
     * </pre>
     *
     * <code>optional uint64 creation_time = 5;</code>
     * @return The creationTime.
     */
    long getCreationTime();

    /**
     * <pre>
     * Time to live in seconds from creation time
     * If request is received after the time to live has expired then the request will not be processed
     * and the the response will be ENUM_OPERATION_STATUS_REQUEST_EXPIRED
     * </pre>
     *
     * <code>optional uint32 time_to_live = 6;</code>
     * @return Whether the timeToLive field is set.
     */
    boolean hasTimeToLive();
    /**
     * <pre>
     * Time to live in seconds from creation time
     * If request is received after the time to live has expired then the request will not be processed
     * and the the response will be ENUM_OPERATION_STATUS_REQUEST_EXPIRED
     * </pre>
     *
     * <code>optional uint32 time_to_live = 6;</code>
     * @return The timeToLive.
     */
    int getTimeToLive();

    /**
     * <pre>
     * Sequence number increments with every request
     * If a sequence number is received that is less than or equal to the last sequence number received then the request
     * will not be processed and the response will be NUM_OPERATION_STATUS_REQUEST_OUT_OF_SEQUENCE
     * Sequence number will start at 0 whenever the service gateway is restarted and will be fast-forwarded to the value of
     * the first request when it is received
     * </pre>
     *
     * <code>optional uint32 sequence_no = 7;</code>
     * @return Whether the sequenceNo field is set.
     */
    boolean hasSequenceNo();
    /**
     * <pre>
     * Sequence number increments with every request
     * If a sequence number is received that is less than or equal to the last sequence number received then the request
     * will not be processed and the response will be NUM_OPERATION_STATUS_REQUEST_OUT_OF_SEQUENCE
     * Sequence number will start at 0 whenever the service gateway is restarted and will be fast-forwarded to the value of
     * the first request when it is received
     * </pre>
     *
     * <code>optional uint32 sequence_no = 7;</code>
     * @return The sequenceNo.
     */
    int getSequenceNo();

    /**
     * <pre>
     * Vehicle unique id, used to ensure that the request is for this vehicle and this vehicle alone
     * Will be the unique id that is provided to OBG during OBG's authentication process
     * If the vuid is not correct then the request will not be processed and the response will be ENUM_OPERATION_STATUS_INVALID_VUID
     * </pre>
     *
     * <code>optional string vuid = 8;</code>
     * @return Whether the vuid field is set.
     */
    boolean hasVuid();
    /**
     * <pre>
     * Vehicle unique id, used to ensure that the request is for this vehicle and this vehicle alone
     * Will be the unique id that is provided to OBG during OBG's authentication process
     * If the vuid is not correct then the request will not be processed and the response will be ENUM_OPERATION_STATUS_INVALID_VUID
     * </pre>
     *
     * <code>optional string vuid = 8;</code>
     * @return The vuid.
     */
    java.lang.String getVuid();
    /**
     * <pre>
     * Vehicle unique id, used to ensure that the request is for this vehicle and this vehicle alone
     * Will be the unique id that is provided to OBG during OBG's authentication process
     * If the vuid is not correct then the request will not be processed and the response will be ENUM_OPERATION_STATUS_INVALID_VUID
     * </pre>
     *
     * <code>optional string vuid = 8;</code>
     * @return The bytes for vuid.
     */
    com.google.protobuf.ByteString
        getVuidBytes();

    gateway_service.Envelope.GatewayRequestMessage.MessageCase getMessageCase();
  }
  /**
   * <pre>
   * Request messages received from the cloud backend
   * </pre>
   *
   * Protobuf type {@code gateway_service.GatewayRequestMessage}
   */
  public static final class GatewayRequestMessage extends
      com.google.protobuf.GeneratedMessage implements
      // @@protoc_insertion_point(message_implements:gateway_service.GatewayRequestMessage)
      GatewayRequestMessageOrBuilder {
  private static final long serialVersionUID = 0L;
    static {
      com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
        com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
        /* major= */ 4,
        /* minor= */ 28,
        /* patch= */ 2,
        /* suffix= */ "",
        GatewayRequestMessage.class.getName());
    }
    // Use GatewayRequestMessage.newBuilder() to construct.
    private GatewayRequestMessage(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
    }
    private GatewayRequestMessage() {
      vuid_ = "";
    }

    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return gateway_service.Envelope.internal_static_gateway_service_GatewayRequestMessage_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return gateway_service.Envelope.internal_static_gateway_service_GatewayRequestMessage_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              gateway_service.Envelope.GatewayRequestMessage.class, gateway_service.Envelope.GatewayRequestMessage.Builder.class);
    }

    private int bitField0_;
    private int messageCase_ = 0;
    @SuppressWarnings("serial")
    private java.lang.Object message_;
    public enum MessageCase
        implements com.google.protobuf.Internal.EnumLite,
            com.google.protobuf.AbstractMessage.InternalOneOfEnum {
      SUBSCRIBE_REQUEST(1),
      UNSUBSCRIBE_REQUEST(2),
      REQUEST_MESSAGE(3),
      RFC_MESSAGE(4),
      MESSAGE_NOT_SET(0);
      private final int value;
      private MessageCase(int value) {
        this.value = value;
      }
      /**
       * @param value The number of the enum to look for.
       * @return The enum associated with the given number.
       * @deprecated Use {@link #forNumber(int)} instead.
       */
      @java.lang.Deprecated
      public static MessageCase valueOf(int value) {
        return forNumber(value);
      }

      public static MessageCase forNumber(int value) {
        switch (value) {
          case 1: return SUBSCRIBE_REQUEST;
          case 2: return UNSUBSCRIBE_REQUEST;
          case 3: return REQUEST_MESSAGE;
          case 4: return RFC_MESSAGE;
          case 0: return MESSAGE_NOT_SET;
          default: return null;
        }
      }
      public int getNumber() {
        return this.value;
      }
    };

    public MessageCase
    getMessageCase() {
      return MessageCase.forNumber(
          messageCase_);
    }

    public static final int SUBSCRIBE_REQUEST_FIELD_NUMBER = 1;
    /**
     * <code>.gateway_service.SubscribeMessageRequest subscribe_request = 1;</code>
     * @return Whether the subscribeRequest field is set.
     */
    @java.lang.Override
    public boolean hasSubscribeRequest() {
      return messageCase_ == 1;
    }
    /**
     * <code>.gateway_service.SubscribeMessageRequest subscribe_request = 1;</code>
     * @return The subscribeRequest.
     */
    @java.lang.Override
    public gateway_service.Envelope.SubscribeMessageRequest getSubscribeRequest() {
      if (messageCase_ == 1) {
         return (gateway_service.Envelope.SubscribeMessageRequest) message_;
      }
      return gateway_service.Envelope.SubscribeMessageRequest.getDefaultInstance();
    }
    /**
     * <code>.gateway_service.SubscribeMessageRequest subscribe_request = 1;</code>
     */
    @java.lang.Override
    public gateway_service.Envelope.SubscribeMessageRequestOrBuilder getSubscribeRequestOrBuilder() {
      if (messageCase_ == 1) {
         return (gateway_service.Envelope.SubscribeMessageRequest) message_;
      }
      return gateway_service.Envelope.SubscribeMessageRequest.getDefaultInstance();
    }

    public static final int UNSUBSCRIBE_REQUEST_FIELD_NUMBER = 2;
    /**
     * <code>.gateway_service.UnsubscribeMessageRequest unsubscribe_request = 2;</code>
     * @return Whether the unsubscribeRequest field is set.
     */
    @java.lang.Override
    public boolean hasUnsubscribeRequest() {
      return messageCase_ == 2;
    }
    /**
     * <code>.gateway_service.UnsubscribeMessageRequest unsubscribe_request = 2;</code>
     * @return The unsubscribeRequest.
     */
    @java.lang.Override
    public gateway_service.Envelope.UnsubscribeMessageRequest getUnsubscribeRequest() {
      if (messageCase_ == 2) {
         return (gateway_service.Envelope.UnsubscribeMessageRequest) message_;
      }
      return gateway_service.Envelope.UnsubscribeMessageRequest.getDefaultInstance();
    }
    /**
     * <code>.gateway_service.UnsubscribeMessageRequest unsubscribe_request = 2;</code>
     */
    @java.lang.Override
    public gateway_service.Envelope.UnsubscribeMessageRequestOrBuilder getUnsubscribeRequestOrBuilder() {
      if (messageCase_ == 2) {
         return (gateway_service.Envelope.UnsubscribeMessageRequest) message_;
      }
      return gateway_service.Envelope.UnsubscribeMessageRequest.getDefaultInstance();
    }

    public static final int REQUEST_MESSAGE_FIELD_NUMBER = 3;
    /**
     * <code>.gateway_service.RequestMessage request_message = 3;</code>
     * @return Whether the requestMessage field is set.
     */
    @java.lang.Override
    public boolean hasRequestMessage() {
      return messageCase_ == 3;
    }
    /**
     * <code>.gateway_service.RequestMessage request_message = 3;</code>
     * @return The requestMessage.
     */
    @java.lang.Override
    public gateway_service.Envelope.RequestMessage getRequestMessage() {
      if (messageCase_ == 3) {
         return (gateway_service.Envelope.RequestMessage) message_;
      }
      return gateway_service.Envelope.RequestMessage.getDefaultInstance();
    }
    /**
     * <code>.gateway_service.RequestMessage request_message = 3;</code>
     */
    @java.lang.Override
    public gateway_service.Envelope.RequestMessageOrBuilder getRequestMessageOrBuilder() {
      if (messageCase_ == 3) {
         return (gateway_service.Envelope.RequestMessage) message_;
      }
      return gateway_service.Envelope.RequestMessage.getDefaultInstance();
    }

    public static final int RFC_MESSAGE_FIELD_NUMBER = 4;
    /**
     * <code>.gateway_service.RFCRequestMessage rfc_message = 4;</code>
     * @return Whether the rfcMessage field is set.
     */
    @java.lang.Override
    public boolean hasRfcMessage() {
      return messageCase_ == 4;
    }
    /**
     * <code>.gateway_service.RFCRequestMessage rfc_message = 4;</code>
     * @return The rfcMessage.
     */
    @java.lang.Override
    public gateway_service.Envelope.RFCRequestMessage getRfcMessage() {
      if (messageCase_ == 4) {
         return (gateway_service.Envelope.RFCRequestMessage) message_;
      }
      return gateway_service.Envelope.RFCRequestMessage.getDefaultInstance();
    }
    /**
     * <code>.gateway_service.RFCRequestMessage rfc_message = 4;</code>
     */
    @java.lang.Override
    public gateway_service.Envelope.RFCRequestMessageOrBuilder getRfcMessageOrBuilder() {
      if (messageCase_ == 4) {
         return (gateway_service.Envelope.RFCRequestMessage) message_;
      }
      return gateway_service.Envelope.RFCRequestMessage.getDefaultInstance();
    }

    public static final int CREATION_TIME_FIELD_NUMBER = 5;
    private long creationTime_ = 0L;
    /**
     * <pre>
     * The message creation time in UTC seconds from UNIX epoch
     * </pre>
     *
     * <code>optional uint64 creation_time = 5;</code>
     * @return Whether the creationTime field is set.
     */
    @java.lang.Override
    public boolean hasCreationTime() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <pre>
     * The message creation time in UTC seconds from UNIX epoch
     * </pre>
     *
     * <code>optional uint64 creation_time = 5;</code>
     * @return The creationTime.
     */
    @java.lang.Override
    public long getCreationTime() {
      return creationTime_;
    }

    public static final int TIME_TO_LIVE_FIELD_NUMBER = 6;
    private int timeToLive_ = 0;
    /**
     * <pre>
     * Time to live in seconds from creation time
     * If request is received after the time to live has expired then the request will not be processed
     * and the the response will be ENUM_OPERATION_STATUS_REQUEST_EXPIRED
     * </pre>
     *
     * <code>optional uint32 time_to_live = 6;</code>
     * @return Whether the timeToLive field is set.
     */
    @java.lang.Override
    public boolean hasTimeToLive() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <pre>
     * Time to live in seconds from creation time
     * If request is received after the time to live has expired then the request will not be processed
     * and the the response will be ENUM_OPERATION_STATUS_REQUEST_EXPIRED
     * </pre>
     *
     * <code>optional uint32 time_to_live = 6;</code>
     * @return The timeToLive.
     */
    @java.lang.Override
    public int getTimeToLive() {
      return timeToLive_;
    }

    public static final int SEQUENCE_NO_FIELD_NUMBER = 7;
    private int sequenceNo_ = 0;
    /**
     * <pre>
     * Sequence number increments with every request
     * If a sequence number is received that is less than or equal to the last sequence number received then the request
     * will not be processed and the response will be NUM_OPERATION_STATUS_REQUEST_OUT_OF_SEQUENCE
     * Sequence number will start at 0 whenever the service gateway is restarted and will be fast-forwarded to the value of
     * the first request when it is received
     * </pre>
     *
     * <code>optional uint32 sequence_no = 7;</code>
     * @return Whether the sequenceNo field is set.
     */
    @java.lang.Override
    public boolean hasSequenceNo() {
      return ((bitField0_ & 0x00000004) != 0);
    }
    /**
     * <pre>
     * Sequence number increments with every request
     * If a sequence number is received that is less than or equal to the last sequence number received then the request
     * will not be processed and the response will be NUM_OPERATION_STATUS_REQUEST_OUT_OF_SEQUENCE
     * Sequence number will start at 0 whenever the service gateway is restarted and will be fast-forwarded to the value of
     * the first request when it is received
     * </pre>
     *
     * <code>optional uint32 sequence_no = 7;</code>
     * @return The sequenceNo.
     */
    @java.lang.Override
    public int getSequenceNo() {
      return sequenceNo_;
    }

    public static final int VUID_FIELD_NUMBER = 8;
    @SuppressWarnings("serial")
    private volatile java.lang.Object vuid_ = "";
    /**
     * <pre>
     * Vehicle unique id, used to ensure that the request is for this vehicle and this vehicle alone
     * Will be the unique id that is provided to OBG during OBG's authentication process
     * If the vuid is not correct then the request will not be processed and the response will be ENUM_OPERATION_STATUS_INVALID_VUID
     * </pre>
     *
     * <code>optional string vuid = 8;</code>
     * @return Whether the vuid field is set.
     */
    @java.lang.Override
    public boolean hasVuid() {
      return ((bitField0_ & 0x00000008) != 0);
    }
    /**
     * <pre>
     * Vehicle unique id, used to ensure that the request is for this vehicle and this vehicle alone
     * Will be the unique id that is provided to OBG during OBG's authentication process
     * If the vuid is not correct then the request will not be processed and the response will be ENUM_OPERATION_STATUS_INVALID_VUID
     * </pre>
     *
     * <code>optional string vuid = 8;</code>
     * @return The vuid.
     */
    @java.lang.Override
    public java.lang.String getVuid() {
      java.lang.Object ref = vuid_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        vuid_ = s;
        return s;
      }
    }
    /**
     * <pre>
     * Vehicle unique id, used to ensure that the request is for this vehicle and this vehicle alone
     * Will be the unique id that is provided to OBG during OBG's authentication process
     * If the vuid is not correct then the request will not be processed and the response will be ENUM_OPERATION_STATUS_INVALID_VUID
     * </pre>
     *
     * <code>optional string vuid = 8;</code>
     * @return The bytes for vuid.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getVuidBytes() {
      java.lang.Object ref = vuid_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        vuid_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (messageCase_ == 1) {
        output.writeMessage(1, (gateway_service.Envelope.SubscribeMessageRequest) message_);
      }
      if (messageCase_ == 2) {
        output.writeMessage(2, (gateway_service.Envelope.UnsubscribeMessageRequest) message_);
      }
      if (messageCase_ == 3) {
        output.writeMessage(3, (gateway_service.Envelope.RequestMessage) message_);
      }
      if (messageCase_ == 4) {
        output.writeMessage(4, (gateway_service.Envelope.RFCRequestMessage) message_);
      }
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeUInt64(5, creationTime_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        output.writeUInt32(6, timeToLive_);
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        output.writeUInt32(7, sequenceNo_);
      }
      if (((bitField0_ & 0x00000008) != 0)) {
        com.google.protobuf.GeneratedMessage.writeString(output, 8, vuid_);
      }
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (messageCase_ == 1) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(1, (gateway_service.Envelope.SubscribeMessageRequest) message_);
      }
      if (messageCase_ == 2) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(2, (gateway_service.Envelope.UnsubscribeMessageRequest) message_);
      }
      if (messageCase_ == 3) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(3, (gateway_service.Envelope.RequestMessage) message_);
      }
      if (messageCase_ == 4) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(4, (gateway_service.Envelope.RFCRequestMessage) message_);
      }
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt64Size(5, creationTime_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(6, timeToLive_);
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(7, sequenceNo_);
      }
      if (((bitField0_ & 0x00000008) != 0)) {
        size += com.google.protobuf.GeneratedMessage.computeStringSize(8, vuid_);
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof gateway_service.Envelope.GatewayRequestMessage)) {
        return super.equals(obj);
      }
      gateway_service.Envelope.GatewayRequestMessage other = (gateway_service.Envelope.GatewayRequestMessage) obj;

      if (hasCreationTime() != other.hasCreationTime()) return false;
      if (hasCreationTime()) {
        if (getCreationTime()
            != other.getCreationTime()) return false;
      }
      if (hasTimeToLive() != other.hasTimeToLive()) return false;
      if (hasTimeToLive()) {
        if (getTimeToLive()
            != other.getTimeToLive()) return false;
      }
      if (hasSequenceNo() != other.hasSequenceNo()) return false;
      if (hasSequenceNo()) {
        if (getSequenceNo()
            != other.getSequenceNo()) return false;
      }
      if (hasVuid() != other.hasVuid()) return false;
      if (hasVuid()) {
        if (!getVuid()
            .equals(other.getVuid())) return false;
      }
      if (!getMessageCase().equals(other.getMessageCase())) return false;
      switch (messageCase_) {
        case 1:
          if (!getSubscribeRequest()
              .equals(other.getSubscribeRequest())) return false;
          break;
        case 2:
          if (!getUnsubscribeRequest()
              .equals(other.getUnsubscribeRequest())) return false;
          break;
        case 3:
          if (!getRequestMessage()
              .equals(other.getRequestMessage())) return false;
          break;
        case 4:
          if (!getRfcMessage()
              .equals(other.getRfcMessage())) return false;
          break;
        case 0:
        default:
      }
      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasCreationTime()) {
        hash = (37 * hash) + CREATION_TIME_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getCreationTime());
      }
      if (hasTimeToLive()) {
        hash = (37 * hash) + TIME_TO_LIVE_FIELD_NUMBER;
        hash = (53 * hash) + getTimeToLive();
      }
      if (hasSequenceNo()) {
        hash = (37 * hash) + SEQUENCE_NO_FIELD_NUMBER;
        hash = (53 * hash) + getSequenceNo();
      }
      if (hasVuid()) {
        hash = (37 * hash) + VUID_FIELD_NUMBER;
        hash = (53 * hash) + getVuid().hashCode();
      }
      switch (messageCase_) {
        case 1:
          hash = (37 * hash) + SUBSCRIBE_REQUEST_FIELD_NUMBER;
          hash = (53 * hash) + getSubscribeRequest().hashCode();
          break;
        case 2:
          hash = (37 * hash) + UNSUBSCRIBE_REQUEST_FIELD_NUMBER;
          hash = (53 * hash) + getUnsubscribeRequest().hashCode();
          break;
        case 3:
          hash = (37 * hash) + REQUEST_MESSAGE_FIELD_NUMBER;
          hash = (53 * hash) + getRequestMessage().hashCode();
          break;
        case 4:
          hash = (37 * hash) + RFC_MESSAGE_FIELD_NUMBER;
          hash = (53 * hash) + getRfcMessage().hashCode();
          break;
        case 0:
        default:
      }
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static gateway_service.Envelope.GatewayRequestMessage parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static gateway_service.Envelope.GatewayRequestMessage parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static gateway_service.Envelope.GatewayRequestMessage parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static gateway_service.Envelope.GatewayRequestMessage parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static gateway_service.Envelope.GatewayRequestMessage parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static gateway_service.Envelope.GatewayRequestMessage parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static gateway_service.Envelope.GatewayRequestMessage parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input);
    }
    public static gateway_service.Envelope.GatewayRequestMessage parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public static gateway_service.Envelope.GatewayRequestMessage parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseDelimitedWithIOException(PARSER, input);
    }

    public static gateway_service.Envelope.GatewayRequestMessage parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static gateway_service.Envelope.GatewayRequestMessage parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input);
    }
    public static gateway_service.Envelope.GatewayRequestMessage parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(gateway_service.Envelope.GatewayRequestMessage prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     * Request messages received from the cloud backend
     * </pre>
     *
     * Protobuf type {@code gateway_service.GatewayRequestMessage}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:gateway_service.GatewayRequestMessage)
        gateway_service.Envelope.GatewayRequestMessageOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return gateway_service.Envelope.internal_static_gateway_service_GatewayRequestMessage_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return gateway_service.Envelope.internal_static_gateway_service_GatewayRequestMessage_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                gateway_service.Envelope.GatewayRequestMessage.class, gateway_service.Envelope.GatewayRequestMessage.Builder.class);
      }

      // Construct using gateway_service.Envelope.GatewayRequestMessage.newBuilder()
      private Builder() {

      }

      private Builder(
          com.google.protobuf.GeneratedMessage.BuilderParent parent) {
        super(parent);

      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        bitField0_ = 0;
        if (subscribeRequestBuilder_ != null) {
          subscribeRequestBuilder_.clear();
        }
        if (unsubscribeRequestBuilder_ != null) {
          unsubscribeRequestBuilder_.clear();
        }
        if (requestMessageBuilder_ != null) {
          requestMessageBuilder_.clear();
        }
        if (rfcMessageBuilder_ != null) {
          rfcMessageBuilder_.clear();
        }
        creationTime_ = 0L;
        timeToLive_ = 0;
        sequenceNo_ = 0;
        vuid_ = "";
        messageCase_ = 0;
        message_ = null;
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return gateway_service.Envelope.internal_static_gateway_service_GatewayRequestMessage_descriptor;
      }

      @java.lang.Override
      public gateway_service.Envelope.GatewayRequestMessage getDefaultInstanceForType() {
        return gateway_service.Envelope.GatewayRequestMessage.getDefaultInstance();
      }

      @java.lang.Override
      public gateway_service.Envelope.GatewayRequestMessage build() {
        gateway_service.Envelope.GatewayRequestMessage result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public gateway_service.Envelope.GatewayRequestMessage buildPartial() {
        gateway_service.Envelope.GatewayRequestMessage result = new gateway_service.Envelope.GatewayRequestMessage(this);
        if (bitField0_ != 0) { buildPartial0(result); }
        buildPartialOneofs(result);
        onBuilt();
        return result;
      }

      private void buildPartial0(gateway_service.Envelope.GatewayRequestMessage result) {
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000010) != 0)) {
          result.creationTime_ = creationTime_;
          to_bitField0_ |= 0x00000001;
        }
        if (((from_bitField0_ & 0x00000020) != 0)) {
          result.timeToLive_ = timeToLive_;
          to_bitField0_ |= 0x00000002;
        }
        if (((from_bitField0_ & 0x00000040) != 0)) {
          result.sequenceNo_ = sequenceNo_;
          to_bitField0_ |= 0x00000004;
        }
        if (((from_bitField0_ & 0x00000080) != 0)) {
          result.vuid_ = vuid_;
          to_bitField0_ |= 0x00000008;
        }
        result.bitField0_ |= to_bitField0_;
      }

      private void buildPartialOneofs(gateway_service.Envelope.GatewayRequestMessage result) {
        result.messageCase_ = messageCase_;
        result.message_ = this.message_;
        if (messageCase_ == 1 &&
            subscribeRequestBuilder_ != null) {
          result.message_ = subscribeRequestBuilder_.build();
        }
        if (messageCase_ == 2 &&
            unsubscribeRequestBuilder_ != null) {
          result.message_ = unsubscribeRequestBuilder_.build();
        }
        if (messageCase_ == 3 &&
            requestMessageBuilder_ != null) {
          result.message_ = requestMessageBuilder_.build();
        }
        if (messageCase_ == 4 &&
            rfcMessageBuilder_ != null) {
          result.message_ = rfcMessageBuilder_.build();
        }
      }

      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof gateway_service.Envelope.GatewayRequestMessage) {
          return mergeFrom((gateway_service.Envelope.GatewayRequestMessage)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(gateway_service.Envelope.GatewayRequestMessage other) {
        if (other == gateway_service.Envelope.GatewayRequestMessage.getDefaultInstance()) return this;
        if (other.hasCreationTime()) {
          setCreationTime(other.getCreationTime());
        }
        if (other.hasTimeToLive()) {
          setTimeToLive(other.getTimeToLive());
        }
        if (other.hasSequenceNo()) {
          setSequenceNo(other.getSequenceNo());
        }
        if (other.hasVuid()) {
          vuid_ = other.vuid_;
          bitField0_ |= 0x00000080;
          onChanged();
        }
        switch (other.getMessageCase()) {
          case SUBSCRIBE_REQUEST: {
            mergeSubscribeRequest(other.getSubscribeRequest());
            break;
          }
          case UNSUBSCRIBE_REQUEST: {
            mergeUnsubscribeRequest(other.getUnsubscribeRequest());
            break;
          }
          case REQUEST_MESSAGE: {
            mergeRequestMessage(other.getRequestMessage());
            break;
          }
          case RFC_MESSAGE: {
            mergeRfcMessage(other.getRfcMessage());
            break;
          }
          case MESSAGE_NOT_SET: {
            break;
          }
        }
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 10: {
                input.readMessage(
                    getSubscribeRequestFieldBuilder().getBuilder(),
                    extensionRegistry);
                messageCase_ = 1;
                break;
              } // case 10
              case 18: {
                input.readMessage(
                    getUnsubscribeRequestFieldBuilder().getBuilder(),
                    extensionRegistry);
                messageCase_ = 2;
                break;
              } // case 18
              case 26: {
                input.readMessage(
                    getRequestMessageFieldBuilder().getBuilder(),
                    extensionRegistry);
                messageCase_ = 3;
                break;
              } // case 26
              case 34: {
                input.readMessage(
                    getRfcMessageFieldBuilder().getBuilder(),
                    extensionRegistry);
                messageCase_ = 4;
                break;
              } // case 34
              case 40: {
                creationTime_ = input.readUInt64();
                bitField0_ |= 0x00000010;
                break;
              } // case 40
              case 48: {
                timeToLive_ = input.readUInt32();
                bitField0_ |= 0x00000020;
                break;
              } // case 48
              case 56: {
                sequenceNo_ = input.readUInt32();
                bitField0_ |= 0x00000040;
                break;
              } // case 56
              case 66: {
                vuid_ = input.readStringRequireUtf8();
                bitField0_ |= 0x00000080;
                break;
              } // case 66
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }
      private int messageCase_ = 0;
      private java.lang.Object message_;
      public MessageCase
          getMessageCase() {
        return MessageCase.forNumber(
            messageCase_);
      }

      public Builder clearMessage() {
        messageCase_ = 0;
        message_ = null;
        onChanged();
        return this;
      }

      private int bitField0_;

      private com.google.protobuf.SingleFieldBuilder<
          gateway_service.Envelope.SubscribeMessageRequest, gateway_service.Envelope.SubscribeMessageRequest.Builder, gateway_service.Envelope.SubscribeMessageRequestOrBuilder> subscribeRequestBuilder_;
      /**
       * <code>.gateway_service.SubscribeMessageRequest subscribe_request = 1;</code>
       * @return Whether the subscribeRequest field is set.
       */
      @java.lang.Override
      public boolean hasSubscribeRequest() {
        return messageCase_ == 1;
      }
      /**
       * <code>.gateway_service.SubscribeMessageRequest subscribe_request = 1;</code>
       * @return The subscribeRequest.
       */
      @java.lang.Override
      public gateway_service.Envelope.SubscribeMessageRequest getSubscribeRequest() {
        if (subscribeRequestBuilder_ == null) {
          if (messageCase_ == 1) {
            return (gateway_service.Envelope.SubscribeMessageRequest) message_;
          }
          return gateway_service.Envelope.SubscribeMessageRequest.getDefaultInstance();
        } else {
          if (messageCase_ == 1) {
            return subscribeRequestBuilder_.getMessage();
          }
          return gateway_service.Envelope.SubscribeMessageRequest.getDefaultInstance();
        }
      }
      /**
       * <code>.gateway_service.SubscribeMessageRequest subscribe_request = 1;</code>
       */
      public Builder setSubscribeRequest(gateway_service.Envelope.SubscribeMessageRequest value) {
        if (subscribeRequestBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          message_ = value;
          onChanged();
        } else {
          subscribeRequestBuilder_.setMessage(value);
        }
        messageCase_ = 1;
        return this;
      }
      /**
       * <code>.gateway_service.SubscribeMessageRequest subscribe_request = 1;</code>
       */
      public Builder setSubscribeRequest(
          gateway_service.Envelope.SubscribeMessageRequest.Builder builderForValue) {
        if (subscribeRequestBuilder_ == null) {
          message_ = builderForValue.build();
          onChanged();
        } else {
          subscribeRequestBuilder_.setMessage(builderForValue.build());
        }
        messageCase_ = 1;
        return this;
      }
      /**
       * <code>.gateway_service.SubscribeMessageRequest subscribe_request = 1;</code>
       */
      public Builder mergeSubscribeRequest(gateway_service.Envelope.SubscribeMessageRequest value) {
        if (subscribeRequestBuilder_ == null) {
          if (messageCase_ == 1 &&
              message_ != gateway_service.Envelope.SubscribeMessageRequest.getDefaultInstance()) {
            message_ = gateway_service.Envelope.SubscribeMessageRequest.newBuilder((gateway_service.Envelope.SubscribeMessageRequest) message_)
                .mergeFrom(value).buildPartial();
          } else {
            message_ = value;
          }
          onChanged();
        } else {
          if (messageCase_ == 1) {
            subscribeRequestBuilder_.mergeFrom(value);
          } else {
            subscribeRequestBuilder_.setMessage(value);
          }
        }
        messageCase_ = 1;
        return this;
      }
      /**
       * <code>.gateway_service.SubscribeMessageRequest subscribe_request = 1;</code>
       */
      public Builder clearSubscribeRequest() {
        if (subscribeRequestBuilder_ == null) {
          if (messageCase_ == 1) {
            messageCase_ = 0;
            message_ = null;
            onChanged();
          }
        } else {
          if (messageCase_ == 1) {
            messageCase_ = 0;
            message_ = null;
          }
          subscribeRequestBuilder_.clear();
        }
        return this;
      }
      /**
       * <code>.gateway_service.SubscribeMessageRequest subscribe_request = 1;</code>
       */
      public gateway_service.Envelope.SubscribeMessageRequest.Builder getSubscribeRequestBuilder() {
        return getSubscribeRequestFieldBuilder().getBuilder();
      }
      /**
       * <code>.gateway_service.SubscribeMessageRequest subscribe_request = 1;</code>
       */
      @java.lang.Override
      public gateway_service.Envelope.SubscribeMessageRequestOrBuilder getSubscribeRequestOrBuilder() {
        if ((messageCase_ == 1) && (subscribeRequestBuilder_ != null)) {
          return subscribeRequestBuilder_.getMessageOrBuilder();
        } else {
          if (messageCase_ == 1) {
            return (gateway_service.Envelope.SubscribeMessageRequest) message_;
          }
          return gateway_service.Envelope.SubscribeMessageRequest.getDefaultInstance();
        }
      }
      /**
       * <code>.gateway_service.SubscribeMessageRequest subscribe_request = 1;</code>
       */
      private com.google.protobuf.SingleFieldBuilder<
          gateway_service.Envelope.SubscribeMessageRequest, gateway_service.Envelope.SubscribeMessageRequest.Builder, gateway_service.Envelope.SubscribeMessageRequestOrBuilder> 
          getSubscribeRequestFieldBuilder() {
        if (subscribeRequestBuilder_ == null) {
          if (!(messageCase_ == 1)) {
            message_ = gateway_service.Envelope.SubscribeMessageRequest.getDefaultInstance();
          }
          subscribeRequestBuilder_ = new com.google.protobuf.SingleFieldBuilder<
              gateway_service.Envelope.SubscribeMessageRequest, gateway_service.Envelope.SubscribeMessageRequest.Builder, gateway_service.Envelope.SubscribeMessageRequestOrBuilder>(
                  (gateway_service.Envelope.SubscribeMessageRequest) message_,
                  getParentForChildren(),
                  isClean());
          message_ = null;
        }
        messageCase_ = 1;
        onChanged();
        return subscribeRequestBuilder_;
      }

      private com.google.protobuf.SingleFieldBuilder<
          gateway_service.Envelope.UnsubscribeMessageRequest, gateway_service.Envelope.UnsubscribeMessageRequest.Builder, gateway_service.Envelope.UnsubscribeMessageRequestOrBuilder> unsubscribeRequestBuilder_;
      /**
       * <code>.gateway_service.UnsubscribeMessageRequest unsubscribe_request = 2;</code>
       * @return Whether the unsubscribeRequest field is set.
       */
      @java.lang.Override
      public boolean hasUnsubscribeRequest() {
        return messageCase_ == 2;
      }
      /**
       * <code>.gateway_service.UnsubscribeMessageRequest unsubscribe_request = 2;</code>
       * @return The unsubscribeRequest.
       */
      @java.lang.Override
      public gateway_service.Envelope.UnsubscribeMessageRequest getUnsubscribeRequest() {
        if (unsubscribeRequestBuilder_ == null) {
          if (messageCase_ == 2) {
            return (gateway_service.Envelope.UnsubscribeMessageRequest) message_;
          }
          return gateway_service.Envelope.UnsubscribeMessageRequest.getDefaultInstance();
        } else {
          if (messageCase_ == 2) {
            return unsubscribeRequestBuilder_.getMessage();
          }
          return gateway_service.Envelope.UnsubscribeMessageRequest.getDefaultInstance();
        }
      }
      /**
       * <code>.gateway_service.UnsubscribeMessageRequest unsubscribe_request = 2;</code>
       */
      public Builder setUnsubscribeRequest(gateway_service.Envelope.UnsubscribeMessageRequest value) {
        if (unsubscribeRequestBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          message_ = value;
          onChanged();
        } else {
          unsubscribeRequestBuilder_.setMessage(value);
        }
        messageCase_ = 2;
        return this;
      }
      /**
       * <code>.gateway_service.UnsubscribeMessageRequest unsubscribe_request = 2;</code>
       */
      public Builder setUnsubscribeRequest(
          gateway_service.Envelope.UnsubscribeMessageRequest.Builder builderForValue) {
        if (unsubscribeRequestBuilder_ == null) {
          message_ = builderForValue.build();
          onChanged();
        } else {
          unsubscribeRequestBuilder_.setMessage(builderForValue.build());
        }
        messageCase_ = 2;
        return this;
      }
      /**
       * <code>.gateway_service.UnsubscribeMessageRequest unsubscribe_request = 2;</code>
       */
      public Builder mergeUnsubscribeRequest(gateway_service.Envelope.UnsubscribeMessageRequest value) {
        if (unsubscribeRequestBuilder_ == null) {
          if (messageCase_ == 2 &&
              message_ != gateway_service.Envelope.UnsubscribeMessageRequest.getDefaultInstance()) {
            message_ = gateway_service.Envelope.UnsubscribeMessageRequest.newBuilder((gateway_service.Envelope.UnsubscribeMessageRequest) message_)
                .mergeFrom(value).buildPartial();
          } else {
            message_ = value;
          }
          onChanged();
        } else {
          if (messageCase_ == 2) {
            unsubscribeRequestBuilder_.mergeFrom(value);
          } else {
            unsubscribeRequestBuilder_.setMessage(value);
          }
        }
        messageCase_ = 2;
        return this;
      }
      /**
       * <code>.gateway_service.UnsubscribeMessageRequest unsubscribe_request = 2;</code>
       */
      public Builder clearUnsubscribeRequest() {
        if (unsubscribeRequestBuilder_ == null) {
          if (messageCase_ == 2) {
            messageCase_ = 0;
            message_ = null;
            onChanged();
          }
        } else {
          if (messageCase_ == 2) {
            messageCase_ = 0;
            message_ = null;
          }
          unsubscribeRequestBuilder_.clear();
        }
        return this;
      }
      /**
       * <code>.gateway_service.UnsubscribeMessageRequest unsubscribe_request = 2;</code>
       */
      public gateway_service.Envelope.UnsubscribeMessageRequest.Builder getUnsubscribeRequestBuilder() {
        return getUnsubscribeRequestFieldBuilder().getBuilder();
      }
      /**
       * <code>.gateway_service.UnsubscribeMessageRequest unsubscribe_request = 2;</code>
       */
      @java.lang.Override
      public gateway_service.Envelope.UnsubscribeMessageRequestOrBuilder getUnsubscribeRequestOrBuilder() {
        if ((messageCase_ == 2) && (unsubscribeRequestBuilder_ != null)) {
          return unsubscribeRequestBuilder_.getMessageOrBuilder();
        } else {
          if (messageCase_ == 2) {
            return (gateway_service.Envelope.UnsubscribeMessageRequest) message_;
          }
          return gateway_service.Envelope.UnsubscribeMessageRequest.getDefaultInstance();
        }
      }
      /**
       * <code>.gateway_service.UnsubscribeMessageRequest unsubscribe_request = 2;</code>
       */
      private com.google.protobuf.SingleFieldBuilder<
          gateway_service.Envelope.UnsubscribeMessageRequest, gateway_service.Envelope.UnsubscribeMessageRequest.Builder, gateway_service.Envelope.UnsubscribeMessageRequestOrBuilder> 
          getUnsubscribeRequestFieldBuilder() {
        if (unsubscribeRequestBuilder_ == null) {
          if (!(messageCase_ == 2)) {
            message_ = gateway_service.Envelope.UnsubscribeMessageRequest.getDefaultInstance();
          }
          unsubscribeRequestBuilder_ = new com.google.protobuf.SingleFieldBuilder<
              gateway_service.Envelope.UnsubscribeMessageRequest, gateway_service.Envelope.UnsubscribeMessageRequest.Builder, gateway_service.Envelope.UnsubscribeMessageRequestOrBuilder>(
                  (gateway_service.Envelope.UnsubscribeMessageRequest) message_,
                  getParentForChildren(),
                  isClean());
          message_ = null;
        }
        messageCase_ = 2;
        onChanged();
        return unsubscribeRequestBuilder_;
      }

      private com.google.protobuf.SingleFieldBuilder<
          gateway_service.Envelope.RequestMessage, gateway_service.Envelope.RequestMessage.Builder, gateway_service.Envelope.RequestMessageOrBuilder> requestMessageBuilder_;
      /**
       * <code>.gateway_service.RequestMessage request_message = 3;</code>
       * @return Whether the requestMessage field is set.
       */
      @java.lang.Override
      public boolean hasRequestMessage() {
        return messageCase_ == 3;
      }
      /**
       * <code>.gateway_service.RequestMessage request_message = 3;</code>
       * @return The requestMessage.
       */
      @java.lang.Override
      public gateway_service.Envelope.RequestMessage getRequestMessage() {
        if (requestMessageBuilder_ == null) {
          if (messageCase_ == 3) {
            return (gateway_service.Envelope.RequestMessage) message_;
          }
          return gateway_service.Envelope.RequestMessage.getDefaultInstance();
        } else {
          if (messageCase_ == 3) {
            return requestMessageBuilder_.getMessage();
          }
          return gateway_service.Envelope.RequestMessage.getDefaultInstance();
        }
      }
      /**
       * <code>.gateway_service.RequestMessage request_message = 3;</code>
       */
      public Builder setRequestMessage(gateway_service.Envelope.RequestMessage value) {
        if (requestMessageBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          message_ = value;
          onChanged();
        } else {
          requestMessageBuilder_.setMessage(value);
        }
        messageCase_ = 3;
        return this;
      }
      /**
       * <code>.gateway_service.RequestMessage request_message = 3;</code>
       */
      public Builder setRequestMessage(
          gateway_service.Envelope.RequestMessage.Builder builderForValue) {
        if (requestMessageBuilder_ == null) {
          message_ = builderForValue.build();
          onChanged();
        } else {
          requestMessageBuilder_.setMessage(builderForValue.build());
        }
        messageCase_ = 3;
        return this;
      }
      /**
       * <code>.gateway_service.RequestMessage request_message = 3;</code>
       */
      public Builder mergeRequestMessage(gateway_service.Envelope.RequestMessage value) {
        if (requestMessageBuilder_ == null) {
          if (messageCase_ == 3 &&
              message_ != gateway_service.Envelope.RequestMessage.getDefaultInstance()) {
            message_ = gateway_service.Envelope.RequestMessage.newBuilder((gateway_service.Envelope.RequestMessage) message_)
                .mergeFrom(value).buildPartial();
          } else {
            message_ = value;
          }
          onChanged();
        } else {
          if (messageCase_ == 3) {
            requestMessageBuilder_.mergeFrom(value);
          } else {
            requestMessageBuilder_.setMessage(value);
          }
        }
        messageCase_ = 3;
        return this;
      }
      /**
       * <code>.gateway_service.RequestMessage request_message = 3;</code>
       */
      public Builder clearRequestMessage() {
        if (requestMessageBuilder_ == null) {
          if (messageCase_ == 3) {
            messageCase_ = 0;
            message_ = null;
            onChanged();
          }
        } else {
          if (messageCase_ == 3) {
            messageCase_ = 0;
            message_ = null;
          }
          requestMessageBuilder_.clear();
        }
        return this;
      }
      /**
       * <code>.gateway_service.RequestMessage request_message = 3;</code>
       */
      public gateway_service.Envelope.RequestMessage.Builder getRequestMessageBuilder() {
        return getRequestMessageFieldBuilder().getBuilder();
      }
      /**
       * <code>.gateway_service.RequestMessage request_message = 3;</code>
       */
      @java.lang.Override
      public gateway_service.Envelope.RequestMessageOrBuilder getRequestMessageOrBuilder() {
        if ((messageCase_ == 3) && (requestMessageBuilder_ != null)) {
          return requestMessageBuilder_.getMessageOrBuilder();
        } else {
          if (messageCase_ == 3) {
            return (gateway_service.Envelope.RequestMessage) message_;
          }
          return gateway_service.Envelope.RequestMessage.getDefaultInstance();
        }
      }
      /**
       * <code>.gateway_service.RequestMessage request_message = 3;</code>
       */
      private com.google.protobuf.SingleFieldBuilder<
          gateway_service.Envelope.RequestMessage, gateway_service.Envelope.RequestMessage.Builder, gateway_service.Envelope.RequestMessageOrBuilder> 
          getRequestMessageFieldBuilder() {
        if (requestMessageBuilder_ == null) {
          if (!(messageCase_ == 3)) {
            message_ = gateway_service.Envelope.RequestMessage.getDefaultInstance();
          }
          requestMessageBuilder_ = new com.google.protobuf.SingleFieldBuilder<
              gateway_service.Envelope.RequestMessage, gateway_service.Envelope.RequestMessage.Builder, gateway_service.Envelope.RequestMessageOrBuilder>(
                  (gateway_service.Envelope.RequestMessage) message_,
                  getParentForChildren(),
                  isClean());
          message_ = null;
        }
        messageCase_ = 3;
        onChanged();
        return requestMessageBuilder_;
      }

      private com.google.protobuf.SingleFieldBuilder<
          gateway_service.Envelope.RFCRequestMessage, gateway_service.Envelope.RFCRequestMessage.Builder, gateway_service.Envelope.RFCRequestMessageOrBuilder> rfcMessageBuilder_;
      /**
       * <code>.gateway_service.RFCRequestMessage rfc_message = 4;</code>
       * @return Whether the rfcMessage field is set.
       */
      @java.lang.Override
      public boolean hasRfcMessage() {
        return messageCase_ == 4;
      }
      /**
       * <code>.gateway_service.RFCRequestMessage rfc_message = 4;</code>
       * @return The rfcMessage.
       */
      @java.lang.Override
      public gateway_service.Envelope.RFCRequestMessage getRfcMessage() {
        if (rfcMessageBuilder_ == null) {
          if (messageCase_ == 4) {
            return (gateway_service.Envelope.RFCRequestMessage) message_;
          }
          return gateway_service.Envelope.RFCRequestMessage.getDefaultInstance();
        } else {
          if (messageCase_ == 4) {
            return rfcMessageBuilder_.getMessage();
          }
          return gateway_service.Envelope.RFCRequestMessage.getDefaultInstance();
        }
      }
      /**
       * <code>.gateway_service.RFCRequestMessage rfc_message = 4;</code>
       */
      public Builder setRfcMessage(gateway_service.Envelope.RFCRequestMessage value) {
        if (rfcMessageBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          message_ = value;
          onChanged();
        } else {
          rfcMessageBuilder_.setMessage(value);
        }
        messageCase_ = 4;
        return this;
      }
      /**
       * <code>.gateway_service.RFCRequestMessage rfc_message = 4;</code>
       */
      public Builder setRfcMessage(
          gateway_service.Envelope.RFCRequestMessage.Builder builderForValue) {
        if (rfcMessageBuilder_ == null) {
          message_ = builderForValue.build();
          onChanged();
        } else {
          rfcMessageBuilder_.setMessage(builderForValue.build());
        }
        messageCase_ = 4;
        return this;
      }
      /**
       * <code>.gateway_service.RFCRequestMessage rfc_message = 4;</code>
       */
      public Builder mergeRfcMessage(gateway_service.Envelope.RFCRequestMessage value) {
        if (rfcMessageBuilder_ == null) {
          if (messageCase_ == 4 &&
              message_ != gateway_service.Envelope.RFCRequestMessage.getDefaultInstance()) {
            message_ = gateway_service.Envelope.RFCRequestMessage.newBuilder((gateway_service.Envelope.RFCRequestMessage) message_)
                .mergeFrom(value).buildPartial();
          } else {
            message_ = value;
          }
          onChanged();
        } else {
          if (messageCase_ == 4) {
            rfcMessageBuilder_.mergeFrom(value);
          } else {
            rfcMessageBuilder_.setMessage(value);
          }
        }
        messageCase_ = 4;
        return this;
      }
      /**
       * <code>.gateway_service.RFCRequestMessage rfc_message = 4;</code>
       */
      public Builder clearRfcMessage() {
        if (rfcMessageBuilder_ == null) {
          if (messageCase_ == 4) {
            messageCase_ = 0;
            message_ = null;
            onChanged();
          }
        } else {
          if (messageCase_ == 4) {
            messageCase_ = 0;
            message_ = null;
          }
          rfcMessageBuilder_.clear();
        }
        return this;
      }
      /**
       * <code>.gateway_service.RFCRequestMessage rfc_message = 4;</code>
       */
      public gateway_service.Envelope.RFCRequestMessage.Builder getRfcMessageBuilder() {
        return getRfcMessageFieldBuilder().getBuilder();
      }
      /**
       * <code>.gateway_service.RFCRequestMessage rfc_message = 4;</code>
       */
      @java.lang.Override
      public gateway_service.Envelope.RFCRequestMessageOrBuilder getRfcMessageOrBuilder() {
        if ((messageCase_ == 4) && (rfcMessageBuilder_ != null)) {
          return rfcMessageBuilder_.getMessageOrBuilder();
        } else {
          if (messageCase_ == 4) {
            return (gateway_service.Envelope.RFCRequestMessage) message_;
          }
          return gateway_service.Envelope.RFCRequestMessage.getDefaultInstance();
        }
      }
      /**
       * <code>.gateway_service.RFCRequestMessage rfc_message = 4;</code>
       */
      private com.google.protobuf.SingleFieldBuilder<
          gateway_service.Envelope.RFCRequestMessage, gateway_service.Envelope.RFCRequestMessage.Builder, gateway_service.Envelope.RFCRequestMessageOrBuilder> 
          getRfcMessageFieldBuilder() {
        if (rfcMessageBuilder_ == null) {
          if (!(messageCase_ == 4)) {
            message_ = gateway_service.Envelope.RFCRequestMessage.getDefaultInstance();
          }
          rfcMessageBuilder_ = new com.google.protobuf.SingleFieldBuilder<
              gateway_service.Envelope.RFCRequestMessage, gateway_service.Envelope.RFCRequestMessage.Builder, gateway_service.Envelope.RFCRequestMessageOrBuilder>(
                  (gateway_service.Envelope.RFCRequestMessage) message_,
                  getParentForChildren(),
                  isClean());
          message_ = null;
        }
        messageCase_ = 4;
        onChanged();
        return rfcMessageBuilder_;
      }

      private long creationTime_ ;
      /**
       * <pre>
       * The message creation time in UTC seconds from UNIX epoch
       * </pre>
       *
       * <code>optional uint64 creation_time = 5;</code>
       * @return Whether the creationTime field is set.
       */
      @java.lang.Override
      public boolean hasCreationTime() {
        return ((bitField0_ & 0x00000010) != 0);
      }
      /**
       * <pre>
       * The message creation time in UTC seconds from UNIX epoch
       * </pre>
       *
       * <code>optional uint64 creation_time = 5;</code>
       * @return The creationTime.
       */
      @java.lang.Override
      public long getCreationTime() {
        return creationTime_;
      }
      /**
       * <pre>
       * The message creation time in UTC seconds from UNIX epoch
       * </pre>
       *
       * <code>optional uint64 creation_time = 5;</code>
       * @param value The creationTime to set.
       * @return This builder for chaining.
       */
      public Builder setCreationTime(long value) {

        creationTime_ = value;
        bitField0_ |= 0x00000010;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * The message creation time in UTC seconds from UNIX epoch
       * </pre>
       *
       * <code>optional uint64 creation_time = 5;</code>
       * @return This builder for chaining.
       */
      public Builder clearCreationTime() {
        bitField0_ = (bitField0_ & ~0x00000010);
        creationTime_ = 0L;
        onChanged();
        return this;
      }

      private int timeToLive_ ;
      /**
       * <pre>
       * Time to live in seconds from creation time
       * If request is received after the time to live has expired then the request will not be processed
       * and the the response will be ENUM_OPERATION_STATUS_REQUEST_EXPIRED
       * </pre>
       *
       * <code>optional uint32 time_to_live = 6;</code>
       * @return Whether the timeToLive field is set.
       */
      @java.lang.Override
      public boolean hasTimeToLive() {
        return ((bitField0_ & 0x00000020) != 0);
      }
      /**
       * <pre>
       * Time to live in seconds from creation time
       * If request is received after the time to live has expired then the request will not be processed
       * and the the response will be ENUM_OPERATION_STATUS_REQUEST_EXPIRED
       * </pre>
       *
       * <code>optional uint32 time_to_live = 6;</code>
       * @return The timeToLive.
       */
      @java.lang.Override
      public int getTimeToLive() {
        return timeToLive_;
      }
      /**
       * <pre>
       * Time to live in seconds from creation time
       * If request is received after the time to live has expired then the request will not be processed
       * and the the response will be ENUM_OPERATION_STATUS_REQUEST_EXPIRED
       * </pre>
       *
       * <code>optional uint32 time_to_live = 6;</code>
       * @param value The timeToLive to set.
       * @return This builder for chaining.
       */
      public Builder setTimeToLive(int value) {

        timeToLive_ = value;
        bitField0_ |= 0x00000020;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * Time to live in seconds from creation time
       * If request is received after the time to live has expired then the request will not be processed
       * and the the response will be ENUM_OPERATION_STATUS_REQUEST_EXPIRED
       * </pre>
       *
       * <code>optional uint32 time_to_live = 6;</code>
       * @return This builder for chaining.
       */
      public Builder clearTimeToLive() {
        bitField0_ = (bitField0_ & ~0x00000020);
        timeToLive_ = 0;
        onChanged();
        return this;
      }

      private int sequenceNo_ ;
      /**
       * <pre>
       * Sequence number increments with every request
       * If a sequence number is received that is less than or equal to the last sequence number received then the request
       * will not be processed and the response will be NUM_OPERATION_STATUS_REQUEST_OUT_OF_SEQUENCE
       * Sequence number will start at 0 whenever the service gateway is restarted and will be fast-forwarded to the value of
       * the first request when it is received
       * </pre>
       *
       * <code>optional uint32 sequence_no = 7;</code>
       * @return Whether the sequenceNo field is set.
       */
      @java.lang.Override
      public boolean hasSequenceNo() {
        return ((bitField0_ & 0x00000040) != 0);
      }
      /**
       * <pre>
       * Sequence number increments with every request
       * If a sequence number is received that is less than or equal to the last sequence number received then the request
       * will not be processed and the response will be NUM_OPERATION_STATUS_REQUEST_OUT_OF_SEQUENCE
       * Sequence number will start at 0 whenever the service gateway is restarted and will be fast-forwarded to the value of
       * the first request when it is received
       * </pre>
       *
       * <code>optional uint32 sequence_no = 7;</code>
       * @return The sequenceNo.
       */
      @java.lang.Override
      public int getSequenceNo() {
        return sequenceNo_;
      }
      /**
       * <pre>
       * Sequence number increments with every request
       * If a sequence number is received that is less than or equal to the last sequence number received then the request
       * will not be processed and the response will be NUM_OPERATION_STATUS_REQUEST_OUT_OF_SEQUENCE
       * Sequence number will start at 0 whenever the service gateway is restarted and will be fast-forwarded to the value of
       * the first request when it is received
       * </pre>
       *
       * <code>optional uint32 sequence_no = 7;</code>
       * @param value The sequenceNo to set.
       * @return This builder for chaining.
       */
      public Builder setSequenceNo(int value) {

        sequenceNo_ = value;
        bitField0_ |= 0x00000040;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * Sequence number increments with every request
       * If a sequence number is received that is less than or equal to the last sequence number received then the request
       * will not be processed and the response will be NUM_OPERATION_STATUS_REQUEST_OUT_OF_SEQUENCE
       * Sequence number will start at 0 whenever the service gateway is restarted and will be fast-forwarded to the value of
       * the first request when it is received
       * </pre>
       *
       * <code>optional uint32 sequence_no = 7;</code>
       * @return This builder for chaining.
       */
      public Builder clearSequenceNo() {
        bitField0_ = (bitField0_ & ~0x00000040);
        sequenceNo_ = 0;
        onChanged();
        return this;
      }

      private java.lang.Object vuid_ = "";
      /**
       * <pre>
       * Vehicle unique id, used to ensure that the request is for this vehicle and this vehicle alone
       * Will be the unique id that is provided to OBG during OBG's authentication process
       * If the vuid is not correct then the request will not be processed and the response will be ENUM_OPERATION_STATUS_INVALID_VUID
       * </pre>
       *
       * <code>optional string vuid = 8;</code>
       * @return Whether the vuid field is set.
       */
      public boolean hasVuid() {
        return ((bitField0_ & 0x00000080) != 0);
      }
      /**
       * <pre>
       * Vehicle unique id, used to ensure that the request is for this vehicle and this vehicle alone
       * Will be the unique id that is provided to OBG during OBG's authentication process
       * If the vuid is not correct then the request will not be processed and the response will be ENUM_OPERATION_STATUS_INVALID_VUID
       * </pre>
       *
       * <code>optional string vuid = 8;</code>
       * @return The vuid.
       */
      public java.lang.String getVuid() {
        java.lang.Object ref = vuid_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          vuid_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       * Vehicle unique id, used to ensure that the request is for this vehicle and this vehicle alone
       * Will be the unique id that is provided to OBG during OBG's authentication process
       * If the vuid is not correct then the request will not be processed and the response will be ENUM_OPERATION_STATUS_INVALID_VUID
       * </pre>
       *
       * <code>optional string vuid = 8;</code>
       * @return The bytes for vuid.
       */
      public com.google.protobuf.ByteString
          getVuidBytes() {
        java.lang.Object ref = vuid_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          vuid_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       * Vehicle unique id, used to ensure that the request is for this vehicle and this vehicle alone
       * Will be the unique id that is provided to OBG during OBG's authentication process
       * If the vuid is not correct then the request will not be processed and the response will be ENUM_OPERATION_STATUS_INVALID_VUID
       * </pre>
       *
       * <code>optional string vuid = 8;</code>
       * @param value The vuid to set.
       * @return This builder for chaining.
       */
      public Builder setVuid(
          java.lang.String value) {
        if (value == null) { throw new NullPointerException(); }
        vuid_ = value;
        bitField0_ |= 0x00000080;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * Vehicle unique id, used to ensure that the request is for this vehicle and this vehicle alone
       * Will be the unique id that is provided to OBG during OBG's authentication process
       * If the vuid is not correct then the request will not be processed and the response will be ENUM_OPERATION_STATUS_INVALID_VUID
       * </pre>
       *
       * <code>optional string vuid = 8;</code>
       * @return This builder for chaining.
       */
      public Builder clearVuid() {
        vuid_ = getDefaultInstance().getVuid();
        bitField0_ = (bitField0_ & ~0x00000080);
        onChanged();
        return this;
      }
      /**
       * <pre>
       * Vehicle unique id, used to ensure that the request is for this vehicle and this vehicle alone
       * Will be the unique id that is provided to OBG during OBG's authentication process
       * If the vuid is not correct then the request will not be processed and the response will be ENUM_OPERATION_STATUS_INVALID_VUID
       * </pre>
       *
       * <code>optional string vuid = 8;</code>
       * @param value The bytes for vuid to set.
       * @return This builder for chaining.
       */
      public Builder setVuidBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        checkByteStringIsUtf8(value);
        vuid_ = value;
        bitField0_ |= 0x00000080;
        onChanged();
        return this;
      }

      // @@protoc_insertion_point(builder_scope:gateway_service.GatewayRequestMessage)
    }

    // @@protoc_insertion_point(class_scope:gateway_service.GatewayRequestMessage)
    private static final gateway_service.Envelope.GatewayRequestMessage DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new gateway_service.Envelope.GatewayRequestMessage();
    }

    public static gateway_service.Envelope.GatewayRequestMessage getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<GatewayRequestMessage>
        PARSER = new com.google.protobuf.AbstractParser<GatewayRequestMessage>() {
      @java.lang.Override
      public GatewayRequestMessage parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<GatewayRequestMessage> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<GatewayRequestMessage> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public gateway_service.Envelope.GatewayRequestMessage getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface GatewayResponseMessageOrBuilder extends
      // @@protoc_insertion_point(interface_extends:gateway_service.GatewayResponseMessage)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>.gateway_service.SubscribeMessageResponse subscribe_response = 1;</code>
     * @return Whether the subscribeResponse field is set.
     */
    boolean hasSubscribeResponse();
    /**
     * <code>.gateway_service.SubscribeMessageResponse subscribe_response = 1;</code>
     * @return The subscribeResponse.
     */
    gateway_service.Envelope.SubscribeMessageResponse getSubscribeResponse();
    /**
     * <code>.gateway_service.SubscribeMessageResponse subscribe_response = 1;</code>
     */
    gateway_service.Envelope.SubscribeMessageResponseOrBuilder getSubscribeResponseOrBuilder();

    /**
     * <code>.gateway_service.UnsubscribeMessageResponse unsubscribe_response = 2;</code>
     * @return Whether the unsubscribeResponse field is set.
     */
    boolean hasUnsubscribeResponse();
    /**
     * <code>.gateway_service.UnsubscribeMessageResponse unsubscribe_response = 2;</code>
     * @return The unsubscribeResponse.
     */
    gateway_service.Envelope.UnsubscribeMessageResponse getUnsubscribeResponse();
    /**
     * <code>.gateway_service.UnsubscribeMessageResponse unsubscribe_response = 2;</code>
     */
    gateway_service.Envelope.UnsubscribeMessageResponseOrBuilder getUnsubscribeResponseOrBuilder();

    /**
     * <code>.gateway_service.ResponseMessage response_message = 3;</code>
     * @return Whether the responseMessage field is set.
     */
    boolean hasResponseMessage();
    /**
     * <code>.gateway_service.ResponseMessage response_message = 3;</code>
     * @return The responseMessage.
     */
    gateway_service.Envelope.ResponseMessage getResponseMessage();
    /**
     * <code>.gateway_service.ResponseMessage response_message = 3;</code>
     */
    gateway_service.Envelope.ResponseMessageOrBuilder getResponseMessageOrBuilder();

    /**
     * <code>.gateway_service.EventMessage event_message = 4;</code>
     * @return Whether the eventMessage field is set.
     */
    boolean hasEventMessage();
    /**
     * <code>.gateway_service.EventMessage event_message = 4;</code>
     * @return The eventMessage.
     */
    gateway_service.Envelope.EventMessage getEventMessage();
    /**
     * <code>.gateway_service.EventMessage event_message = 4;</code>
     */
    gateway_service.Envelope.EventMessageOrBuilder getEventMessageOrBuilder();

    /**
     * <code>.gateway_service.RFCResponseMessage rfc_response = 5;</code>
     * @return Whether the rfcResponse field is set.
     */
    boolean hasRfcResponse();
    /**
     * <code>.gateway_service.RFCResponseMessage rfc_response = 5;</code>
     * @return The rfcResponse.
     */
    gateway_service.Envelope.RFCResponseMessage getRfcResponse();
    /**
     * <code>.gateway_service.RFCResponseMessage rfc_response = 5;</code>
     */
    gateway_service.Envelope.RFCResponseMessageOrBuilder getRfcResponseOrBuilder();

    /**
     * <pre>
     * Time in ms from UNIX epoch
     * </pre>
     *
     * <code>optional uint64 timestamp_ms = 6;</code>
     * @return Whether the timestampMs field is set.
     */
    boolean hasTimestampMs();
    /**
     * <pre>
     * Time in ms from UNIX epoch
     * </pre>
     *
     * <code>optional uint64 timestamp_ms = 6;</code>
     * @return The timestampMs.
     */
    long getTimestampMs();

    gateway_service.Envelope.GatewayResponseMessage.MessageCase getMessageCase();
  }
  /**
   * <pre>
   * Response messages sent to the cloud backend in response to one of the requests
   * </pre>
   *
   * Protobuf type {@code gateway_service.GatewayResponseMessage}
   */
  public static final class GatewayResponseMessage extends
      com.google.protobuf.GeneratedMessage implements
      // @@protoc_insertion_point(message_implements:gateway_service.GatewayResponseMessage)
      GatewayResponseMessageOrBuilder {
  private static final long serialVersionUID = 0L;
    static {
      com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
        com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
        /* major= */ 4,
        /* minor= */ 28,
        /* patch= */ 2,
        /* suffix= */ "",
        GatewayResponseMessage.class.getName());
    }
    // Use GatewayResponseMessage.newBuilder() to construct.
    private GatewayResponseMessage(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
    }
    private GatewayResponseMessage() {
    }

    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return gateway_service.Envelope.internal_static_gateway_service_GatewayResponseMessage_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return gateway_service.Envelope.internal_static_gateway_service_GatewayResponseMessage_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              gateway_service.Envelope.GatewayResponseMessage.class, gateway_service.Envelope.GatewayResponseMessage.Builder.class);
    }

    private int bitField0_;
    private int messageCase_ = 0;
    @SuppressWarnings("serial")
    private java.lang.Object message_;
    public enum MessageCase
        implements com.google.protobuf.Internal.EnumLite,
            com.google.protobuf.AbstractMessage.InternalOneOfEnum {
      SUBSCRIBE_RESPONSE(1),
      UNSUBSCRIBE_RESPONSE(2),
      RESPONSE_MESSAGE(3),
      EVENT_MESSAGE(4),
      RFC_RESPONSE(5),
      MESSAGE_NOT_SET(0);
      private final int value;
      private MessageCase(int value) {
        this.value = value;
      }
      /**
       * @param value The number of the enum to look for.
       * @return The enum associated with the given number.
       * @deprecated Use {@link #forNumber(int)} instead.
       */
      @java.lang.Deprecated
      public static MessageCase valueOf(int value) {
        return forNumber(value);
      }

      public static MessageCase forNumber(int value) {
        switch (value) {
          case 1: return SUBSCRIBE_RESPONSE;
          case 2: return UNSUBSCRIBE_RESPONSE;
          case 3: return RESPONSE_MESSAGE;
          case 4: return EVENT_MESSAGE;
          case 5: return RFC_RESPONSE;
          case 0: return MESSAGE_NOT_SET;
          default: return null;
        }
      }
      public int getNumber() {
        return this.value;
      }
    };

    public MessageCase
    getMessageCase() {
      return MessageCase.forNumber(
          messageCase_);
    }

    public static final int SUBSCRIBE_RESPONSE_FIELD_NUMBER = 1;
    /**
     * <code>.gateway_service.SubscribeMessageResponse subscribe_response = 1;</code>
     * @return Whether the subscribeResponse field is set.
     */
    @java.lang.Override
    public boolean hasSubscribeResponse() {
      return messageCase_ == 1;
    }
    /**
     * <code>.gateway_service.SubscribeMessageResponse subscribe_response = 1;</code>
     * @return The subscribeResponse.
     */
    @java.lang.Override
    public gateway_service.Envelope.SubscribeMessageResponse getSubscribeResponse() {
      if (messageCase_ == 1) {
         return (gateway_service.Envelope.SubscribeMessageResponse) message_;
      }
      return gateway_service.Envelope.SubscribeMessageResponse.getDefaultInstance();
    }
    /**
     * <code>.gateway_service.SubscribeMessageResponse subscribe_response = 1;</code>
     */
    @java.lang.Override
    public gateway_service.Envelope.SubscribeMessageResponseOrBuilder getSubscribeResponseOrBuilder() {
      if (messageCase_ == 1) {
         return (gateway_service.Envelope.SubscribeMessageResponse) message_;
      }
      return gateway_service.Envelope.SubscribeMessageResponse.getDefaultInstance();
    }

    public static final int UNSUBSCRIBE_RESPONSE_FIELD_NUMBER = 2;
    /**
     * <code>.gateway_service.UnsubscribeMessageResponse unsubscribe_response = 2;</code>
     * @return Whether the unsubscribeResponse field is set.
     */
    @java.lang.Override
    public boolean hasUnsubscribeResponse() {
      return messageCase_ == 2;
    }
    /**
     * <code>.gateway_service.UnsubscribeMessageResponse unsubscribe_response = 2;</code>
     * @return The unsubscribeResponse.
     */
    @java.lang.Override
    public gateway_service.Envelope.UnsubscribeMessageResponse getUnsubscribeResponse() {
      if (messageCase_ == 2) {
         return (gateway_service.Envelope.UnsubscribeMessageResponse) message_;
      }
      return gateway_service.Envelope.UnsubscribeMessageResponse.getDefaultInstance();
    }
    /**
     * <code>.gateway_service.UnsubscribeMessageResponse unsubscribe_response = 2;</code>
     */
    @java.lang.Override
    public gateway_service.Envelope.UnsubscribeMessageResponseOrBuilder getUnsubscribeResponseOrBuilder() {
      if (messageCase_ == 2) {
         return (gateway_service.Envelope.UnsubscribeMessageResponse) message_;
      }
      return gateway_service.Envelope.UnsubscribeMessageResponse.getDefaultInstance();
    }

    public static final int RESPONSE_MESSAGE_FIELD_NUMBER = 3;
    /**
     * <code>.gateway_service.ResponseMessage response_message = 3;</code>
     * @return Whether the responseMessage field is set.
     */
    @java.lang.Override
    public boolean hasResponseMessage() {
      return messageCase_ == 3;
    }
    /**
     * <code>.gateway_service.ResponseMessage response_message = 3;</code>
     * @return The responseMessage.
     */
    @java.lang.Override
    public gateway_service.Envelope.ResponseMessage getResponseMessage() {
      if (messageCase_ == 3) {
         return (gateway_service.Envelope.ResponseMessage) message_;
      }
      return gateway_service.Envelope.ResponseMessage.getDefaultInstance();
    }
    /**
     * <code>.gateway_service.ResponseMessage response_message = 3;</code>
     */
    @java.lang.Override
    public gateway_service.Envelope.ResponseMessageOrBuilder getResponseMessageOrBuilder() {
      if (messageCase_ == 3) {
         return (gateway_service.Envelope.ResponseMessage) message_;
      }
      return gateway_service.Envelope.ResponseMessage.getDefaultInstance();
    }

    public static final int EVENT_MESSAGE_FIELD_NUMBER = 4;
    /**
     * <code>.gateway_service.EventMessage event_message = 4;</code>
     * @return Whether the eventMessage field is set.
     */
    @java.lang.Override
    public boolean hasEventMessage() {
      return messageCase_ == 4;
    }
    /**
     * <code>.gateway_service.EventMessage event_message = 4;</code>
     * @return The eventMessage.
     */
    @java.lang.Override
    public gateway_service.Envelope.EventMessage getEventMessage() {
      if (messageCase_ == 4) {
         return (gateway_service.Envelope.EventMessage) message_;
      }
      return gateway_service.Envelope.EventMessage.getDefaultInstance();
    }
    /**
     * <code>.gateway_service.EventMessage event_message = 4;</code>
     */
    @java.lang.Override
    public gateway_service.Envelope.EventMessageOrBuilder getEventMessageOrBuilder() {
      if (messageCase_ == 4) {
         return (gateway_service.Envelope.EventMessage) message_;
      }
      return gateway_service.Envelope.EventMessage.getDefaultInstance();
    }

    public static final int RFC_RESPONSE_FIELD_NUMBER = 5;
    /**
     * <code>.gateway_service.RFCResponseMessage rfc_response = 5;</code>
     * @return Whether the rfcResponse field is set.
     */
    @java.lang.Override
    public boolean hasRfcResponse() {
      return messageCase_ == 5;
    }
    /**
     * <code>.gateway_service.RFCResponseMessage rfc_response = 5;</code>
     * @return The rfcResponse.
     */
    @java.lang.Override
    public gateway_service.Envelope.RFCResponseMessage getRfcResponse() {
      if (messageCase_ == 5) {
         return (gateway_service.Envelope.RFCResponseMessage) message_;
      }
      return gateway_service.Envelope.RFCResponseMessage.getDefaultInstance();
    }
    /**
     * <code>.gateway_service.RFCResponseMessage rfc_response = 5;</code>
     */
    @java.lang.Override
    public gateway_service.Envelope.RFCResponseMessageOrBuilder getRfcResponseOrBuilder() {
      if (messageCase_ == 5) {
         return (gateway_service.Envelope.RFCResponseMessage) message_;
      }
      return gateway_service.Envelope.RFCResponseMessage.getDefaultInstance();
    }

    public static final int TIMESTAMP_MS_FIELD_NUMBER = 6;
    private long timestampMs_ = 0L;
    /**
     * <pre>
     * Time in ms from UNIX epoch
     * </pre>
     *
     * <code>optional uint64 timestamp_ms = 6;</code>
     * @return Whether the timestampMs field is set.
     */
    @java.lang.Override
    public boolean hasTimestampMs() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <pre>
     * Time in ms from UNIX epoch
     * </pre>
     *
     * <code>optional uint64 timestamp_ms = 6;</code>
     * @return The timestampMs.
     */
    @java.lang.Override
    public long getTimestampMs() {
      return timestampMs_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (messageCase_ == 1) {
        output.writeMessage(1, (gateway_service.Envelope.SubscribeMessageResponse) message_);
      }
      if (messageCase_ == 2) {
        output.writeMessage(2, (gateway_service.Envelope.UnsubscribeMessageResponse) message_);
      }
      if (messageCase_ == 3) {
        output.writeMessage(3, (gateway_service.Envelope.ResponseMessage) message_);
      }
      if (messageCase_ == 4) {
        output.writeMessage(4, (gateway_service.Envelope.EventMessage) message_);
      }
      if (messageCase_ == 5) {
        output.writeMessage(5, (gateway_service.Envelope.RFCResponseMessage) message_);
      }
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeUInt64(6, timestampMs_);
      }
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (messageCase_ == 1) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(1, (gateway_service.Envelope.SubscribeMessageResponse) message_);
      }
      if (messageCase_ == 2) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(2, (gateway_service.Envelope.UnsubscribeMessageResponse) message_);
      }
      if (messageCase_ == 3) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(3, (gateway_service.Envelope.ResponseMessage) message_);
      }
      if (messageCase_ == 4) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(4, (gateway_service.Envelope.EventMessage) message_);
      }
      if (messageCase_ == 5) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(5, (gateway_service.Envelope.RFCResponseMessage) message_);
      }
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt64Size(6, timestampMs_);
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof gateway_service.Envelope.GatewayResponseMessage)) {
        return super.equals(obj);
      }
      gateway_service.Envelope.GatewayResponseMessage other = (gateway_service.Envelope.GatewayResponseMessage) obj;

      if (hasTimestampMs() != other.hasTimestampMs()) return false;
      if (hasTimestampMs()) {
        if (getTimestampMs()
            != other.getTimestampMs()) return false;
      }
      if (!getMessageCase().equals(other.getMessageCase())) return false;
      switch (messageCase_) {
        case 1:
          if (!getSubscribeResponse()
              .equals(other.getSubscribeResponse())) return false;
          break;
        case 2:
          if (!getUnsubscribeResponse()
              .equals(other.getUnsubscribeResponse())) return false;
          break;
        case 3:
          if (!getResponseMessage()
              .equals(other.getResponseMessage())) return false;
          break;
        case 4:
          if (!getEventMessage()
              .equals(other.getEventMessage())) return false;
          break;
        case 5:
          if (!getRfcResponse()
              .equals(other.getRfcResponse())) return false;
          break;
        case 0:
        default:
      }
      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasTimestampMs()) {
        hash = (37 * hash) + TIMESTAMP_MS_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getTimestampMs());
      }
      switch (messageCase_) {
        case 1:
          hash = (37 * hash) + SUBSCRIBE_RESPONSE_FIELD_NUMBER;
          hash = (53 * hash) + getSubscribeResponse().hashCode();
          break;
        case 2:
          hash = (37 * hash) + UNSUBSCRIBE_RESPONSE_FIELD_NUMBER;
          hash = (53 * hash) + getUnsubscribeResponse().hashCode();
          break;
        case 3:
          hash = (37 * hash) + RESPONSE_MESSAGE_FIELD_NUMBER;
          hash = (53 * hash) + getResponseMessage().hashCode();
          break;
        case 4:
          hash = (37 * hash) + EVENT_MESSAGE_FIELD_NUMBER;
          hash = (53 * hash) + getEventMessage().hashCode();
          break;
        case 5:
          hash = (37 * hash) + RFC_RESPONSE_FIELD_NUMBER;
          hash = (53 * hash) + getRfcResponse().hashCode();
          break;
        case 0:
        default:
      }
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static gateway_service.Envelope.GatewayResponseMessage parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static gateway_service.Envelope.GatewayResponseMessage parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static gateway_service.Envelope.GatewayResponseMessage parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static gateway_service.Envelope.GatewayResponseMessage parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static gateway_service.Envelope.GatewayResponseMessage parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static gateway_service.Envelope.GatewayResponseMessage parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static gateway_service.Envelope.GatewayResponseMessage parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input);
    }
    public static gateway_service.Envelope.GatewayResponseMessage parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public static gateway_service.Envelope.GatewayResponseMessage parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseDelimitedWithIOException(PARSER, input);
    }

    public static gateway_service.Envelope.GatewayResponseMessage parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static gateway_service.Envelope.GatewayResponseMessage parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input);
    }
    public static gateway_service.Envelope.GatewayResponseMessage parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(gateway_service.Envelope.GatewayResponseMessage prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     * Response messages sent to the cloud backend in response to one of the requests
     * </pre>
     *
     * Protobuf type {@code gateway_service.GatewayResponseMessage}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:gateway_service.GatewayResponseMessage)
        gateway_service.Envelope.GatewayResponseMessageOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return gateway_service.Envelope.internal_static_gateway_service_GatewayResponseMessage_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return gateway_service.Envelope.internal_static_gateway_service_GatewayResponseMessage_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                gateway_service.Envelope.GatewayResponseMessage.class, gateway_service.Envelope.GatewayResponseMessage.Builder.class);
      }

      // Construct using gateway_service.Envelope.GatewayResponseMessage.newBuilder()
      private Builder() {

      }

      private Builder(
          com.google.protobuf.GeneratedMessage.BuilderParent parent) {
        super(parent);

      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        bitField0_ = 0;
        if (subscribeResponseBuilder_ != null) {
          subscribeResponseBuilder_.clear();
        }
        if (unsubscribeResponseBuilder_ != null) {
          unsubscribeResponseBuilder_.clear();
        }
        if (responseMessageBuilder_ != null) {
          responseMessageBuilder_.clear();
        }
        if (eventMessageBuilder_ != null) {
          eventMessageBuilder_.clear();
        }
        if (rfcResponseBuilder_ != null) {
          rfcResponseBuilder_.clear();
        }
        timestampMs_ = 0L;
        messageCase_ = 0;
        message_ = null;
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return gateway_service.Envelope.internal_static_gateway_service_GatewayResponseMessage_descriptor;
      }

      @java.lang.Override
      public gateway_service.Envelope.GatewayResponseMessage getDefaultInstanceForType() {
        return gateway_service.Envelope.GatewayResponseMessage.getDefaultInstance();
      }

      @java.lang.Override
      public gateway_service.Envelope.GatewayResponseMessage build() {
        gateway_service.Envelope.GatewayResponseMessage result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public gateway_service.Envelope.GatewayResponseMessage buildPartial() {
        gateway_service.Envelope.GatewayResponseMessage result = new gateway_service.Envelope.GatewayResponseMessage(this);
        if (bitField0_ != 0) { buildPartial0(result); }
        buildPartialOneofs(result);
        onBuilt();
        return result;
      }

      private void buildPartial0(gateway_service.Envelope.GatewayResponseMessage result) {
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000020) != 0)) {
          result.timestampMs_ = timestampMs_;
          to_bitField0_ |= 0x00000001;
        }
        result.bitField0_ |= to_bitField0_;
      }

      private void buildPartialOneofs(gateway_service.Envelope.GatewayResponseMessage result) {
        result.messageCase_ = messageCase_;
        result.message_ = this.message_;
        if (messageCase_ == 1 &&
            subscribeResponseBuilder_ != null) {
          result.message_ = subscribeResponseBuilder_.build();
        }
        if (messageCase_ == 2 &&
            unsubscribeResponseBuilder_ != null) {
          result.message_ = unsubscribeResponseBuilder_.build();
        }
        if (messageCase_ == 3 &&
            responseMessageBuilder_ != null) {
          result.message_ = responseMessageBuilder_.build();
        }
        if (messageCase_ == 4 &&
            eventMessageBuilder_ != null) {
          result.message_ = eventMessageBuilder_.build();
        }
        if (messageCase_ == 5 &&
            rfcResponseBuilder_ != null) {
          result.message_ = rfcResponseBuilder_.build();
        }
      }

      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof gateway_service.Envelope.GatewayResponseMessage) {
          return mergeFrom((gateway_service.Envelope.GatewayResponseMessage)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(gateway_service.Envelope.GatewayResponseMessage other) {
        if (other == gateway_service.Envelope.GatewayResponseMessage.getDefaultInstance()) return this;
        if (other.hasTimestampMs()) {
          setTimestampMs(other.getTimestampMs());
        }
        switch (other.getMessageCase()) {
          case SUBSCRIBE_RESPONSE: {
            mergeSubscribeResponse(other.getSubscribeResponse());
            break;
          }
          case UNSUBSCRIBE_RESPONSE: {
            mergeUnsubscribeResponse(other.getUnsubscribeResponse());
            break;
          }
          case RESPONSE_MESSAGE: {
            mergeResponseMessage(other.getResponseMessage());
            break;
          }
          case EVENT_MESSAGE: {
            mergeEventMessage(other.getEventMessage());
            break;
          }
          case RFC_RESPONSE: {
            mergeRfcResponse(other.getRfcResponse());
            break;
          }
          case MESSAGE_NOT_SET: {
            break;
          }
        }
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 10: {
                input.readMessage(
                    getSubscribeResponseFieldBuilder().getBuilder(),
                    extensionRegistry);
                messageCase_ = 1;
                break;
              } // case 10
              case 18: {
                input.readMessage(
                    getUnsubscribeResponseFieldBuilder().getBuilder(),
                    extensionRegistry);
                messageCase_ = 2;
                break;
              } // case 18
              case 26: {
                input.readMessage(
                    getResponseMessageFieldBuilder().getBuilder(),
                    extensionRegistry);
                messageCase_ = 3;
                break;
              } // case 26
              case 34: {
                input.readMessage(
                    getEventMessageFieldBuilder().getBuilder(),
                    extensionRegistry);
                messageCase_ = 4;
                break;
              } // case 34
              case 42: {
                input.readMessage(
                    getRfcResponseFieldBuilder().getBuilder(),
                    extensionRegistry);
                messageCase_ = 5;
                break;
              } // case 42
              case 48: {
                timestampMs_ = input.readUInt64();
                bitField0_ |= 0x00000020;
                break;
              } // case 48
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }
      private int messageCase_ = 0;
      private java.lang.Object message_;
      public MessageCase
          getMessageCase() {
        return MessageCase.forNumber(
            messageCase_);
      }

      public Builder clearMessage() {
        messageCase_ = 0;
        message_ = null;
        onChanged();
        return this;
      }

      private int bitField0_;

      private com.google.protobuf.SingleFieldBuilder<
          gateway_service.Envelope.SubscribeMessageResponse, gateway_service.Envelope.SubscribeMessageResponse.Builder, gateway_service.Envelope.SubscribeMessageResponseOrBuilder> subscribeResponseBuilder_;
      /**
       * <code>.gateway_service.SubscribeMessageResponse subscribe_response = 1;</code>
       * @return Whether the subscribeResponse field is set.
       */
      @java.lang.Override
      public boolean hasSubscribeResponse() {
        return messageCase_ == 1;
      }
      /**
       * <code>.gateway_service.SubscribeMessageResponse subscribe_response = 1;</code>
       * @return The subscribeResponse.
       */
      @java.lang.Override
      public gateway_service.Envelope.SubscribeMessageResponse getSubscribeResponse() {
        if (subscribeResponseBuilder_ == null) {
          if (messageCase_ == 1) {
            return (gateway_service.Envelope.SubscribeMessageResponse) message_;
          }
          return gateway_service.Envelope.SubscribeMessageResponse.getDefaultInstance();
        } else {
          if (messageCase_ == 1) {
            return subscribeResponseBuilder_.getMessage();
          }
          return gateway_service.Envelope.SubscribeMessageResponse.getDefaultInstance();
        }
      }
      /**
       * <code>.gateway_service.SubscribeMessageResponse subscribe_response = 1;</code>
       */
      public Builder setSubscribeResponse(gateway_service.Envelope.SubscribeMessageResponse value) {
        if (subscribeResponseBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          message_ = value;
          onChanged();
        } else {
          subscribeResponseBuilder_.setMessage(value);
        }
        messageCase_ = 1;
        return this;
      }
      /**
       * <code>.gateway_service.SubscribeMessageResponse subscribe_response = 1;</code>
       */
      public Builder setSubscribeResponse(
          gateway_service.Envelope.SubscribeMessageResponse.Builder builderForValue) {
        if (subscribeResponseBuilder_ == null) {
          message_ = builderForValue.build();
          onChanged();
        } else {
          subscribeResponseBuilder_.setMessage(builderForValue.build());
        }
        messageCase_ = 1;
        return this;
      }
      /**
       * <code>.gateway_service.SubscribeMessageResponse subscribe_response = 1;</code>
       */
      public Builder mergeSubscribeResponse(gateway_service.Envelope.SubscribeMessageResponse value) {
        if (subscribeResponseBuilder_ == null) {
          if (messageCase_ == 1 &&
              message_ != gateway_service.Envelope.SubscribeMessageResponse.getDefaultInstance()) {
            message_ = gateway_service.Envelope.SubscribeMessageResponse.newBuilder((gateway_service.Envelope.SubscribeMessageResponse) message_)
                .mergeFrom(value).buildPartial();
          } else {
            message_ = value;
          }
          onChanged();
        } else {
          if (messageCase_ == 1) {
            subscribeResponseBuilder_.mergeFrom(value);
          } else {
            subscribeResponseBuilder_.setMessage(value);
          }
        }
        messageCase_ = 1;
        return this;
      }
      /**
       * <code>.gateway_service.SubscribeMessageResponse subscribe_response = 1;</code>
       */
      public Builder clearSubscribeResponse() {
        if (subscribeResponseBuilder_ == null) {
          if (messageCase_ == 1) {
            messageCase_ = 0;
            message_ = null;
            onChanged();
          }
        } else {
          if (messageCase_ == 1) {
            messageCase_ = 0;
            message_ = null;
          }
          subscribeResponseBuilder_.clear();
        }
        return this;
      }
      /**
       * <code>.gateway_service.SubscribeMessageResponse subscribe_response = 1;</code>
       */
      public gateway_service.Envelope.SubscribeMessageResponse.Builder getSubscribeResponseBuilder() {
        return getSubscribeResponseFieldBuilder().getBuilder();
      }
      /**
       * <code>.gateway_service.SubscribeMessageResponse subscribe_response = 1;</code>
       */
      @java.lang.Override
      public gateway_service.Envelope.SubscribeMessageResponseOrBuilder getSubscribeResponseOrBuilder() {
        if ((messageCase_ == 1) && (subscribeResponseBuilder_ != null)) {
          return subscribeResponseBuilder_.getMessageOrBuilder();
        } else {
          if (messageCase_ == 1) {
            return (gateway_service.Envelope.SubscribeMessageResponse) message_;
          }
          return gateway_service.Envelope.SubscribeMessageResponse.getDefaultInstance();
        }
      }
      /**
       * <code>.gateway_service.SubscribeMessageResponse subscribe_response = 1;</code>
       */
      private com.google.protobuf.SingleFieldBuilder<
          gateway_service.Envelope.SubscribeMessageResponse, gateway_service.Envelope.SubscribeMessageResponse.Builder, gateway_service.Envelope.SubscribeMessageResponseOrBuilder> 
          getSubscribeResponseFieldBuilder() {
        if (subscribeResponseBuilder_ == null) {
          if (!(messageCase_ == 1)) {
            message_ = gateway_service.Envelope.SubscribeMessageResponse.getDefaultInstance();
          }
          subscribeResponseBuilder_ = new com.google.protobuf.SingleFieldBuilder<
              gateway_service.Envelope.SubscribeMessageResponse, gateway_service.Envelope.SubscribeMessageResponse.Builder, gateway_service.Envelope.SubscribeMessageResponseOrBuilder>(
                  (gateway_service.Envelope.SubscribeMessageResponse) message_,
                  getParentForChildren(),
                  isClean());
          message_ = null;
        }
        messageCase_ = 1;
        onChanged();
        return subscribeResponseBuilder_;
      }

      private com.google.protobuf.SingleFieldBuilder<
          gateway_service.Envelope.UnsubscribeMessageResponse, gateway_service.Envelope.UnsubscribeMessageResponse.Builder, gateway_service.Envelope.UnsubscribeMessageResponseOrBuilder> unsubscribeResponseBuilder_;
      /**
       * <code>.gateway_service.UnsubscribeMessageResponse unsubscribe_response = 2;</code>
       * @return Whether the unsubscribeResponse field is set.
       */
      @java.lang.Override
      public boolean hasUnsubscribeResponse() {
        return messageCase_ == 2;
      }
      /**
       * <code>.gateway_service.UnsubscribeMessageResponse unsubscribe_response = 2;</code>
       * @return The unsubscribeResponse.
       */
      @java.lang.Override
      public gateway_service.Envelope.UnsubscribeMessageResponse getUnsubscribeResponse() {
        if (unsubscribeResponseBuilder_ == null) {
          if (messageCase_ == 2) {
            return (gateway_service.Envelope.UnsubscribeMessageResponse) message_;
          }
          return gateway_service.Envelope.UnsubscribeMessageResponse.getDefaultInstance();
        } else {
          if (messageCase_ == 2) {
            return unsubscribeResponseBuilder_.getMessage();
          }
          return gateway_service.Envelope.UnsubscribeMessageResponse.getDefaultInstance();
        }
      }
      /**
       * <code>.gateway_service.UnsubscribeMessageResponse unsubscribe_response = 2;</code>
       */
      public Builder setUnsubscribeResponse(gateway_service.Envelope.UnsubscribeMessageResponse value) {
        if (unsubscribeResponseBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          message_ = value;
          onChanged();
        } else {
          unsubscribeResponseBuilder_.setMessage(value);
        }
        messageCase_ = 2;
        return this;
      }
      /**
       * <code>.gateway_service.UnsubscribeMessageResponse unsubscribe_response = 2;</code>
       */
      public Builder setUnsubscribeResponse(
          gateway_service.Envelope.UnsubscribeMessageResponse.Builder builderForValue) {
        if (unsubscribeResponseBuilder_ == null) {
          message_ = builderForValue.build();
          onChanged();
        } else {
          unsubscribeResponseBuilder_.setMessage(builderForValue.build());
        }
        messageCase_ = 2;
        return this;
      }
      /**
       * <code>.gateway_service.UnsubscribeMessageResponse unsubscribe_response = 2;</code>
       */
      public Builder mergeUnsubscribeResponse(gateway_service.Envelope.UnsubscribeMessageResponse value) {
        if (unsubscribeResponseBuilder_ == null) {
          if (messageCase_ == 2 &&
              message_ != gateway_service.Envelope.UnsubscribeMessageResponse.getDefaultInstance()) {
            message_ = gateway_service.Envelope.UnsubscribeMessageResponse.newBuilder((gateway_service.Envelope.UnsubscribeMessageResponse) message_)
                .mergeFrom(value).buildPartial();
          } else {
            message_ = value;
          }
          onChanged();
        } else {
          if (messageCase_ == 2) {
            unsubscribeResponseBuilder_.mergeFrom(value);
          } else {
            unsubscribeResponseBuilder_.setMessage(value);
          }
        }
        messageCase_ = 2;
        return this;
      }
      /**
       * <code>.gateway_service.UnsubscribeMessageResponse unsubscribe_response = 2;</code>
       */
      public Builder clearUnsubscribeResponse() {
        if (unsubscribeResponseBuilder_ == null) {
          if (messageCase_ == 2) {
            messageCase_ = 0;
            message_ = null;
            onChanged();
          }
        } else {
          if (messageCase_ == 2) {
            messageCase_ = 0;
            message_ = null;
          }
          unsubscribeResponseBuilder_.clear();
        }
        return this;
      }
      /**
       * <code>.gateway_service.UnsubscribeMessageResponse unsubscribe_response = 2;</code>
       */
      public gateway_service.Envelope.UnsubscribeMessageResponse.Builder getUnsubscribeResponseBuilder() {
        return getUnsubscribeResponseFieldBuilder().getBuilder();
      }
      /**
       * <code>.gateway_service.UnsubscribeMessageResponse unsubscribe_response = 2;</code>
       */
      @java.lang.Override
      public gateway_service.Envelope.UnsubscribeMessageResponseOrBuilder getUnsubscribeResponseOrBuilder() {
        if ((messageCase_ == 2) && (unsubscribeResponseBuilder_ != null)) {
          return unsubscribeResponseBuilder_.getMessageOrBuilder();
        } else {
          if (messageCase_ == 2) {
            return (gateway_service.Envelope.UnsubscribeMessageResponse) message_;
          }
          return gateway_service.Envelope.UnsubscribeMessageResponse.getDefaultInstance();
        }
      }
      /**
       * <code>.gateway_service.UnsubscribeMessageResponse unsubscribe_response = 2;</code>
       */
      private com.google.protobuf.SingleFieldBuilder<
          gateway_service.Envelope.UnsubscribeMessageResponse, gateway_service.Envelope.UnsubscribeMessageResponse.Builder, gateway_service.Envelope.UnsubscribeMessageResponseOrBuilder> 
          getUnsubscribeResponseFieldBuilder() {
        if (unsubscribeResponseBuilder_ == null) {
          if (!(messageCase_ == 2)) {
            message_ = gateway_service.Envelope.UnsubscribeMessageResponse.getDefaultInstance();
          }
          unsubscribeResponseBuilder_ = new com.google.protobuf.SingleFieldBuilder<
              gateway_service.Envelope.UnsubscribeMessageResponse, gateway_service.Envelope.UnsubscribeMessageResponse.Builder, gateway_service.Envelope.UnsubscribeMessageResponseOrBuilder>(
                  (gateway_service.Envelope.UnsubscribeMessageResponse) message_,
                  getParentForChildren(),
                  isClean());
          message_ = null;
        }
        messageCase_ = 2;
        onChanged();
        return unsubscribeResponseBuilder_;
      }

      private com.google.protobuf.SingleFieldBuilder<
          gateway_service.Envelope.ResponseMessage, gateway_service.Envelope.ResponseMessage.Builder, gateway_service.Envelope.ResponseMessageOrBuilder> responseMessageBuilder_;
      /**
       * <code>.gateway_service.ResponseMessage response_message = 3;</code>
       * @return Whether the responseMessage field is set.
       */
      @java.lang.Override
      public boolean hasResponseMessage() {
        return messageCase_ == 3;
      }
      /**
       * <code>.gateway_service.ResponseMessage response_message = 3;</code>
       * @return The responseMessage.
       */
      @java.lang.Override
      public gateway_service.Envelope.ResponseMessage getResponseMessage() {
        if (responseMessageBuilder_ == null) {
          if (messageCase_ == 3) {
            return (gateway_service.Envelope.ResponseMessage) message_;
          }
          return gateway_service.Envelope.ResponseMessage.getDefaultInstance();
        } else {
          if (messageCase_ == 3) {
            return responseMessageBuilder_.getMessage();
          }
          return gateway_service.Envelope.ResponseMessage.getDefaultInstance();
        }
      }
      /**
       * <code>.gateway_service.ResponseMessage response_message = 3;</code>
       */
      public Builder setResponseMessage(gateway_service.Envelope.ResponseMessage value) {
        if (responseMessageBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          message_ = value;
          onChanged();
        } else {
          responseMessageBuilder_.setMessage(value);
        }
        messageCase_ = 3;
        return this;
      }
      /**
       * <code>.gateway_service.ResponseMessage response_message = 3;</code>
       */
      public Builder setResponseMessage(
          gateway_service.Envelope.ResponseMessage.Builder builderForValue) {
        if (responseMessageBuilder_ == null) {
          message_ = builderForValue.build();
          onChanged();
        } else {
          responseMessageBuilder_.setMessage(builderForValue.build());
        }
        messageCase_ = 3;
        return this;
      }
      /**
       * <code>.gateway_service.ResponseMessage response_message = 3;</code>
       */
      public Builder mergeResponseMessage(gateway_service.Envelope.ResponseMessage value) {
        if (responseMessageBuilder_ == null) {
          if (messageCase_ == 3 &&
              message_ != gateway_service.Envelope.ResponseMessage.getDefaultInstance()) {
            message_ = gateway_service.Envelope.ResponseMessage.newBuilder((gateway_service.Envelope.ResponseMessage) message_)
                .mergeFrom(value).buildPartial();
          } else {
            message_ = value;
          }
          onChanged();
        } else {
          if (messageCase_ == 3) {
            responseMessageBuilder_.mergeFrom(value);
          } else {
            responseMessageBuilder_.setMessage(value);
          }
        }
        messageCase_ = 3;
        return this;
      }
      /**
       * <code>.gateway_service.ResponseMessage response_message = 3;</code>
       */
      public Builder clearResponseMessage() {
        if (responseMessageBuilder_ == null) {
          if (messageCase_ == 3) {
            messageCase_ = 0;
            message_ = null;
            onChanged();
          }
        } else {
          if (messageCase_ == 3) {
            messageCase_ = 0;
            message_ = null;
          }
          responseMessageBuilder_.clear();
        }
        return this;
      }
      /**
       * <code>.gateway_service.ResponseMessage response_message = 3;</code>
       */
      public gateway_service.Envelope.ResponseMessage.Builder getResponseMessageBuilder() {
        return getResponseMessageFieldBuilder().getBuilder();
      }
      /**
       * <code>.gateway_service.ResponseMessage response_message = 3;</code>
       */
      @java.lang.Override
      public gateway_service.Envelope.ResponseMessageOrBuilder getResponseMessageOrBuilder() {
        if ((messageCase_ == 3) && (responseMessageBuilder_ != null)) {
          return responseMessageBuilder_.getMessageOrBuilder();
        } else {
          if (messageCase_ == 3) {
            return (gateway_service.Envelope.ResponseMessage) message_;
          }
          return gateway_service.Envelope.ResponseMessage.getDefaultInstance();
        }
      }
      /**
       * <code>.gateway_service.ResponseMessage response_message = 3;</code>
       */
      private com.google.protobuf.SingleFieldBuilder<
          gateway_service.Envelope.ResponseMessage, gateway_service.Envelope.ResponseMessage.Builder, gateway_service.Envelope.ResponseMessageOrBuilder> 
          getResponseMessageFieldBuilder() {
        if (responseMessageBuilder_ == null) {
          if (!(messageCase_ == 3)) {
            message_ = gateway_service.Envelope.ResponseMessage.getDefaultInstance();
          }
          responseMessageBuilder_ = new com.google.protobuf.SingleFieldBuilder<
              gateway_service.Envelope.ResponseMessage, gateway_service.Envelope.ResponseMessage.Builder, gateway_service.Envelope.ResponseMessageOrBuilder>(
                  (gateway_service.Envelope.ResponseMessage) message_,
                  getParentForChildren(),
                  isClean());
          message_ = null;
        }
        messageCase_ = 3;
        onChanged();
        return responseMessageBuilder_;
      }

      private com.google.protobuf.SingleFieldBuilder<
          gateway_service.Envelope.EventMessage, gateway_service.Envelope.EventMessage.Builder, gateway_service.Envelope.EventMessageOrBuilder> eventMessageBuilder_;
      /**
       * <code>.gateway_service.EventMessage event_message = 4;</code>
       * @return Whether the eventMessage field is set.
       */
      @java.lang.Override
      public boolean hasEventMessage() {
        return messageCase_ == 4;
      }
      /**
       * <code>.gateway_service.EventMessage event_message = 4;</code>
       * @return The eventMessage.
       */
      @java.lang.Override
      public gateway_service.Envelope.EventMessage getEventMessage() {
        if (eventMessageBuilder_ == null) {
          if (messageCase_ == 4) {
            return (gateway_service.Envelope.EventMessage) message_;
          }
          return gateway_service.Envelope.EventMessage.getDefaultInstance();
        } else {
          if (messageCase_ == 4) {
            return eventMessageBuilder_.getMessage();
          }
          return gateway_service.Envelope.EventMessage.getDefaultInstance();
        }
      }
      /**
       * <code>.gateway_service.EventMessage event_message = 4;</code>
       */
      public Builder setEventMessage(gateway_service.Envelope.EventMessage value) {
        if (eventMessageBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          message_ = value;
          onChanged();
        } else {
          eventMessageBuilder_.setMessage(value);
        }
        messageCase_ = 4;
        return this;
      }
      /**
       * <code>.gateway_service.EventMessage event_message = 4;</code>
       */
      public Builder setEventMessage(
          gateway_service.Envelope.EventMessage.Builder builderForValue) {
        if (eventMessageBuilder_ == null) {
          message_ = builderForValue.build();
          onChanged();
        } else {
          eventMessageBuilder_.setMessage(builderForValue.build());
        }
        messageCase_ = 4;
        return this;
      }
      /**
       * <code>.gateway_service.EventMessage event_message = 4;</code>
       */
      public Builder mergeEventMessage(gateway_service.Envelope.EventMessage value) {
        if (eventMessageBuilder_ == null) {
          if (messageCase_ == 4 &&
              message_ != gateway_service.Envelope.EventMessage.getDefaultInstance()) {
            message_ = gateway_service.Envelope.EventMessage.newBuilder((gateway_service.Envelope.EventMessage) message_)
                .mergeFrom(value).buildPartial();
          } else {
            message_ = value;
          }
          onChanged();
        } else {
          if (messageCase_ == 4) {
            eventMessageBuilder_.mergeFrom(value);
          } else {
            eventMessageBuilder_.setMessage(value);
          }
        }
        messageCase_ = 4;
        return this;
      }
      /**
       * <code>.gateway_service.EventMessage event_message = 4;</code>
       */
      public Builder clearEventMessage() {
        if (eventMessageBuilder_ == null) {
          if (messageCase_ == 4) {
            messageCase_ = 0;
            message_ = null;
            onChanged();
          }
        } else {
          if (messageCase_ == 4) {
            messageCase_ = 0;
            message_ = null;
          }
          eventMessageBuilder_.clear();
        }
        return this;
      }
      /**
       * <code>.gateway_service.EventMessage event_message = 4;</code>
       */
      public gateway_service.Envelope.EventMessage.Builder getEventMessageBuilder() {
        return getEventMessageFieldBuilder().getBuilder();
      }
      /**
       * <code>.gateway_service.EventMessage event_message = 4;</code>
       */
      @java.lang.Override
      public gateway_service.Envelope.EventMessageOrBuilder getEventMessageOrBuilder() {
        if ((messageCase_ == 4) && (eventMessageBuilder_ != null)) {
          return eventMessageBuilder_.getMessageOrBuilder();
        } else {
          if (messageCase_ == 4) {
            return (gateway_service.Envelope.EventMessage) message_;
          }
          return gateway_service.Envelope.EventMessage.getDefaultInstance();
        }
      }
      /**
       * <code>.gateway_service.EventMessage event_message = 4;</code>
       */
      private com.google.protobuf.SingleFieldBuilder<
          gateway_service.Envelope.EventMessage, gateway_service.Envelope.EventMessage.Builder, gateway_service.Envelope.EventMessageOrBuilder> 
          getEventMessageFieldBuilder() {
        if (eventMessageBuilder_ == null) {
          if (!(messageCase_ == 4)) {
            message_ = gateway_service.Envelope.EventMessage.getDefaultInstance();
          }
          eventMessageBuilder_ = new com.google.protobuf.SingleFieldBuilder<
              gateway_service.Envelope.EventMessage, gateway_service.Envelope.EventMessage.Builder, gateway_service.Envelope.EventMessageOrBuilder>(
                  (gateway_service.Envelope.EventMessage) message_,
                  getParentForChildren(),
                  isClean());
          message_ = null;
        }
        messageCase_ = 4;
        onChanged();
        return eventMessageBuilder_;
      }

      private com.google.protobuf.SingleFieldBuilder<
          gateway_service.Envelope.RFCResponseMessage, gateway_service.Envelope.RFCResponseMessage.Builder, gateway_service.Envelope.RFCResponseMessageOrBuilder> rfcResponseBuilder_;
      /**
       * <code>.gateway_service.RFCResponseMessage rfc_response = 5;</code>
       * @return Whether the rfcResponse field is set.
       */
      @java.lang.Override
      public boolean hasRfcResponse() {
        return messageCase_ == 5;
      }
      /**
       * <code>.gateway_service.RFCResponseMessage rfc_response = 5;</code>
       * @return The rfcResponse.
       */
      @java.lang.Override
      public gateway_service.Envelope.RFCResponseMessage getRfcResponse() {
        if (rfcResponseBuilder_ == null) {
          if (messageCase_ == 5) {
            return (gateway_service.Envelope.RFCResponseMessage) message_;
          }
          return gateway_service.Envelope.RFCResponseMessage.getDefaultInstance();
        } else {
          if (messageCase_ == 5) {
            return rfcResponseBuilder_.getMessage();
          }
          return gateway_service.Envelope.RFCResponseMessage.getDefaultInstance();
        }
      }
      /**
       * <code>.gateway_service.RFCResponseMessage rfc_response = 5;</code>
       */
      public Builder setRfcResponse(gateway_service.Envelope.RFCResponseMessage value) {
        if (rfcResponseBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          message_ = value;
          onChanged();
        } else {
          rfcResponseBuilder_.setMessage(value);
        }
        messageCase_ = 5;
        return this;
      }
      /**
       * <code>.gateway_service.RFCResponseMessage rfc_response = 5;</code>
       */
      public Builder setRfcResponse(
          gateway_service.Envelope.RFCResponseMessage.Builder builderForValue) {
        if (rfcResponseBuilder_ == null) {
          message_ = builderForValue.build();
          onChanged();
        } else {
          rfcResponseBuilder_.setMessage(builderForValue.build());
        }
        messageCase_ = 5;
        return this;
      }
      /**
       * <code>.gateway_service.RFCResponseMessage rfc_response = 5;</code>
       */
      public Builder mergeRfcResponse(gateway_service.Envelope.RFCResponseMessage value) {
        if (rfcResponseBuilder_ == null) {
          if (messageCase_ == 5 &&
              message_ != gateway_service.Envelope.RFCResponseMessage.getDefaultInstance()) {
            message_ = gateway_service.Envelope.RFCResponseMessage.newBuilder((gateway_service.Envelope.RFCResponseMessage) message_)
                .mergeFrom(value).buildPartial();
          } else {
            message_ = value;
          }
          onChanged();
        } else {
          if (messageCase_ == 5) {
            rfcResponseBuilder_.mergeFrom(value);
          } else {
            rfcResponseBuilder_.setMessage(value);
          }
        }
        messageCase_ = 5;
        return this;
      }
      /**
       * <code>.gateway_service.RFCResponseMessage rfc_response = 5;</code>
       */
      public Builder clearRfcResponse() {
        if (rfcResponseBuilder_ == null) {
          if (messageCase_ == 5) {
            messageCase_ = 0;
            message_ = null;
            onChanged();
          }
        } else {
          if (messageCase_ == 5) {
            messageCase_ = 0;
            message_ = null;
          }
          rfcResponseBuilder_.clear();
        }
        return this;
      }
      /**
       * <code>.gateway_service.RFCResponseMessage rfc_response = 5;</code>
       */
      public gateway_service.Envelope.RFCResponseMessage.Builder getRfcResponseBuilder() {
        return getRfcResponseFieldBuilder().getBuilder();
      }
      /**
       * <code>.gateway_service.RFCResponseMessage rfc_response = 5;</code>
       */
      @java.lang.Override
      public gateway_service.Envelope.RFCResponseMessageOrBuilder getRfcResponseOrBuilder() {
        if ((messageCase_ == 5) && (rfcResponseBuilder_ != null)) {
          return rfcResponseBuilder_.getMessageOrBuilder();
        } else {
          if (messageCase_ == 5) {
            return (gateway_service.Envelope.RFCResponseMessage) message_;
          }
          return gateway_service.Envelope.RFCResponseMessage.getDefaultInstance();
        }
      }
      /**
       * <code>.gateway_service.RFCResponseMessage rfc_response = 5;</code>
       */
      private com.google.protobuf.SingleFieldBuilder<
          gateway_service.Envelope.RFCResponseMessage, gateway_service.Envelope.RFCResponseMessage.Builder, gateway_service.Envelope.RFCResponseMessageOrBuilder> 
          getRfcResponseFieldBuilder() {
        if (rfcResponseBuilder_ == null) {
          if (!(messageCase_ == 5)) {
            message_ = gateway_service.Envelope.RFCResponseMessage.getDefaultInstance();
          }
          rfcResponseBuilder_ = new com.google.protobuf.SingleFieldBuilder<
              gateway_service.Envelope.RFCResponseMessage, gateway_service.Envelope.RFCResponseMessage.Builder, gateway_service.Envelope.RFCResponseMessageOrBuilder>(
                  (gateway_service.Envelope.RFCResponseMessage) message_,
                  getParentForChildren(),
                  isClean());
          message_ = null;
        }
        messageCase_ = 5;
        onChanged();
        return rfcResponseBuilder_;
      }

      private long timestampMs_ ;
      /**
       * <pre>
       * Time in ms from UNIX epoch
       * </pre>
       *
       * <code>optional uint64 timestamp_ms = 6;</code>
       * @return Whether the timestampMs field is set.
       */
      @java.lang.Override
      public boolean hasTimestampMs() {
        return ((bitField0_ & 0x00000020) != 0);
      }
      /**
       * <pre>
       * Time in ms from UNIX epoch
       * </pre>
       *
       * <code>optional uint64 timestamp_ms = 6;</code>
       * @return The timestampMs.
       */
      @java.lang.Override
      public long getTimestampMs() {
        return timestampMs_;
      }
      /**
       * <pre>
       * Time in ms from UNIX epoch
       * </pre>
       *
       * <code>optional uint64 timestamp_ms = 6;</code>
       * @param value The timestampMs to set.
       * @return This builder for chaining.
       */
      public Builder setTimestampMs(long value) {

        timestampMs_ = value;
        bitField0_ |= 0x00000020;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * Time in ms from UNIX epoch
       * </pre>
       *
       * <code>optional uint64 timestamp_ms = 6;</code>
       * @return This builder for chaining.
       */
      public Builder clearTimestampMs() {
        bitField0_ = (bitField0_ & ~0x00000020);
        timestampMs_ = 0L;
        onChanged();
        return this;
      }

      // @@protoc_insertion_point(builder_scope:gateway_service.GatewayResponseMessage)
    }

    // @@protoc_insertion_point(class_scope:gateway_service.GatewayResponseMessage)
    private static final gateway_service.Envelope.GatewayResponseMessage DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new gateway_service.Envelope.GatewayResponseMessage();
    }

    public static gateway_service.Envelope.GatewayResponseMessage getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<GatewayResponseMessage>
        PARSER = new com.google.protobuf.AbstractParser<GatewayResponseMessage>() {
      @java.lang.Override
      public GatewayResponseMessage parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<GatewayResponseMessage> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<GatewayResponseMessage> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public gateway_service.Envelope.GatewayResponseMessage getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_gateway_service_SubscribeMessageRequest_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_gateway_service_SubscribeMessageRequest_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_gateway_service_SubscribeMessageResponse_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_gateway_service_SubscribeMessageResponse_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_gateway_service_UnsubscribeMessageRequest_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_gateway_service_UnsubscribeMessageRequest_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_gateway_service_UnsubscribeMessageResponse_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_gateway_service_UnsubscribeMessageResponse_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_gateway_service_RequestMessage_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_gateway_service_RequestMessage_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_gateway_service_ResponseMessage_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_gateway_service_ResponseMessage_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_gateway_service_EventMessage_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_gateway_service_EventMessage_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_gateway_service_RFCRequestMessage_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_gateway_service_RFCRequestMessage_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_gateway_service_RFCResponseMessage_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_gateway_service_RFCResponseMessage_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_gateway_service_GatewayRequestMessage_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_gateway_service_GatewayRequestMessage_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_gateway_service_GatewayResponseMessage_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_gateway_service_GatewayResponseMessage_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n\016envelope.proto\022\017gateway_service\"u\n\027Sub" +
      "scribeMessageRequest\022\021\n\004hash\030\001 \001(\tH\000\210\001\001\022" +
      "#\n\026wait_for_service_in_ms\030\002 \001(\rH\001\210\001\001B\007\n\005" +
      "_hashB\031\n\027_wait_for_service_in_ms\"l\n\030Subs" +
      "cribeMessageResponse\0224\n\006result\030\001 \001(\0162$.g" +
      "ateway_service.EnumOperationStatus\022\021\n\004ha" +
      "sh\030\002 \001(\tH\000\210\001\001B\007\n\005_hash\"7\n\031UnsubscribeMes" +
      "sageRequest\022\021\n\004hash\030\001 \001(\tH\000\210\001\001B\007\n\005_hash\"" +
      "n\n\032UnsubscribeMessageResponse\0224\n\006result\030" +
      "\001 \001(\0162$.gateway_service.EnumOperationSta" +
      "tus\022\021\n\004hash\030\002 \001(\tH\000\210\001\001B\007\n\005_hash\"\212\002\n\016Requ" +
      "estMessage\022\021\n\004hash\030\001 \001(\tH\000\210\001\001\022\034\n\017request" +
      "_context\030\002 \001(\rH\001\210\001\001\022#\n\026wait_for_service_" +
      "in_ms\030\003 \001(\rH\002\210\001\001\022 \n\023response_timeout_ms\030" +
      "\004 \001(\rH\003\210\001\001\022\034\n\017message_payload\030\005 \001(\014H\004\210\001\001" +
      "B\007\n\005_hashB\022\n\020_request_contextB\031\n\027_wait_f" +
      "or_service_in_msB\026\n\024_response_timeout_ms" +
      "B\022\n\020_message_payload\"\307\001\n\017ResponseMessage" +
      "\0224\n\006result\030\001 \001(\0162$.gateway_service.EnumO" +
      "perationStatus\022\034\n\017request_context\030\002 \001(\rH" +
      "\000\210\001\001\022\021\n\004hash\030\003 \001(\tH\001\210\001\001\022\034\n\017message_paylo" +
      "ad\030\004 \001(\014H\002\210\001\001B\022\n\020_request_contextB\007\n\005_ha" +
      "shB\022\n\020_message_payload\"\\\n\014EventMessage\022\021" +
      "\n\004hash\030\001 \001(\tH\000\210\001\001\022\034\n\017message_payload\030\002 \001" +
      "(\014H\001\210\001\001B\007\n\005_hashB\022\n\020_message_payload\"\205\003\n" +
      "\021RFCRequestMessage\022\034\n\017request_context\030\001 " +
      "\001(\rH\001\210\001\001\022@\n\016network_action\030\002 \001(\0162(.gatew" +
      "ay_service.EnumNetworkDemandAction\022\033\n\016ne" +
      "twork_demand\030\003 \001(\rH\002\210\001\001\022#\n\026network_deman" +
      "d_time_ms\030\004 \001(\rH\003\210\001\001\022E\n\021subscribe_reques" +
      "t\030\005 \001(\0132(.gateway_service.SubscribeMessa" +
      "geRequestH\000\022:\n\017request_message\030\006 \001(\0132\037.g" +
      "ateway_service.RequestMessageH\000B\t\n\007messa" +
      "geB\022\n\020_request_contextB\021\n\017_network_deman" +
      "dB\031\n\027_network_demand_time_ms\"|\n\022RFCRespo" +
      "nseMessage\0224\n\006result\030\001 \001(\0162$.gateway_ser" +
      "vice.EnumOperationStatus\022\034\n\017request_cont" +
      "ext\030\002 \001(\rH\000\210\001\001B\022\n\020_request_context\"\313\003\n\025G" +
      "atewayRequestMessage\022E\n\021subscribe_reques" +
      "t\030\001 \001(\0132(.gateway_service.SubscribeMessa" +
      "geRequestH\000\022I\n\023unsubscribe_request\030\002 \001(\013" +
      "2*.gateway_service.UnsubscribeMessageReq" +
      "uestH\000\022:\n\017request_message\030\003 \001(\0132\037.gatewa" +
      "y_service.RequestMessageH\000\0229\n\013rfc_messag" +
      "e\030\004 \001(\0132\".gateway_service.RFCRequestMess" +
      "ageH\000\022\032\n\rcreation_time\030\005 \001(\004H\001\210\001\001\022\031\n\014tim" +
      "e_to_live\030\006 \001(\rH\002\210\001\001\022\030\n\013sequence_no\030\007 \001(" +
      "\rH\003\210\001\001\022\021\n\004vuid\030\010 \001(\tH\004\210\001\001B\t\n\007messageB\020\n\016" +
      "_creation_timeB\017\n\r_time_to_liveB\016\n\014_sequ" +
      "ence_noB\007\n\005_vuid\"\230\003\n\026GatewayResponseMess" +
      "age\022G\n\022subscribe_response\030\001 \001(\0132).gatewa" +
      "y_service.SubscribeMessageResponseH\000\022K\n\024" +
      "unsubscribe_response\030\002 \001(\0132+.gateway_ser" +
      "vice.UnsubscribeMessageResponseH\000\022<\n\020res" +
      "ponse_message\030\003 \001(\0132 .gateway_service.Re" +
      "sponseMessageH\000\0226\n\revent_message\030\004 \001(\0132\035" +
      ".gateway_service.EventMessageH\000\022;\n\014rfc_r" +
      "esponse\030\005 \001(\0132#.gateway_service.RFCRespo" +
      "nseMessageH\000\022\031\n\014timestamp_ms\030\006 \001(\004H\001\210\001\001B" +
      "\t\n\007messageB\017\n\r_timestamp_ms*\375\003\n\023EnumOper" +
      "ationStatus\022%\n!ENUM_OPERATION_STATUS_UNS" +
      "PECIFIED\020\000\022\034\n\030ENUM_OPERATION_STATUS_OK\020\001" +
      "\022(\n$ENUM_OPERATION_STATUS_HASH_NOT_FOUND" +
      "\020\002\022/\n+ENUM_OPERATION_STATUS_SERVICE_NOT_" +
      "AVAILABLE\020\003\022*\n&ENUM_OPERATION_STATUS_SEC" +
      "URITY_FAILURE\020\004\0221\n-ENUM_OPERATION_STATUS" +
      "_REQUEST_OUT_OF_SEQUENCE\020\005\022)\n%ENUM_OPERA" +
      "TION_STATUS_REQUEST_EXPIRED\020\006\022+\n\'ENUM_OP" +
      "ERATION_STATUS_REQUEST_TIMED_OUT\020\007\022;\n7EN" +
      "UM_OPERATION_STATUS_REQUEST_NETWORK_ACTI" +
      "VATION_FAILED\020\010\022*\n&ENUM_OPERATION_STATUS" +
      "_OPERATION_FAILED\020\t\022&\n\"ENUM_OPERATION_ST" +
      "ATUS_INVALID_VUID\020\n*\225\001\n\027EnumNetworkDeman" +
      "dAction\022*\n&ENUM_NETWORD_DEMAND_ACTION_UN" +
      "SPECIFIED\020\000\022&\n\"ENUM_NETWORK_DEMAND_ACTIO" +
      "N_ACQUIRE\020\001\022&\n\"ENUM_NETWORK_DEMAND_ACTIO" +
      "N_RELEASE\020\002b\006proto3"
    };
    descriptor = com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
        });
    internal_static_gateway_service_SubscribeMessageRequest_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_gateway_service_SubscribeMessageRequest_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessage.FieldAccessorTable(
        internal_static_gateway_service_SubscribeMessageRequest_descriptor,
        new java.lang.String[] { "Hash", "WaitForServiceInMs", });
    internal_static_gateway_service_SubscribeMessageResponse_descriptor =
      getDescriptor().getMessageTypes().get(1);
    internal_static_gateway_service_SubscribeMessageResponse_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessage.FieldAccessorTable(
        internal_static_gateway_service_SubscribeMessageResponse_descriptor,
        new java.lang.String[] { "Result", "Hash", });
    internal_static_gateway_service_UnsubscribeMessageRequest_descriptor =
      getDescriptor().getMessageTypes().get(2);
    internal_static_gateway_service_UnsubscribeMessageRequest_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessage.FieldAccessorTable(
        internal_static_gateway_service_UnsubscribeMessageRequest_descriptor,
        new java.lang.String[] { "Hash", });
    internal_static_gateway_service_UnsubscribeMessageResponse_descriptor =
      getDescriptor().getMessageTypes().get(3);
    internal_static_gateway_service_UnsubscribeMessageResponse_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessage.FieldAccessorTable(
        internal_static_gateway_service_UnsubscribeMessageResponse_descriptor,
        new java.lang.String[] { "Result", "Hash", });
    internal_static_gateway_service_RequestMessage_descriptor =
      getDescriptor().getMessageTypes().get(4);
    internal_static_gateway_service_RequestMessage_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessage.FieldAccessorTable(
        internal_static_gateway_service_RequestMessage_descriptor,
        new java.lang.String[] { "Hash", "RequestContext", "WaitForServiceInMs", "ResponseTimeoutMs", "MessagePayload", });
    internal_static_gateway_service_ResponseMessage_descriptor =
      getDescriptor().getMessageTypes().get(5);
    internal_static_gateway_service_ResponseMessage_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessage.FieldAccessorTable(
        internal_static_gateway_service_ResponseMessage_descriptor,
        new java.lang.String[] { "Result", "RequestContext", "Hash", "MessagePayload", });
    internal_static_gateway_service_EventMessage_descriptor =
      getDescriptor().getMessageTypes().get(6);
    internal_static_gateway_service_EventMessage_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessage.FieldAccessorTable(
        internal_static_gateway_service_EventMessage_descriptor,
        new java.lang.String[] { "Hash", "MessagePayload", });
    internal_static_gateway_service_RFCRequestMessage_descriptor =
      getDescriptor().getMessageTypes().get(7);
    internal_static_gateway_service_RFCRequestMessage_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessage.FieldAccessorTable(
        internal_static_gateway_service_RFCRequestMessage_descriptor,
        new java.lang.String[] { "RequestContext", "NetworkAction", "NetworkDemand", "NetworkDemandTimeMs", "SubscribeRequest", "RequestMessage", "Message", });
    internal_static_gateway_service_RFCResponseMessage_descriptor =
      getDescriptor().getMessageTypes().get(8);
    internal_static_gateway_service_RFCResponseMessage_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessage.FieldAccessorTable(
        internal_static_gateway_service_RFCResponseMessage_descriptor,
        new java.lang.String[] { "Result", "RequestContext", });
    internal_static_gateway_service_GatewayRequestMessage_descriptor =
      getDescriptor().getMessageTypes().get(9);
    internal_static_gateway_service_GatewayRequestMessage_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessage.FieldAccessorTable(
        internal_static_gateway_service_GatewayRequestMessage_descriptor,
        new java.lang.String[] { "SubscribeRequest", "UnsubscribeRequest", "RequestMessage", "RfcMessage", "CreationTime", "TimeToLive", "SequenceNo", "Vuid", "Message", });
    internal_static_gateway_service_GatewayResponseMessage_descriptor =
      getDescriptor().getMessageTypes().get(10);
    internal_static_gateway_service_GatewayResponseMessage_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessage.FieldAccessorTable(
        internal_static_gateway_service_GatewayResponseMessage_descriptor,
        new java.lang.String[] { "SubscribeResponse", "UnsubscribeResponse", "ResponseMessage", "EventMessage", "RfcResponse", "TimestampMs", "Message", });
    descriptor.resolveAllFeaturesImmutable();
  }

  // @@protoc_insertion_point(outer_class_scope)
}
