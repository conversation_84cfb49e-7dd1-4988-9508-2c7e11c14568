/*
 * Copyright (c) Jaguar Land Rover Ltd 2023. All rights reserved
 */

package com.jaguarlandrover.vcdp.sinks;

import static com.jaguarlandrover.vcdp.config.StreamConfig.*;
import static com.jaguarlandrover.vcdp.config.Utils.getKafkaSaslProperties;

import com.alibaba.fastjson2.JSONObject;
import com.amazonaws.services.kinesisanalytics.flink.connectors.serialization.JsonSerializationSchema;
import java.util.Properties;
import org.apache.flink.connector.kafka.sink.KafkaRecordSerializationSchema;
import org.apache.flink.connector.kafka.sink.KafkaSink;
import org.apache.flink.streaming.api.environment.StreamExecutionEnvironment;

public class KafkaStreamingSink {
  private  KafkaStreamingSink() {}

  public static KafkaSink<JSONObject> getKafkaSink(StreamExecutionEnvironment sEnv, Properties propertiesMap) {

    Properties kafkaSinkProperties = getKafkaSaslProperties(sEnv);
    kafkaSinkProperties.put("transaction.timeout.ms", "60000");

    KafkaRecordSerializationSchema<JSONObject> serializer =
            KafkaRecordSerializationSchema.builder()
                    .setValueSerializationSchema(new JsonSerializationSchema<JSONObject>())
                    .setTopic(propertiesMap.getProperty(KAFKA_TOPIC_JSON_OUT_NAME))
                    .build();

    return KafkaSink.<JSONObject>builder()
            .setBootstrapServers(propertiesMap.getProperty(KAFKA_OUTPUT_BOOTSTRAP_SERVERS))
            .setKafkaProducerConfig(kafkaSinkProperties)
            .setRecordSerializer(serializer)
            .build();
  }
}
