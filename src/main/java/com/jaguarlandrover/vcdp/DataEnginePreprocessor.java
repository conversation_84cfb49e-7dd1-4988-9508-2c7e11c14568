/*
 * Copyright (c) Jaguar Land Rover Ltd 2023. All rights reserved
 */

package com.jaguarlandrover.vcdp;

import static com.jaguarlandrover.vcdp.config.StreamConfig.*;
import static com.jaguarlandrover.vcdp.config.Utils.*;
import static com.jaguarlandrover.vcdp.sinks.KafkaStreamingSink.getKafkaSink;
import static com.jaguarlandrover.vcdp.sources.KafkaStreamingSource.getKafkaProtobufToJsonObjectSource;

import com.alibaba.fastjson2.JSONObject;
import com.jaguarlandrover.vcdp.config.ConfigLoader;
import com.jaguarlandrover.vcdp.functions.SourceVaFormatMappingWithKeyFunction;
import java.util.Map;
import java.util.Objects;
import java.util.Properties;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.flink.api.common.eventtime.WatermarkStrategy;
import org.apache.flink.connector.kafka.sink.KafkaSink;
import org.apache.flink.connector.kafka.source.KafkaSource;
import org.apache.flink.streaming.api.datastream.SingleOutputStreamOperator;
import org.apache.flink.streaming.api.environment.StreamExecutionEnvironment;


@Slf4j
public class DataEnginePreprocessor {

  public static final String PROPERTIES_FILE = "localRunConfing/application-properties-dev.json";

  @SneakyThrows
  public static void main(String[] args) {
    log.info("Starting Data Engine Preprocessor Flink Streaming job");
    StreamExecutionEnvironment sEnv = configureEnvironment();
    sEnv.setBufferTimeout(BUFFER_OUT_TIME);
    Map<String, Properties> propertiesMap = initPropertiesMap(sEnv, PROPERTIES_FILE);
    Properties kafkaProperties = propertiesMap.get(KAFKA_SOURCE_GROUP_ID_KEY);

    Map<String, String> hashMapping =  ConfigLoader.generateMessageTypeHashMapping(sEnv, PROTO_DESC_DIRECTORY);

    KafkaSource<JSONObject> messageWrapperProtobufJsonObjectSource =
            getKafkaProtobufToJsonObjectSource(sEnv, kafkaProperties, hashMapping);

    KafkaSink<JSONObject> kafkaSink = getKafkaSink(sEnv, kafkaProperties);

    defineWorkFlow(sEnv, messageWrapperProtobufJsonObjectSource, kafkaSink);

    sEnv.execute("Data Engine Preprocessor");
  }

  public static void defineWorkFlow(StreamExecutionEnvironment sEnv,
                                    KafkaSource<JSONObject> source,
                                    KafkaSink<JSONObject> kafkaSink) {
    // Ingest Protobuf Source stream
    SingleOutputStreamOperator<JSONObject> kafkaIngestSource = sEnv
            .fromSource(source, WatermarkStrategy.noWatermarks(), "kafka-protobuf-source")
            .name("kafkaIngestSourceStream")
            .uid("kafkaIngestSourceStream");

    // Mapping the (Deserialize Protobuf / JSON) to the (VA Format)  stream
    JSONObject mappingJson = ConfigLoader.getJsonObjectFromResourceFile(MAPPING_JSON_TRANSFORMATION);

    SingleOutputStreamOperator<JSONObject> kafkaMappingSourceStream =  kafkaIngestSource
            .map(new SourceVaFormatMappingWithKeyFunction(mappingJson))
            .filter(jsonObj -> !jsonObj.isEmpty())
            .name("kafkaMappingSourceStream")
            .uid("kafkaMappingSourceStream");

    // Output the deserialized kafkaIngestSource stream into kafka
    kafkaMappingSourceStream
            .sinkTo(kafkaSink)
            .name("kafkaSink")
            .uid("kafkaSink");
  }
}

