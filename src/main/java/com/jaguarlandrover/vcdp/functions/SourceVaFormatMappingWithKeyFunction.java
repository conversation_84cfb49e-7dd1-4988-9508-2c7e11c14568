/*
 * Copyright (c) Jaguar Land Rover Ltd 2023. All rights reserved
 */

package com.jaguarlandrover.vcdp.functions;

import static com.jaguarlandrover.vcdp.config.Utils.anonymizeString;

import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.apache.flink.api.common.functions.RichMapFunction;

/**
 * RichMapFunction that transforms protobuf deserialised messages into VA format.
 */
@Slf4j
public class SourceVaFormatMappingWithKeyFunction extends RichMapFunction<JSONObject, JSONObject> {
  static final long serialVersionUID = 1;
  private static final String ERROR_MESSAGE_TYPE = "Data-engine-preprocessor, error_type=mapping-error ";
  private static final int ANONYMIZATION_INDEX = 8;
  private final JSONObject transformationMappingJson;

  /**
   * Construct a new SourceVaFormatMappingWithKeyFunction with the given transformation mapping.

   * @param inputJson The JSON object containing the transformation mapping.
   */
  public SourceVaFormatMappingWithKeyFunction(JSONObject inputJson) {
    transformationMappingJson = inputJson.clone();
  }

  /**
   * Map the input message to a JSONObject in VA format.

   * @param message The input message to be mapped.
   * @return A JSONObject in VA format.
   */
  @Override
  public JSONObject map(JSONObject message) {

    JSONObject vaFormatMsg = new JSONObject();
    try {

      String messageHash = message.getString("hash");
      JSONObject transformationMapping = transformationMappingJson.getJSONObject(messageHash);
      if (transformationMapping == null) {
        log.error("{}error_message=No transformation mapping found for hash: {}", ERROR_MESSAGE_TYPE, messageHash);
        return new JSONObject();
      }
      String messageUniqueId = message.getString("unique_id");
      JSONObject messagePayload = message.getJSONObject("payload");
      if ("ENUM_STATUS_OK".equals(messagePayload.get("status"))) {
        vaFormatMsg.put("unique_id", messageUniqueId);
        vaFormatMsg.put("query_id", transformationMapping.get("query_id"));
        Long messageTimestamp = message.getLong("timestamp_ms");
        JSONObject dataIdMappings = transformationMapping.getJSONObject("data_id_mapping");
        JSONArray dataList = new JSONArray();
        for (String key : messagePayload.keySet()) {
          if (dataIdMappings.containsKey(key)) {
            JSONObject keyDataIdMapping = dataIdMappings.getJSONObject(key);
            JSONObject dataElement = new JSONObject();
            dataElement.put("data_id", keyDataIdMapping.get("id"));
            JSONObject sampleElement = new JSONObject();
            sampleElement.put("timestamp_ms", messageTimestamp);
            sampleElement.put("value", messagePayload.get(key));
            sampleElement.put("type", keyDataIdMapping.get("type"));
            JSONArray sampleList = new JSONArray();
            sampleList.add(sampleElement);
            dataElement.put("samples", sampleList);
            dataList.add(dataElement);
          }
        }
        if (!dataList.isEmpty()) {
          vaFormatMsg.put("data", dataList);
        }
        vaFormatMsg.put("event_timestamp_ms", messageTimestamp);
        vaFormatMsg.put("fleet_id", "");
        vaFormatMsg.put("start_deserialisation_timestamp", message.getString("start_deserialisation_timestamp"));
        vaFormatMsg.put("end_deserialisation_timestamp", System.currentTimeMillis());

      } else {
        log.error("{}error_message=Input Message ENUM_STATUS Error for vuid: {}" ,
                ERROR_MESSAGE_TYPE,
                anonymizeString(messageUniqueId, ANONYMIZATION_INDEX));
        return new JSONObject();
      }

    } catch (Exception e) {
      log.error("{}error_message= issue doing the mapping in map function --{}",
              ERROR_MESSAGE_TYPE,
              e.getMessage());
    }
    return vaFormatMsg;
  }
}
