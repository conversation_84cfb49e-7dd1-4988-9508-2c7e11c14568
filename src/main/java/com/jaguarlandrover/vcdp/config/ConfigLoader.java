/*
 * Copyright (c) Jaguar Land Rover Ltd 2023. All rights reserved
 */

package com.jaguarlandrover.vcdp.config;

import static com.jaguarlandrover.vcdp.config.StreamConfig.MESSAGE_TYPE_START_WITH_LIST;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONException;
import com.alibaba.fastjson2.JSONObject;
import com.google.protobuf.DescriptorProtos;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.math.BigInteger;
import java.net.URI;
import java.net.URISyntaxException;
import java.net.URL;
import java.nio.charset.StandardCharsets;
import java.nio.file.FileSystem;
import java.nio.file.FileSystems;
import java.nio.file.Files;
import java.nio.file.Path;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.*;
import java.util.stream.Collectors;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.flink.streaming.api.environment.LocalStreamEnvironment;
import org.apache.flink.streaming.api.environment.StreamExecutionEnvironment;


@Slf4j
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class ConfigLoader {
  private static final String ERROR_MESSAGE_TYPE = "Data-engine-preprocessor, error_type=config-error ";

  public static JSONObject getJsonObjectFromResourceFile(String jsonFileName) {
    try {
      InputStream configPropertiesStream = ConfigLoader.class.getClassLoader().getResourceAsStream(jsonFileName);
      if (configPropertiesStream == null) {
        log.error("configPropertiesStream is null");
        return new JSONObject();
      }
      log.info("Loading mapping Json file {} ", jsonFileName);
      byte[] bytes = configPropertiesStream.readAllBytes();
      String transformationMappingString = new String(bytes, StandardCharsets.UTF_8);
      return JSON.parseObject(transformationMappingString);
    } catch (JSONException e) {
      log.error("{}error_message= issue parsing json mapping file -- {}", ERROR_MESSAGE_TYPE, e.getMessage());
    } catch (Exception e) {
      log.error("{}error_message= issue loading json mapping file -- {}", ERROR_MESSAGE_TYPE, e.getMessage());
    }
    return new JSONObject();
  }

  public static Map<String, String> generateMessageTypeHashMapping(StreamExecutionEnvironment sEnv,
                                                                   String protoDescDirectory) {
    // generate {Hashed(Id) -> ClassName} mapping from Descriptors
    Map<String, String> messageTypeHashMap = new HashMap<>();
    if (sEnv instanceof LocalStreamEnvironment) {
      try {
        URL descriptorsDirUrl = ConfigLoader.class.getResource(protoDescDirectory);
        if (descriptorsDirUrl != null) {
          File descDir = new File(descriptorsDirUrl.getPath());
          for (File file : Objects.requireNonNull(descDir.listFiles())) {
            if (!file.isDirectory()) {
              InputStream resourceAsStream = new FileInputStream(file);
              messageTypeHashMap.putAll(extractIdClassMapFromDescriptor(resourceAsStream));
            }
          }
        } else {
          log.error("Proto Desc folder not found.");
        }
      } catch (IOException e) {
        log.error("{}error_message= issue reading proto descriptor files in local -- {}", ERROR_MESSAGE_TYPE,
                e.getMessage());
      }
    } else {
      try {
        List<Path> result;

        // get path of the current running JAR
        String jarPath = ConfigLoader.class.getProtectionDomain().getCodeSource().getLocation().toURI().getPath();
        log.info("JAR Path : {}", jarPath);

        // file walks JAR
        URI uri = URI.create("jar:file:" + jarPath);
        FileSystem fs = FileSystems.newFileSystem(uri, Collections.emptyMap());
        result = Files.walk(fs.getPath(protoDescDirectory)).filter(Files::isRegularFile).collect(Collectors.toList());

        for (Path path : result) {
          String filePathInJar = path.toString();
          if (filePathInJar.startsWith("/")) {
            filePathInJar = filePathInJar.substring(1);
          }
          log.info("DescriptorPathInJAR : {}", filePathInJar);

          // read a file from resource folder
          InputStream resourceAsStream = ConfigLoader.class.getClassLoader().getResourceAsStream(filePathInJar);
          messageTypeHashMap.putAll(extractIdClassMapFromDescriptor(resourceAsStream));

        }
        fs.close();
      } catch (URISyntaxException | IOException e) {
        log.error("{}error_message= issue reading proto Descriptor files -- {}", ERROR_MESSAGE_TYPE, e.getMessage());
      }
    }

    return messageTypeHashMap;
  }

  @SneakyThrows
  private static Map<String, String> extractIdClassMapFromDescriptor(InputStream resourceAsStream) throws IOException {
    Map<String, String> hash2IdClassTypeHashMap = new HashMap<>();

    DescriptorProtos.FileDescriptorSet fileDescSet = DescriptorProtos.FileDescriptorSet.parseFrom(resourceAsStream);
    for ( DescriptorProtos.FileDescriptorProto fileDescProto : fileDescSet.getFileList() ){
      String fileName = fileDescProto.getName();
      String serviceName = fileName.substring(0, fileName.lastIndexOf("."));
      String className =  Arrays.stream(serviceName.split("_"))
              .map(s -> s.substring(0, 1).toUpperCase(Locale.ENGLISH) + s.substring(1))
              .collect(Collectors.joining());
      String packageName = fileDescProto.getPackage();

      for (DescriptorProtos.DescriptorProto descriptorProto : fileDescProto.getMessageTypeList()) {
        String name = descriptorProto.getName();
        if (Arrays.stream(MESSAGE_TYPE_START_WITH_LIST.split("\\s*, \\s*")).anyMatch(name::startsWith)) {
          try {
            String fullName = packageName + "." + name;
            String hashedName =  getMD5(fullName);
            String classNameWithHash = packageName + "." + className + "$" + name;
            hash2IdClassTypeHashMap.put(hashedName, classNameWithHash);
          } catch (NoSuchAlgorithmException e) {
            throw new RuntimeException(e);
          }
        }
      }
    }

    return hash2IdClassTypeHashMap;
  }

  private static String getMD5(String input) throws NoSuchAlgorithmException {
    // getInstance() method is called with algorithm MD5
    // nosemgrep: java.lang.security.audit.crypto.use-of-md5.use-of-md5
    MessageDigest md = MessageDigest.getInstance("MD5");

    // digest() method is called to calculate message digest of the input string
    byte[] messageDigest = md.digest(input.getBytes());

    // Convert byte array into signum representation and finally to hex value
    final int SIGNUM = 1;
    final int RADIX = 16;
    return new BigInteger(SIGNUM, messageDigest).toString(RADIX);
  }

}
