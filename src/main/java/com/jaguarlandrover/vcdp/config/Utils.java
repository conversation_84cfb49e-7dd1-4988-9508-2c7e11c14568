/*
 * Copyright (c) Jaguar Land Rover Ltd 2023. All rights reserved
 */

package com.jaguarlandrover.vcdp.config;

import com.amazonaws.services.kinesisanalytics.runtime.KinesisAnalyticsRuntime;
import com.twitter.chill.protobuf.ProtobufSerializer;
import gateway_service.Envelope;
import java.util.Locale;
import java.util.Map;
import java.util.Objects;
import java.util.Properties;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import ngtp_adpt_service.NgtpAdapterService;
import org.apache.flink.connector.kafka.source.enumerator.initializer.OffsetsInitializer;
import org.apache.flink.streaming.api.environment.LocalStreamEnvironment;
import org.apache.flink.streaming.api.environment.StreamExecutionEnvironment;
import someip_adpt_service.SomeipAdapterService;


@Slf4j
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class Utils {
  private static final String ERROR_MESSAGE_TYPE = "Data-engine-preprocessor, error_type=app-error ";

  public static StreamExecutionEnvironment  configureEnvironment() {

    final long checkpointInterval = 5000L;
    final StreamExecutionEnvironment sEnv = StreamExecutionEnvironment.getExecutionEnvironment();
    sEnv.getConfig().enableObjectReuse();
    sEnv.getConfig().registerTypeWithKryoSerializer(Envelope.class, ProtobufSerializer.class);
    sEnv.getConfig().registerTypeWithKryoSerializer(NgtpAdapterService.class, ProtobufSerializer.class);
    sEnv.getConfig().registerTypeWithKryoSerializer(SomeipAdapterService.class, ProtobufSerializer.class);

    sEnv.enableCheckpointing(checkpointInterval); // overridden by KDA environment variables
    return sEnv;
  }

  public static Map<String, Properties> initPropertiesMap(StreamExecutionEnvironment sEnv,
                                                          String propertiesFile)  {
    Map<String, Properties> applicationPropertiesMap = null;
    try {
      if (sEnv instanceof LocalStreamEnvironment) {
        applicationPropertiesMap = KinesisAnalyticsRuntime
            .getApplicationProperties(Objects.requireNonNull(Thread.currentThread().getContextClassLoader()
                    .getResource(propertiesFile))
                .getPath());
        log.info("Reading Properties from resource folder..");
      } else {
        applicationPropertiesMap = KinesisAnalyticsRuntime.getApplicationProperties();
        log.info("Reading Properties from KDA..");
      }
    } catch (Exception e) {
      log.error("{}error_message=PropertiesFile (not found/reading) exception: {}",
              ERROR_MESSAGE_TYPE,
              e.getMessage());
    }
    return applicationPropertiesMap;
  }

  public static OffsetsInitializer getOffsetStrategy(String offsetStrategy) {
    switch (offsetStrategy.toUpperCase(Locale.ENGLISH)) {
      case "LATEST_OFFSET":
        return OffsetsInitializer.latest();
      case "EARLIEST_OFFSET":
        return OffsetsInitializer.earliest();
      default:
        log.info("Unknown offset strategy: " + offsetStrategy + ". Defaulting to the latest offset strategy");
        return OffsetsInitializer.latest();
    }
  }

  public static Properties getKafkaSaslProperties(StreamExecutionEnvironment sEnv) {
    Properties saslProperties = new Properties();
    if (sEnv instanceof LocalStreamEnvironment) {
      return saslProperties;
    }
    saslProperties.put("security.protocol", "SASL_SSL");
    saslProperties.put("sasl.mechanism", "AWS_MSK_IAM");
    saslProperties.put("sasl.jaas.config", "software.amazon.msk.auth.iam.IAMLoginModule required;");
    saslProperties.put("sasl.client.callback.handler.class",
        "software.amazon.msk.auth.iam.IAMClientCallbackHandler");
    return saslProperties;
  }

  public static String anonymizeString(String input, int startIndex) {
    if (input != null) {
      StringBuilder anonymized = new StringBuilder(input);
      for (int i = startIndex; i < input.length(); i++) {
        anonymized.setCharAt(i, '*');
      }
      return anonymized.toString();
    } else {
      return null;
    }
  }

}
