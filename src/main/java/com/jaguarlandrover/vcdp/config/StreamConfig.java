/*
 * Copyright (c) Jaguar Land Rover Ltd 2023. All rights reserved
 */

package com.jaguarlandrover.vcdp.config;

public final class StreamConfig {
  // Kafka Properties
  public static final String KAFKA_SOURCE_GROUP_ID_KEY = "kafka.config";
  public static final String KAFKA_INPUT_BOOTSTRAP_SERVERS = "kafka.vcdp.bootstrap.servers";
  public static final String KAFKA_OUTPUT_BOOTSTRAP_SERVERS = "kafka.bootstrap.servers";
  public static final String KAFKA_TOPIC_INGEST_INIT_POSITION = "kafka.topic.ingest.initial.position";
  public static final String KAFKA_TOPIC_PROTOBUF_IN_NAME = "kafka.topic.ingest.name";
  public static final String KAFKA_TOPIC_JSON_OUT_NAME = "kafka.topic.out.name";
  public static final Integer BUFFER_OUT_TIME = 5;
  public static final String MAPPING_JSON_TRANSFORMATION = "mappingFiles/transformationMapping.json";
  public static final String PROTO_DESC_DIRECTORY = "/protoDescriptors";
  public static final String MESSAGE_TYPE_START_WITH_LIST = "Notify, Get";

  private StreamConfig() {}

}
