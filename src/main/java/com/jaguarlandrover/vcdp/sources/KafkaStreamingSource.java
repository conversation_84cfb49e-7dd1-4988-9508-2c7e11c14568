/*
 * Copyright (c) Jaguar Land Rover Ltd 2023. All rights reserved
 */

package com.jaguarlandrover.vcdp.sources;

import static com.jaguarlandrover.vcdp.config.StreamConfig.*;
import static com.jaguarlandrover.vcdp.config.Utils.getKafkaSaslProperties;
import static com.jaguarlandrover.vcdp.config.Utils.getOffsetStrategy;

import com.alibaba.fastjson2.JSONObject;
import com.jaguarlandrover.vcdp.serde.MsgGatewayProtobufDeserializer;
import java.util.Map;
import java.util.Properties;
import org.apache.flink.connector.kafka.source.KafkaSource;
import org.apache.flink.streaming.api.environment.StreamExecutionEnvironment;


public class KafkaStreamingSource {
  private KafkaStreamingSource() {}

  // Kafka source - Ingest
  public static KafkaSource<JSONObject> getKafkaProtobufToJsonObjectSource(
      StreamExecutionEnvironment sEnv,
      Properties propertiesMap,
      Map<String, String> hashMapping) {
    return KafkaSource.<JSONObject>builder()
            .setBootstrapServers(propertiesMap.getProperty(KAFKA_INPUT_BOOTSTRAP_SERVERS))
            .setTopics(propertiesMap.getProperty(KAFKA_TOPIC_PROTOBUF_IN_NAME))
            .setStartingOffsets(getOffsetStrategy(propertiesMap.getProperty(KAFKA_TOPIC_INGEST_INIT_POSITION)))
            .setProperties(getKafkaSaslProperties(sEnv))
            .setDeserializer(new MsgGatewayProtobufDeserializer(hashMapping))
            .build();
  }
}
