/*
 * Copyright (c) Jaguar Land Rover Ltd 2023. All rights reserved
 */

package com.jaguarlandrover.vcdp.serde;

import com.alibaba.fastjson2.JSONObject;
import com.google.protobuf.ByteString;
import com.google.protobuf.GeneratedMessage;
import com.google.protobuf.util.JsonFormat;
import gateway_service.Envelope;
import java.lang.reflect.InvocationTargetException;
import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.Map;
import lombok.extern.slf4j.Slf4j;
import org.apache.flink.api.common.typeinfo.TypeInformation;
import org.apache.flink.connector.kafka.source.reader.deserializer.KafkaRecordDeserializationSchema;
import org.apache.flink.util.Collector;
import org.apache.kafka.clients.consumer.ConsumerRecord;


@Slf4j
public class MsgGatewayProtobufDeserializer implements KafkaRecordDeserializationSchema<JSONObject> {
  private static final long serialVersionUID = 1;
  private static final String ERROR_MESSAGE_TYPE = "Data-engine-preprocessor, error_type=deserialization-error ";
  private Map<String, String> hashMapping = new HashMap<>();

  public MsgGatewayProtobufDeserializer(Map<String, String> inputJson) {
    hashMapping = inputJson;
  }

  @Override
  public void deserialize(ConsumerRecord<byte[], byte[]> kafkaProtobufRecord, Collector<JSONObject> out) {
    if (kafkaProtobufRecord.value() == null || kafkaProtobufRecord.key() == null) {
      log.error("{}error_message= Message key or value should not be null", ERROR_MESSAGE_TYPE);
      return;
    }
    long startTimestamp = System.currentTimeMillis();
    String key = extractKeyFromRecord(kafkaProtobufRecord);
    try {

      Envelope.GatewayResponseMessage decodedMsg = Envelope.GatewayResponseMessage.parseFrom(kafkaProtobufRecord.value());

      JSONObject result = processMessage(decodedMsg, key);

      if (result != null) {
        result.put("start_deserialisation_timestamp", startTimestamp);
        result.put("timestamp_ms", decodedMsg.getTimestampMs());
        out.collect(result);
      }

    } catch (IllegalAccessException | NoSuchMethodException | InvocationTargetException | RuntimeException e) {
      log.error("{}error_message= Invalid protobuf message for key {} -- {}",
              ERROR_MESSAGE_TYPE,
              new String(kafkaProtobufRecord.key(), StandardCharsets.UTF_8).strip(),
              e.getMessage());

    } catch (Exception e) {
      log.error("{}error_message= Exception for the key {} -- {}",
              ERROR_MESSAGE_TYPE,
              new String(kafkaProtobufRecord.key(), StandardCharsets.UTF_8).strip(),
              e.getMessage());
    }
  }

  private String extractKeyFromRecord(ConsumerRecord<byte[], byte[]> kafkaProtobufRecord) {
    String key =  new String(kafkaProtobufRecord.key(), StandardCharsets.UTF_8).strip();
    String[] keyParts = key.split("/", 3);
    if (keyParts.length > 1) {
      key = keyParts[1];
    }
    return key;
  }


  private JSONObject processMessage(Envelope.GatewayResponseMessage decodedMsg, String key) throws Exception {
    // to parse both Notify and GerResponse message, use java method overloading to parse the input:
    // - Depending on whether it has ResponseMessage or EventMessage,
    // - we provide the corresponding type of parameters to the processEnvelopeMessage function.
    if (decodedMsg.hasResponseMessage() && decodedMsg.hasTimestampMs()) {
      return processEnvelopeMessage(decodedMsg.getResponseMessage(), key);
    } else if (decodedMsg.hasEventMessage() && decodedMsg.hasTimestampMs()) {
      return processEnvelopeMessage(decodedMsg.getEventMessage(), key);
    } else {
      log.error("{}error_message= Message format is invalid for the key {}", ERROR_MESSAGE_TYPE, key);
      return null;
    }
  }

  private JSONObject processEnvelopeMessage(Envelope.ResponseMessage message, String key) throws Exception {
    return processMessageParsePayload(message.getHash(), message.getMessagePayload(), key);
  }

  private JSONObject processEnvelopeMessage(Envelope.EventMessage message, String key) throws Exception {
    return processMessageParsePayload(message.getHash(), message.getMessagePayload(), key);
  }

  private JSONObject processMessageParsePayload(String hash, ByteString messagePayload, String key) throws Exception {
    try {
        if (!hash.isEmpty() && !messagePayload.isEmpty()) {
          JSONObject result = new JSONObject();
          result.put("hash", hash);
          result.put("unique_id", key.strip());
          String messageTypeClassName = hashMapping.get(hash);

          Class<?> cls = (Class<?>) Class.class.getDeclaredMethod("forName", String.class)
                  .invoke(null, messageTypeClassName);
          GeneratedMessage enumMessageObject = (GeneratedMessage) cls.getMethod("parseFrom", byte[].class)
                  .invoke(null, (Object) messagePayload.toByteArray());

          String resultEnumMessageString = JsonFormat.printer().preservingProtoFieldNames().print(enumMessageObject);
          JSONObject resultEnumMessageJson = JSONObject.parseObject(resultEnumMessageString);

          result.put("payload", resultEnumMessageJson);

          return result;
        }
    } catch (IllegalAccessException | NoSuchMethodException | InvocationTargetException | RuntimeException e) {
      log.error("{}error_message= issue parsing the payload for the key {}, hash {}: {}",
              ERROR_MESSAGE_TYPE,
              key,
              hash,
              e.getMessage());
    }
    return null;
  }

  @Override
  public TypeInformation<JSONObject> getProducedType() {
    return TypeInformation.of(JSONObject.class);
  }

}
