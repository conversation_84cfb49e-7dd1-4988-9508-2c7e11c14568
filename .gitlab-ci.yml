variables:
  BASE_JAVA_JOB_IMAGE: maven:3.9.3-amazoncorretto-11
  MAVEN_OPTS: "-Dhttps.protocols=TLSv1.2 -Dmaven.repo.local=$CI_PROJECT_DIR/.m2/repository -Dorg.slf4j.simpleLogger.log.org.apache.maven.cli.transfer.Slf4jMavenTransferListener=WARN -Dorg.slf4j.simpleLogger.showDateTime=true -Djava.awt.headless=true"
  MAVEN_CLI_OPTS: "--batch-mode --errors --fail-at-end --show-version -DinstallAtEnd=true -DdeployAtEnd=true"
  region:
    value: "eu-west-2"
    description: "Which region it has to be deployed"
    options:
      - "eu-west-2"
      - "cn-northwest-1"
  image_registry:
    value: "557068259488.dkr.ecr.eu-west-2.amazonaws.com/docker-hub/library/"
    description: "This variable is mandatory when 'region' variable value is 'cn-northwest-1'"
    options:
      - "068946021388.dkr.ecr.cn-northwest-1.amazonaws.com.cn/"
      - "557068259488.dkr.ecr.eu-west-2.amazonaws.com/docker-hub/library/"
  control_role:
    value: "pipelines-data-engine-preprocessor-role"
    description: "Control role"
    options:
      - "pipelines-data-engine-preprocessor-role"
  aws_handel:
    value: "aws"
    description: "Aws handel"
    options:
      - "aws-cn"
      - "aws"
  image:
    value: "557068259488.dkr.ecr.eu-west-2.amazonaws.com/aws-infrastructure-ci:adb259aec713cb4d69e1c8cb723946d9994835a5"
    description: "Base default image"
    options:
      - "068946021388.dkr.ecr.cn-northwest-1.amazonaws.com.cn/aws-infrastructure-ci:e274bc4aaaf5a6cbf5973d7390284e99bd70c968"
      - "557068259488.dkr.ecr.eu-west-2.amazonaws.com/aws-infrastructure-ci:adb259aec713cb4d69e1c8cb723946d9994835a5"


include:
  - local: 'workflow.yml'
  - project: 'd9/infrastructure/gitlab-ci-templates'
    ref: master
    file: '/security/base-sec.yml'
  - project: 'd9/infrastructure/gitlab-ci-templates'
    ref: master
    file: '/security/java-sec.yml'

default:
  tags:
    - ${tagref}
  image:
    name: $image

.ssh_auth: &ssh_auth
  - mkdir -p ~/.ssh
  - chmod 700 ~/.ssh
  - echo "$deploy_key_rw" | tr -d '\r' > ~/.ssh/deploy.key
  - chmod 600 ~/.ssh/deploy.key
  - echo "$ssh_known_hosts" >> ~/.ssh/known_hosts
  - chmod 644 ~/.ssh/known_hosts
  - echo -e "Host ${CI_SERVER_HOST}\n  UpdateHostKeys no\n  PreferredAuthentications publickey\n  IdentityFile ~/.ssh/deploy.key\n" > ~/.ssh/config
  - chmod 600 ~/.ssh/config


stages:
  - preliminary
  - build-jar
  - install
  - tests
  - validate # validate is required for included stages from security/base-sec.yml
  - inspect-artifact
  - inspect code
  - git-tag
  - deploy
  - infra

######################
# BUILD DEPENDENCIES #
######################

install_dependencies:
  stage: install
  image: ${image_registry}${BASE_JAVA_JOB_IMAGE}
  cache:
    key: $CI_COMMIT_REF_SLUG-$CI_PROJECT_DIR
    paths:
      - .m2/repository
      - cache/Cypress
      - .cache/Cypress
      - node_modules/
  before_script:
    - >
      if [[ -f package.json ]]; then
        apt update && apt install -y nodejs npm
      fi
  script:
    - >
      if [[ $region == "cn-northwest-1" ]]; then
        cat $MAVEN_SETTINGS_PATH_CN > $MAVEN_SETTINGS_PATH
        export MAVEN_SETTINGS_PATH
        echo "MAVEN_SETTINGS_PATH=${MAVEN_SETTINGS_PATH}" >> mvn_pth.env
      fi
    - echo $MAVEN_SETTINGS_PATH
    - echo $MAVEN_CLI_OPTS
    - mvn $MAVEN_CLI_OPTS clean install -D skipTests -D dependency-check.skip=true --gs $MAVEN_SETTINGS_PATH
    - >
      if [[ -f package.json ]]; then
        npm ci
      fi
  artifacts:
    reports:
      dotenv: mvn_pth.env

############
# VALIDATE #
############

validate:
  stage: validate
  image: ${image_registry}${BASE_JAVA_JOB_IMAGE}
  dependencies:
    - install_dependencies
  cache:
    key: $CI_COMMIT_REF_SLUG-$CI_PROJECT_DIR
    paths:
      - .m2/repository
    policy: pull
  script:
    - mvn $MAVEN_CLI_OPTS validate --gs $MAVEN_SETTINGS_PATH

tests:
  stage: validate
  image: ${image_registry}${BASE_JAVA_JOB_IMAGE}
  dependencies:
    - install_dependencies
  cache:
    key: $CI_COMMIT_REF_SLUG-$CI_PROJECT_DIR
    paths:
      - .m2/repository
    policy: pull
  script:
    - mvn $MAVEN_CLI_OPTS test verify -D dependency-check.skip=true --gs $MAVEN_SETTINGS_PATH
  artifacts:
    when: always
    paths:
      - target/coverage-reports/
      - target/surefire-reports/
      - target/failsafe-reports
      - target/classes/
      - target/pacts/
      - target/site/
  # In order for 'docker in docker' to work, the below variables & services are needed.
  variables:
    DOCKER_HOST: tcp://docker:2375
    DOCKER_DRIVER: overlay2
    DOCKER_TLS_CERTDIR: ""
  services:
    - name: public.ecr.aws/docker/library/docker:20-dind
      alias: docker
      command: [ "--tls=false" ]

build:
  stage: validate
  image: ${image_registry}${BASE_JAVA_JOB_IMAGE}
  dependencies:
    - install_dependencies
  cache:
    key: $CI_COMMIT_REF_SLUG-$CI_PROJECT_DIR
    paths:
      - .m2/repository
    policy: pull
  script:
    - mvn $MAVEN_CLI_OPTS package -D skipTests --gs $MAVEN_SETTINGS_PATH
  artifacts:
    paths:
      - target/

trivy:
  # Modified from trivy job included from security/base-sec.yml
  # Reason: to operate in correct pipeline stages
  stage: inspect-artifact
  # reset to default rules - original job only runs on master/dev
  rules:
    - when: on_success

fetch_infra_tags:
  stage: preliminary
  #  image: alpine/git:latest
  script:
    - *ssh_auth
    - apt-get update && apt-get install -y git
    - git clone git@${CI_SERVER_HOST}:D9/data-engineering/infra/data-engine-preprocessor-infra.git
    - cd data-engine-preprocessor-infra
    - if [ "$CI_PIPELINE_SOURCE" == "merge_request_event" ]; then CI_COMMIT_BRANCH="$CI_MERGE_REQUEST_SOURCE_BRANCH_NAME"; fi
    - echo "${CI_COMMIT_BRANCH}"
    - if [ ! -z  `git branch -r | grep ${CI_COMMIT_BRANCH} ` ]; then   if [[ "${CI_COMMIT_BRANCH}" =~ ^fc.* ]]; then version_to_use=`git tag -l ${CI_COMMIT_BRANCH}-[0-9]*.[0-9]*.[0-9]* --sort=-creatordate | head -n 1`; elif [[ "${CI_COMMIT_BRANCH}" =~ ^rc.* ]]; then  version_to_use=`git tag -l ${CI_COMMIT_BRANCH}-[0-9]*.[0-9]*.[0-9]* --sort=-creatordate | head -n 1`; fi;  fi
    - if [[ -z ${version_to_use} ]]; then version_to_use=`git tag -l v* --sort=-creatordate | head -n 1`; fi
    - if [[ -z $version_to_use ]]; then echo "Couldn't find the version to use. Please have a check"; exit 1; fi
    - echo "Version to use for use case infra will be ${version_to_use}"
    - echo "use_case_infra_tag=${version_to_use}" >> infra_tags.env
    - mv infra_tags.env $CI_PROJECT_DIR/infra_tags.env
  artifacts:
    when: always
    reports:
      dotenv: $CI_PROJECT_DIR/infra_tags.env

generate-version-number:
  #  image: alpine/git:latest
  stage: preliminary
  script:
    - *ssh_auth
    - git config --global user.email "<EMAIL>"
    - git config --global user.name "deployer"
    - mkdir tagger_dir
    - cd tagger_dir
    - git clone "git@${CI_SERVER_HOST}:D9/data-engineering/dev/data-engine-preprocessor.git"
    - cd data-engine-preprocessor
    - git checkout ${CI_COMMIT_BRANCH}
    - if [[ "${CI_COMMIT_BRANCH}" =~ ^fc.* ]]; then previous_version=`git tag -l ${CI_COMMIT_BRANCH}-[0-9]*.[0-9]*.[0-9]* --sort=-creatordate | head -n 1`; elif [[ "${CI_COMMIT_BRANCH}" =~ ^rc.* ]]; then  previous_version=`git tag -l ${CI_COMMIT_BRANCH}-[0-9]*.[0-9]*.[0-9]* --sort=-creatordate | head -n 1`; elif [[ ${CI_COMMIT_BRANCH} == ${CI_DEFAULT_BRANCH} ]]; then  previous_version=`git tag -l v* --sort=-creatordate | head -n 1`; fi
    - if [[ "${CI_COMMIT_BRANCH}" =~ ^fc.* ]]; then if [[ -z $previous_version ]]; then  new_version="${CI_COMMIT_BRANCH}-0.0.1"; else new_version=`echo "${previous_version}" | awk -F. -v OFS=. '{$3 += 1 ; print}'`; fi elif [[ "${CI_COMMIT_BRANCH}" =~ ^rc.* ]]; then if [[ -z $previous_version ]]; then  new_version="${CI_COMMIT_BRANCH}-0.1.0"; else new_version=`echo "${previous_version}" | awk -F. -v OFS=. '{$2 += 1 ; print}'`; fi elif [[ "${CI_COMMIT_BRANCH}" == ${CI_DEFAULT_BRANCH} ]]; then if [[ -z $previous_version ]]; then  new_version="v1.0.0"; else new_version=v`echo "${previous_version:1}" | awk -F. -v OFS=. '{$1 += 1 ; print}'`; fi fi
    - echo ${new_version}
    - echo "git_tag_number=${new_version}" >> variables.env
  artifacts:
    when: always
    reports:
      dotenv: $CI_PROJECT_DIR/tagger_dir/data-engine-preprocessor/variables.env

sonarqube:
  stage: inspect code
  variables:
    BASE_JAVA_JOB_IMAGE: maven:3.8.1-openjdk-17-slim
  dependencies:
    - tests
    - generate-version-number
    - install_dependencies
  script:
    - echo "git_tag_number ${git_tag_number}"
    - mvn $MAVEN_CLI_OPTS sonar:sonar -Dsonar.projectKey="$SONAR_PROJECT_KEY" -Dsonar.login="$SONAR_TOKEN" -Dsonar.qualitygate.wait=true -Dcheckstyle.skip  -Ddependency-check.skip=true --gs $MAVEN_SETTINGS_PATH -Dsonar.projectVersion=${git_tag_number}
  rules:
    - if: ($CI_PIPELINE_SOURCE == "merge_request_event")
      when: never
    - when: on_success

generate-git-tag:
  #  image: alpine/git:latest
  stage: git-tag
  needs:
    - job: sonarqube
      optional: True
    - job: install_dependencies
    - job: build
    - job: tests
    - job: validate
    - job: generate-version-number
  script:
    - *ssh_auth
    - git config --global user.email "<EMAIL>"
    - git config --global user.name "deployer"
    - mkdir tagger_dir
    - cd tagger_dir
    - git clone "git@${CI_SERVER_HOST}:D9/data-engineering/dev/data-engine-preprocessor.git"
    - cd data-engine-preprocessor
    - git checkout ${CI_COMMIT_BRANCH}
    - echo ${git_tag_number}
    - git tag -a ${git_tag_number} -m "Tag generated on CI pipeline"
    - git push --tags -o ci.skip
  rules:
    - if: ($CI_PIPELINE_SOURCE == "merge_request_event")
      when: never
    - when: on_success

deploy-to-s3:
  stage: deploy
  needs:
    - job: generate-git-tag
      optional: true
    - job: install_dependencies
    - job: build
    - job: tests
    - job: validate
    - job: generate-version-number
      artifacts: true
  #  image: amazon/aws-cli
  script:
    - aws_credentials=$(aws sts assume-role --role-arn arn:${aws_handel}:iam::${acc_id}:role/${control_role} --role-session-name mirror-images --duration-seconds 3600 --output json)
    - echo arn:${aws_handel}:iam::${acc_id}:role/${control_role}
    - export AWS_ACCESS_KEY_ID=$(jq '.Credentials.AccessKeyId' <<< $aws_credentials | tr -d '"')
    - export AWS_SECRET_ACCESS_KEY=$(jq '.Credentials.SecretAccessKey' <<< $aws_credentials | tr -d '"')
    - export AWS_SESSION_TOKEN=$(jq '.Credentials.SessionToken' <<< $aws_credentials | tr -d '"')
    - echo ${git_tag_number}
    - mkdir $CI_PROJECT_DIR/data-engine-preprocessor-package
    - jar_name=data-engine-preprocessor-${git_tag_number}.jar
    - echo ${jar_name}
    - cp $CI_PROJECT_DIR/target/data-engine-preprocessor*.jar $CI_PROJECT_DIR/data-engine-preprocessor-package/${jar_name}
    - aws s3 cp $CI_PROJECT_DIR/data-engine-preprocessor-package/${jar_name} s3://${TF_BUCKET_RELEASES}/data-engine-preprocessor/
    - echo "data_engine_preprocessor_sink_artifact_path=s3://${TF_BUCKET_RELEASES}/data-engine-preprocessor/${jar_name}" >> data_engine_preprocessor_sink_file_path.env
    - echo "jar_file_name=${jar_name}" > data_engine_preprocessor_sink_file_path.env
  artifacts:
    when: always
    reports:
      dotenv: $CI_PROJECT_DIR/data_engine_preprocessor_sink_file_path.env
  rules:
    - if: ($CI_PIPELINE_SOURCE == "merge_request_event")
      when: never
    - when: on_success

trigger-use-case-infra:
  stage: infra
  needs:
    - job: deploy-to-s3
    - job: generate-version-number
    - job: fetch_infra_tags
  variables:
    application_name: data-engine-preprocessor
    jar_file_name: ${jar_file_name}
    change_in_log: Y
    tag_skip_flag: Y
    release_is_minor: N
    new_deployment: Y
    triggering_branch: $CI_COMMIT_REF_NAME
  trigger:
    project: 'D9/data-engineering/infra/data-engine-preprocessor-infra'
    branch: ${use_case_infra_tag}
    strategy: depend
    forward:
      pipeline_variables: true

  rules:
    - if: ($CI_PIPELINE_SOURCE == "merge_request_event")
      when: never
    - when: manual


