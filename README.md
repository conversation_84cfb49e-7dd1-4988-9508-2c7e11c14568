# VCDP Data Engine Preprocessor

Flink Streaming Application - Consume, deserialize, reformat to VCDP format, write to a output kafka topic

## Application overview

The VCDP Data Engine Preprocessor Streaming Application is engineered to handle incoming protobuf data streams, performing real-time deserialization and conversion into the VA Format.
It is built with an emphasis on:
- **Scalability:** Capable of handling increasing volumes of data without compromising performance.
- **Reliability:** Ensures consistent processing and uptime.
- **Low-Latency Processing:** Minimizes delay, enabling immediate data utilization.
This application empowers our data team to leverage notify data effectively.

## Feature overview
The VCDP Data Engine Preprocessor Streaming Application boasts a suite of features designed to optimize data stream processing:

* **Real-Time Deserialization**: Instantly converts incoming protobuf data streams into a usable format.
* **VA Format Conversion**: Seamlessly transforms deserialized data into the VA Format for immediate processing in Data-Engine-Core application.
* **User-Friendly Configuration**: Simplifies setup with clear instructions and automated processes for ease of use.

## Contents
[[_TOC_]]
### Architecture
![img.png](img.png)

### Business logic

#### Deserialization
This process involves consuming records from vehicles present in the Kafka topic `LIVE.data.product.notify.input`. 
The data arrives in a binary protobuf serialized format. Deserialisation occurs in two distinct stages using the provided Proto files:

1. **Stage 1 - Envelope Message Deserialisation**: The Envelope message acts as a ‘wrapper’ for the Notify messages. It will be deserialized first:
    * Sample Envelope Message
    ```
            {
            "event_message": {
                "id":  "ac4e29a8952bf00168d7a625352458f2",
                "message_payload": [  8, 2, 16, 2, 24, 1  ]
                },
            "timestamp_ms": 1704447980000
            }
    ```
   The `id` field is the MD5 hash of “service.message” which determines the type of Notify message serialized in `message_payload`. 
   * Sample **id** filed hashing<br>
   ```MD5("ngtp_adpt_service/NotifyApertureLockState")= "ac4e29a8952bf00168d7a625352458f2"```
    

2. **Stage 2 - Notify Payload Message Deserialisation:**: The Notify Payload message will be deserialised using the message type defined in the id field.
   * Sample Payload Message
    ```
    {
             "aperture_status": "ENUM_APERTURE_STATUS_ALL_APERTURES_EXTERIOR_LOCKED",
             "individual_aperture_status": 2,
             "status": "ENUM_STATUS_OK"
             }
    ```
      After deserialisation, both parts are combined and forwarded to the Data Format Conversion function.
   * Sample Deserlised Message
    ```    
               {
               "event_message": {
                   "id": "ac4e29a8952bf00168d7a625352458f2",
                   "message_payload": {
                                   "aperture_status": "ENUM_APERTURE_STATUS_ALL_APERTURES_EXTERIOR_LOCKED",
                                   "individual_aperture_status": 2,
                                   "status": "ENUM_STATUS_OK"
                                   }
                   },
               "timestamp_ms": 1704447980000
               }
    ```
##### Data format conversion
The deserialised messages from the previous step are converted into the VA Format. This involves iterating through the `message_payload` and mapping it to elements in `**_TransformationMapping.json_**.
- Mapping is initiated by identifying the corresponding element in **_TransformationMapping.json_** that matches the `id`.
  * Sample TransformationMapping.json record
    ```
    {
             "ac4e29a8952bf00168d7a625352458f2": {
                "query_id": "Q7302001",
                "data_id_mappings": {
                   "aperture_status": {
                      "id": "NGTP-ApertureLockState",
                      "type": "DATA_STRING"
                   },
                   "individual_aperture_status": {
                      "id": "NGTP-IndividualApertureState",
                      "type": "DATA_FLOAT"
                   }
                }
             }..
          }
    ```
- If the `status` is "ENUM_STATUS_OK", the process proceeds; otherwise, an error is logged.
- For each element in `message_payload`, the corresponding element in **_TransformationMapping.json_** is used to format a **sample** in the VA Format message.
  - The timestamp from the source message is use for both the `event_timestamp_ms` and **samples** `timestamp_ms`
  - The `query_id` from **_TransformationMapping.json_** is used in the **VA Format** message
  - `unique_id` is extracted from the key of the kafka message during Deserialisation.
  * Sample of the generated FA Format Message
    ```
    {
               "unique_id": "UUID-3567",
               "query_id": "Q7302001",
               "data": [
                  {
                     "data_id": "NGTP-ApertureLockState",
                     "samples": [
                        {
                           "timestamp_ms": 1704447980000,
                           "type": "DATA_STRING",
                           "value": "ENUM_APERTURE_STATUS_ALL_APERTURES_EXTERIOR_UNLOCKED"
                        }
                     ]
                  },
                  {
                     "data_id": "NGTP-IndividualApertureState",
                     "samples": [
                        {
                           "timestamp_ms": 1704447980000,
                           "type": "DATA_FLOAT",
                           "value": 2
                        }
                     ]
                  }
               ],
               "event_timestamp_ms": 1704447979000,
               "fleet_id": ""
            }
    ```
    
Once the data is formatted, it will be published to the output topic `LIVE.data-product-factory.input` in the MSK Cluster `vcdp-data-engineering-msk`.




#### Configuration

To ensure the application runs smoothly, the following resources must be configured prior to execution:

- **Proto Files:**<br> 
  The `.proto` files, which outline the structure of the serialized data messages, should be placed within the `resources` directory.
  The version of the Proto Files used is base on [Release 11.0.0](https://git-gdd.sdo.jlrmotor.com/ONE/dm1/eth-soa-rel/-/releases/11.0.0)
- **TransformationMap.json:**<br> 
  The `transformationMap.json` file, crucial for mapping deserialized messages to the `VA Format` during the Data Format 
  Conversion step, must also be present in the resources directory.
  
When the AWS Managed Flink Service initiates the Maven build process and loads the JAR file, it will automatically
extract classes and descriptors from the .proto files. This is a critical step as it generates the Java classes
necessary for deserialization.

During the initial run of the application, a hashmap is created. This hashmap links the class names from the messages
in the .proto files (utilizing generated `descriptors` files) to their corresponding MD5 hashes in `transformationMap.json`.

Additionally, the AWS Managed Flink Service application requires configuration of several runtime variables:
1. `kafka.topic.ingest.name` - Topic name of main stream data (Protobuf Deserialized message). 
2. `kafka.bootstrap.servers` - Data engineering bootstrap servers
3. `kafka.topic.ingest.initial.position` - Start up mode for ingest topic
4. `kafka.topic.out.name` - Output kafka topic name (FA Format messages)
5. `kafka.vcdp.bootstrap.servers` - VCDP bootstrap servers




### Tech Stack used

#### Java

* Version - 11

#### Flink

* Version - 1.18
* Flink API used - Java Data Stream API

#### Kafka

In our environment we have 2 clusters (Production configurations)

1. vcdp-data-engineering-msk
   * Version - 2.8.1
   * Number of brokers - 6

2. production-msk
   * Version - 3.5.1
   * Number of brokers - 3

### Sources

We need One source in `vcdp-data-engineering-msk`	cluster for the pipeline to be working fine:
- Main data payload from VA `LIVE.data.product.notify.input` topic.

### Output

This application will publish the data to an output kafka topic `LIVE.data-product-factory.input` which is in `production-msk` cluster.

1. LIVE.data-engine.output

### Testing
To run the Unit Tests execute the following command from the project root:
```shell 
mvn test verify -D dependency-check.skip=true
```

- **Test Coverage**
    We are using the JLR Sonarqube for quality gate.
    Please refer [DynamoDbSonar](https://sonarqube.dev.jlr-vcdp.com/dashboard?id=d9-data-engineering-flink-java-data-engine-core&branch=fc_DDA-33357_Flink-Custom-Metrics) for more detailed information.

### Git lab CICD
  We are using `.gitlab-ci.yml` to perform continuous integration and `terraform` to do continuous deployment in all aws environment.
  Please refer sample [pipeline](https://git-gdd.sdo.jlrmotor.com/D9/data-engineering/flink-java/data-engine-core/-/pipelines/1214985) for more details.
  For `high availability` we developed custom [ZDD](https://confluence.devops.jlr-apps.com/display/VCDP/Zero+Downtime+Deployment) implementation.
