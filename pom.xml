<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
		 xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>

	<groupId>com.jaguarlandrover.vcdp</groupId>
	<artifactId>data-engine-preprocessor</artifactId>
	<version>0.1</version>
	<packaging>jar</packaging>

	<name>Data Engine Preprocessor</name>
	<url>https://flink.apache.org</url>

	<properties>
		<project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
		<flink.version>1.18.1</flink.version>
		<target.java.version>1.11</target.java.version>
		<maven.compiler.source>${target.java.version}</maven.compiler.source>
		<maven.compiler.target>${target.java.version}</maven.compiler.target>
		<log4j.version>2.23.1</log4j.version>
		<kda.runtime.version>1.2.0</kda.runtime.version>
		<kda.connectors.version>2.1.0</kda.connectors.version>
		<kafka.version>3.6.1</kafka.version>
		<kafka.junit.version>3.6.0</kafka.junit.version>
		<flink-kafka.version>3.1.0-1.18</flink-kafka.version>
		<lombok.version>1.18.32</lombok.version>
		<jdk.version>11</jdk.version>
		<aws-msk-iam-auth.version>2.0.3</aws-msk-iam-auth.version>
		<fastjson2.version>2.0.51</fastjson2.version>
		<maven-compiler-plugin.version>3.8.1</maven-compiler-plugin.version>
		<maven-shade-plugin.version>3.2.1</maven-shade-plugin.version>
		<maven.jacoco.version>0.8.8</maven.jacoco.version>
		<scala.binary.version>2.12</scala.binary.version>
		<flink.format.parquet.version>1.12.2</flink.format.parquet.version>
		<scala.binary.version>2.12</scala.binary.version>
		<protobuf.java.version>4.28.2</protobuf.java.version>
		<protobuf.maven.plugin.version>3.11.4</protobuf.maven.plugin.version>
		<testcontainers.version>1.19.4</testcontainers.version>
		<junit-jupiter.version>5.10.1</junit-jupiter.version>
		<sonar.exclusions>
			src/main/java/gateway_service/Envelope.java,
			src/main/java/ngtp_adpt_service/NgtpAdapterService.java,
			src/main/java/someip_adpt_service/SomeipAdapterService.java,
			src/main/java/com/jaguarlandrover/vcdp/config/ConfigLoader.java
		</sonar.exclusions>
	</properties>

	<repositories>
		<repository>
			<id>apache.snapshots</id>
			<name>Apache Development Snapshot Repository</name>
			<url>https://repository.apache.org/content/repositories/snapshots/</url>
			<releases>
				<enabled>false</enabled>
			</releases>
			<snapshots>
				<enabled>true</enabled>
			</snapshots>
		</repository>
		<repository>
			<id>maven_central</id>
			<name>Maven Central</name>
			<url>https://repo.maven.apache.org/maven2/</url>
		</repository>
	</repositories>

	<dependencies>
		<!--  start  vulnerable dependencies-->
		<dependency>
			<groupId>com.fasterxml.jackson.core</groupId>
			<artifactId>jackson-databind</artifactId>
			<version>2.17.0</version>
		</dependency>
		<dependency>
			<groupId>com.fasterxml.jackson.dataformat</groupId>
			<artifactId>jackson-dataformat-cbor</artifactId>
			<version>2.17.1</version>
		</dependency>
		<dependency>
			<groupId>com.fasterxml.jackson.core</groupId>
			<artifactId>jackson-annotations</artifactId>
			<version>2.17.2</version>
		</dependency>

		<dependency>
			<groupId>com.fasterxml.jackson.datatype</groupId>
			<artifactId>jackson-datatype-jsr310</artifactId>
			<version>2.17.2</version>
		</dependency>

		<dependency>
			<groupId>com.google.guava</groupId>
			<artifactId>guava</artifactId>
			<version>33.2.1-jre</version>
		</dependency>
		<dependency>
			<groupId>org.scala-lang</groupId>
			<artifactId>scala-library</artifactId>
			<version>2.13.14</version>
		</dependency>
		<dependency>
			<groupId>org.xerial.snappy</groupId>
			<artifactId>snappy-java</artifactId>
			<version>1.1.10.5</version>
		</dependency>
		<dependency>
			<groupId>io.netty</groupId>
			<artifactId>netty-handler</artifactId>
			<version>4.1.118.Final</version>
		</dependency>
		<dependency>
			<groupId>io.netty</groupId>
			<artifactId>netty-codec-http2</artifactId>
			<version>4.1.118.Final</version>
		</dependency>

		<dependency>
			<groupId>org.apache.zookeeper</groupId>
			<artifactId>zookeeper</artifactId>
			<version>3.9.3</version>
		</dependency>
		<dependency>
			<groupId>ch.qos.logback</groupId>
			<artifactId>logback-core</artifactId>
			<version>1.5.13</version>
		</dependency>
		<dependency>
			<groupId>ch.qos.logback</groupId>
			<artifactId>logback-classic</artifactId>
			<version>1.5.6</version>
		</dependency>

		<dependency>
			<groupId>org.codehaus.jettison</groupId>
			<artifactId>jettison</artifactId>
			<version>1.5.4</version>
		</dependency>

		<dependency>
		<groupId>org.apache.commons</groupId>
		<artifactId>commons-lang3</artifactId>
		<version>3.18.0</version>
		</dependency>

		<dependency>
		<groupId>commons-beanutils</groupId>
		<artifactId>commons-beanutils</artifactId>
		<version>1.11.0</version>
		</dependency>

		<!--  end  vulnerable dependencies-->
		<!-- Required for kinesis Analytics. -->
		<dependency>
			<groupId>com.amazonaws</groupId>
			<artifactId>aws-kinesisanalytics-runtime</artifactId>
			<version>${kda.runtime.version}</version>
			<scope>provided</scope>
		</dependency>
		<dependency>
			<groupId>com.amazonaws</groupId>
			<artifactId>aws-kinesisanalytics-flink</artifactId>
			<version>${kda.connectors.version}</version>
		</dependency>
		<dependency>
			<groupId>com.esotericsoftware</groupId>
			<artifactId>minlog</artifactId>
			<version>1.3.0</version>
		</dependency>

		<!-- This dependency is provided, because it should not be packaged into the JAR file. -->
		<dependency>
			<groupId>org.apache.flink</groupId>
			<artifactId>flink-java</artifactId>
			<version>${flink.version}</version>
			<scope>provided</scope>
		</dependency>
		<dependency>
			<groupId>org.apache.flink</groupId>
			<artifactId>flink-streaming-java</artifactId>
			<version>${flink.version}</version>
			<scope>provided</scope>
		</dependency>
		<dependency>
			<groupId>org.apache.flink</groupId>
			<artifactId>flink-clients</artifactId>
			<version>${flink.version}</version>
			<scope>provided</scope>
		</dependency>

		<!-- Add connector dependencies here. They must be in the default scope (compile). -->
		<!-- Required for kafka deserializer. -->
		<dependency>
			<groupId>org.apache.flink</groupId>
			<artifactId>flink-connector-kafka</artifactId>
			<version>${flink-kafka.version}</version>
		</dependency>
		<dependency>
			<groupId>org.apache.flink</groupId>
			<artifactId>flink-connector-base</artifactId>
			<version>${flink.version}</version>
		</dependency>
		<dependency>
			<groupId>software.amazon.msk</groupId>
			<artifactId>aws-msk-iam-auth</artifactId>
			<version>${aws-msk-iam-auth.version}</version>
		</dependency>
		<dependency>
			<groupId>org.apache.kafka</groupId>
			<artifactId>kafka-clients</artifactId>
			<version>${kafka.version}</version>
		</dependency>


		<dependency>
			<groupId>org.projectlombok</groupId>
			<artifactId>lombok</artifactId>
			<version>${lombok.version}</version>
			<scope>compile</scope>
		</dependency>
		<dependency>
			<groupId>com.alibaba.fastjson2</groupId>
			<artifactId>fastjson2</artifactId>
			<version>${fastjson2.version}</version>
		</dependency>

		<!-- Following dependencies are required for Protobuf -->
		<dependency>
			<groupId>com.twitter</groupId>
			<artifactId>chill-protobuf</artifactId>
			<version>0.10.0</version>
			<!-- exclusions for dependency conversion -->
			<exclusions>
				<exclusion>
					<groupId>com.esotericsoftware.kryo</groupId>
					<artifactId>kryo</artifactId>
				</exclusion>
			</exclusions>
		</dependency>

		<!-- We need protobuf for chill-protobuf -->
		<dependency>
			<groupId>com.google.protobuf</groupId>
			<artifactId>protobuf-java</artifactId>
			<version>${protobuf.java.version}</version>
		</dependency>
		<dependency>
			<groupId>com.google.protobuf</groupId>
			<artifactId>protobuf-java-util</artifactId>
			<version>${protobuf.java.version}</version>
		</dependency>
		<!-- Add logging framework, to produce console output when running in the IDE. -->
		<!-- These dependencies are excluded from the application JAR by default. -->
		<dependency>
			<groupId>org.apache.logging.log4j</groupId>
			<artifactId>log4j-slf4j-impl</artifactId>
			<version>${log4j.version}</version>
			<scope>runtime</scope>
		</dependency>

		<dependency>
			<groupId>org.apache.logging.log4j</groupId>
			<artifactId>log4j-api</artifactId>
			<version>${log4j.version}</version>
			<scope>runtime</scope>
		</dependency>

		<dependency>
			<groupId>org.apache.logging.log4j</groupId>
			<artifactId>log4j-core</artifactId>
			<version>${log4j.version}</version>
			<scope>runtime</scope>
		</dependency>

		<dependency>
			<groupId>commons-io</groupId>
			<artifactId>commons-io</artifactId>
			<version>2.17.0</version>
		</dependency>
		<!--		 Testing dependencies-->
		<dependency>
			<groupId>org.apache.flink</groupId>
			<artifactId>flink-test-utils</artifactId>
			<version>${flink.version}</version>
			<scope>test</scope>
			<exclusions>
				<exclusion>
					<groupId>log4j</groupId>
					<artifactId>log4j</artifactId>
				</exclusion>
			</exclusions>
		</dependency>
		<dependency>
			<groupId>org.apache.flink</groupId>
			<artifactId>flink-streaming-java</artifactId>
			<version>${flink.version}</version>
			<classifier>tests</classifier>
			<scope>test</scope>
			<exclusions>
				<exclusion>
					<groupId>commons-collections</groupId>
					<artifactId>commons-collections</artifactId>
				</exclusion>
			</exclusions>
		</dependency>
		<dependency>
			<groupId>net.mguenther.kafka</groupId>
			<artifactId>kafka-junit</artifactId>
			<version>${kafka.junit.version}</version>
			<scope>test</scope>
			<exclusions>
				<exclusion>
					<groupId>ch.qos.reload4j</groupId>
					<artifactId>reload4j</artifactId>
				</exclusion>
				<exclusion>
					<groupId>log4j</groupId>
					<artifactId>log4j</artifactId>
				</exclusion>
				<exclusion>
					<groupId>org.scala-lang</groupId>
					<artifactId>scala-library</artifactId>
				</exclusion>
				<exclusion>
					<groupId>org.eclipse.jetty</groupId>
					<artifactId>*</artifactId>
				</exclusion>
				<exclusion>
					<groupId>org.bitbucket.b_c</groupId>
					<artifactId>jose4j</artifactId>
				</exclusion>
			</exclusions>
		</dependency>
		<dependency>
			<groupId>org.junit.jupiter</groupId>
			<artifactId>junit-jupiter</artifactId>
			<version>${junit-jupiter.version}</version>
			<scope>test</scope>
		</dependency>
		<dependency>
			<groupId>org.testcontainers</groupId>
			<artifactId>testcontainers</artifactId>
			<version>${testcontainers.version}</version>
			<scope>test</scope>
		</dependency>
		<dependency>
			<groupId>org.testcontainers</groupId>
			<artifactId>junit-jupiter</artifactId>
			<version>${testcontainers.version}</version>
			<scope>test</scope>
		</dependency>
		<dependency>
			<groupId>org.apache.commons</groupId>
			<artifactId>commons-compress</artifactId>
			<version>1.26.1</version>
		</dependency>
	</dependencies>

	<build>
		<plugins>
			<!-- Extract protobuf classes from proto file -->
			<plugin>
				<groupId>com.github.os72</groupId>
				<artifactId>protoc-jar-maven-plugin</artifactId>
				<version>${protobuf.maven.plugin.version}</version>
				<executions>
					<execution>
						<phase>generate-sources</phase>
						<goals>
							<goal>run</goal>
						</goals>
						<configuration>
							<addProtoSources>all</addProtoSources>
							<includeMavenTypes>direct</includeMavenTypes>
							<protocVersion>${protobuf.java.version}</protocVersion>
							<inputDirectories>
								<include>src/main/resources/proto</include>
							</inputDirectories>
							<outputTargets>
								<outputTarget>
									<type>java</type>
									<addSources>all</addSources>
									<outputDirectory>src/main/java/proto</outputDirectory>
								</outputTarget>
								<outputTarget>
									<type>descriptor</type>
									<addSources>all</addSources>
									<outputDirectory>src/main/resources/protoDescriptors</outputDirectory>
								</outputTarget>
							</outputTargets>
						</configuration>
					</execution>
				</executions>
			</plugin>
			<!-- Java Compiler -->
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-compiler-plugin</artifactId>
				<version>${maven-compiler-plugin.version}</version>
				<configuration>
					<source>11</source>
					<target>11</target>
					<annotationProcessorPaths>
						<path>
							<groupId>org.projectlombok</groupId>
							<artifactId>lombok</artifactId>
							<version>${lombok.version}</version>
						</path>
					</annotationProcessorPaths>
				</configuration>
			</plugin>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-surefire-plugin</artifactId>
				<version>2.22.2</version>
			</plugin>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-failsafe-plugin</artifactId>
				<version>2.22.2</version>
			</plugin>
			<plugin>
				<groupId>org.jacoco</groupId>
				<artifactId>jacoco-maven-plugin</artifactId>
				<version>${maven.jacoco.version}</version>
				<executions>
					<execution>
						<id>prepare-unit-test</id>
						<phase>initialize</phase>
						<goals>
							<goal>prepare-agent</goal>
						</goals>
					</execution>
					<execution>
						<id>generate-report</id>
						<phase>verify</phase>
						<goals>
							<goal>report</goal>
						</goals>
					</execution>
				</executions>
				<configuration>
					<excludes>
						<exclude>**/gateway_service/Envelope.java</exclude>
						<exclude>**/ngtp_adpt_service/NgtpAdapterService.java</exclude>
						<exclude>**/someip_adpt_service/SomeipAdapterService.java</exclude>
						<exclude>gateway_service/Envelope.java</exclude>
						<exclude>ngtp_adpt_service/NgtpAdapterService.java</exclude>
						<exclude>someip_adpt_service/SomeipAdapterService.java</exclude>
					</excludes>
				</configuration>
			</plugin>
			<!-- We use the maven-shade plugin to create a fat jar that contains all necessary dependencies. -->
			<!-- Change the value of <mainClass>...</mainClass> if your program entry point changes. -->
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-shade-plugin</artifactId>
				<version>${maven-shade-plugin.version}</version>
				<executions>
					<!-- Run shade goal on package phase -->
					<execution>
						<phase>package</phase>
						<goals>
							<goal>shade</goal>
						</goals>
						<configuration>
							<artifactSet>
								<excludes>
									<exclude>org.apache.flink:force-shading</exclude>
									<exclude>com.google.code.findbugs:jsr305</exclude>
									<exclude>org.slf4j:*</exclude>
									<exclude>org.apache.logging.log4j:*</exclude>
								</excludes>
							</artifactSet>
							<createDependencyReducedPom>false</createDependencyReducedPom>
							<filters>
								<filter>
									<!-- Do not copy the signatures in the META-INF folder.
                                    Otherwise, this might cause SecurityExceptions when using the JAR. -->
									<artifact>*:*</artifact>
									<excludes>
										<exclude>META-INF/**</exclude>
										<exclude>META-INF/*.SF</exclude>
										<exclude>META-INF/*.DSA</exclude>
										<exclude>META-INF/*.RSA</exclude>
									</excludes>
								</filter>
							</filters>
							<transformers>
								<transformer implementation="org.apache.maven.plugins.shade.resource.ManifestResourceTransformer">
								<mainClass>com.jaguarlandrover.vcdp.DataEnginePreprocessor</mainClass>
								</transformer>
							</transformers>
						</configuration>
					</execution>
				</executions>
			</plugin>
		</plugins>

		<pluginManagement>
			<plugins>
			</plugins>
		</pluginManagement>

	</build>
	<profiles>
		<profile>
			<id>add-dependencies-for-IDEA</id>
			<activation>
				<property>
					<name>idea.version</name>
				</property>
			</activation>
			<dependencies>
				<dependency>
					<groupId>org.apache.flink</groupId>
					<artifactId>flink-streaming-java</artifactId>
					<version>${flink.version}</version>
					<scope>compile</scope>
				</dependency>
				<dependency>
					<groupId>org.apache.flink</groupId>
					<artifactId>flink-java</artifactId>
					<version>${flink.version}</version>
					<scope>compile</scope>
				</dependency>
				<dependency>
					<groupId>org.apache.flink</groupId>
					<artifactId>flink-clients</artifactId>
					<version>${flink.version}</version>
					<scope>compile</scope>
				</dependency>
				<dependency>
					<groupId>org.apache.flink</groupId>
					<artifactId>flink-runtime-web</artifactId>
					<version>${flink.version}</version>
					<scope>compile</scope>
				</dependency>
			</dependencies>
		</profile>
	</profiles>
</project>
