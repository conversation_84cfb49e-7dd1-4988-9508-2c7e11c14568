workflow:
  rules:
    - if: ($region == "eu-west-2" && $CI_COMMIT_REF_NAME =~ /^fc/)
      variables:
        tagref: "vcdp-dev"
        TF_BUCKET_RELEASES: "vcdp-data-engine-releases-235901252304"
        acc_id: "235901252304"
    - if: ($region == "eu-west-2" && $CI_COMMIT_REF_NAME =~ /^rc/)
      variables:
        tagref: "vcdp-pre-prod"
        TF_BUCKET_RELEASES: "vcdp-data-engine-releases-510773147453"
        acc_id: "510773147453"
    - if: ($region == "eu-west-2" && $CI_COMMIT_REF_NAME == $CI_DEFAULT_BRANCH)
      variables:
        tagref: "vcdp-prod"
        TF_BUCKET_RELEASES: "vcdp-data-engine-releases-010359529471"
        acc_id: "010359529471"
    - if: ($region == "cn-northwest-1" && ($CI_COMMIT_REF_NAME =~ /^fc/ || $triggering_branch =~ /^fc/))
      variables:
        tagref: "cn-dev"
        TF_BUCKET_RELEASES: "vcdp-data-engine-releases-069034308707"
        acc_id: "069034308707"
    - if: ($region == "cn-northwest-1" && ($CI_COMMIT_REF_NAME =~ /^rc/ || $triggering_branch =~ /^rc/))
      variables:
        tagref: "cn-preprod"
        TF_BUCKET_RELEASES: "vcdp-data-engine-releases-075868343702"
        acc_id: "075868343702"
    - if: ($region == "cn-northwest-1" && ($CI_COMMIT_REF_NAME == $CI_DEFAULT_BRANCH || $triggering_branch == $CI_DEFAULT_BRANCH))
      variables:
        tagref: "cn-prod"
        TF_BUCKET_RELEASES: "vcdp-data-engine-releases-076093312952"
        acc_id: "076093312952"
